{"version": 3, "sources": ["lib/prelude.js", "../node_modules/@protobufjs/aspromise/index.js", "../node_modules/@protobufjs/base64/index.js", "../node_modules/@protobufjs/codegen/index.js", "../node_modules/@protobufjs/eventemitter/index.js", "../node_modules/@protobufjs/fetch/index.js", "../node_modules/@protobufjs/float/index.js", "../node_modules/@protobufjs/inquire/index.js", "../node_modules/@protobufjs/path/index.js", "../node_modules/@protobufjs/pool/index.js", "../node_modules/@protobufjs/utf8/index.js", "../src/common.js", "../src/converter.js", "../src/decoder.js", "../src/encoder.js", "../src/enum.js", "../src/field.js", "../src/index-light.js", "../src/index-minimal.js", "../src/index", "../src/mapfield.js", "../src/message.js", "../src/method.js", "../src/namespace.js", "../src/object.js", "../src/oneof.js", "../src/parse.js", "../src/reader.js", "../src/reader_buffer.js", "../src/root.js", "../src/roots.js", "../src/rpc.js", "../src/rpc/service.js", "../src/service.js", "../src/tokenize.js", "../src/type.js", "../src/types.js", "../src/util.js", "../src/util/longbits.js", "../src/util/minimal.js", "../src/verifier.js", "../src/wrappers.js", "../src/writer.js", "../src/writer_buffer.js"], "names": ["undefined", "modules", "cache", "entries", "protobuf", "$require", "name", "$module", "call", "exports", "util", "global", "define", "amd", "<PERSON>", "isLong", "configure", "module", "1", "require", "fn", "ctx", "params", "Array", "arguments", "length", "offset", "index", "pending", "Promise", "resolve", "reject", "err", "apply", "base64", "string", "p", "n", "Math", "ceil", "b64", "s64", "i", "encode", "buffer", "start", "end", "t", "parts", "chunk", "j", "b", "push", "String", "fromCharCode", "slice", "join", "invalidEncoding", "decode", "c", "charCodeAt", "Error", "test", "codegen", "functionParams", "functionName", "body", "Codegen", "formatStringOrScope", "source", "toString", "verbose", "console", "log", "scopeKeys", "Object", "keys", "scopeParams", "scopeValues", "scopeOffset", "Function", "formatParams", "formatOffset", "replace", "$0", "$1", "value", "Number", "floor", "JSON", "stringify", "functionNameOverride", "EventEmitter", "this", "_listeners", "prototype", "on", "evt", "off", "listeners", "splice", "emit", "args", "fetch", "<PERSON><PERSON><PERSON><PERSON>", "fs", "inquire", "filename", "options", "callback", "xhr", "readFile", "contents", "XMLHttpRequest", "binary", "onreadystatechange", "readyState", "status", "response", "responseText", "Uint8Array", "overrideMimeType", "responseType", "open", "send", "factory", "writeFloat_ieee754", "writeUint", "val", "buf", "pos", "sign", "isNaN", "round", "exponent", "LN2", "pow", "readFloat_ieee754", "readUint", "uint", "mantissa", "NaN", "Infinity", "writeFloat_f32_cpy", "f32", "f8b", "writeFloat_f32_rev", "readFloat_f32_cpy", "readFloat_f32_rev", "f64", "le", "writeDouble_ieee754", "off0", "off1", "readDouble_ieee754", "lo", "hi", "writeDouble_f64_cpy", "writeDouble_f64_rev", "readDouble_f64_cpy", "readDouble_f64_rev", "Float32Array", "writeFloatLE", "writeFloatBE", "readFloatLE", "readFloatBE", "bind", "writeUintLE", "writeUintBE", "readUintLE", "readUintBE", "Float64Array", "writeDoubleLE", "writeDoubleBE", "readDoubleLE", "readDoubleBE", "moduleName", "mod", "eval", "e", "isAbsolute", "path", "normalize", "split", "absolute", "prefix", "shift", "originPath", "include<PERSON>ath", "alreadyNormalized", "alloc", "size", "SIZE", "MAX", "slab", "utf8", "len", "read", "write", "c1", "c2", "common", "commonRe", "json", "nested", "google", "Any", "fields", "type_url", "type", "id", "Duration", "timeType", "seconds", "nanos", "Timestamp", "Empty", "Struct", "keyType", "Value", "oneofs", "kind", "oneof", "nullValue", "numberValue", "stringValue", "boolValue", "structValue", "listValue", "Null<PERSON><PERSON>ue", "values", "NULL_VALUE", "ListValue", "rule", "DoubleValue", "FloatValue", "Int64Value", "UInt64Value", "Int32Value", "UInt32Value", "BoolValue", "StringValue", "BytesValue", "FieldMask", "paths", "get", "file", "Enum", "genValuePartial_fromObject", "gen", "field", "fieldIndex", "prop", "resolvedType", "repeated", "typeDefault", "fullName", "isUnsigned", "genValuePartial_toObject", "converter", "fromObject", "mtype", "fieldsArray", "safeProp", "map", "toObject", "sort", "compareFieldsById", "repeatedFields", "mapFields", "normalFields", "partOf", "arrayDefault", "valuesById", "long", "low", "high", "unsigned", "toNumber", "bytes", "hasKs2", "_fieldsArray", "indexOf", "filter", "group", "ref", "types", "defaults", "basic", "packed", "rfield", "required", "wireType", "mapKey", "genTypePartial", "optional", "ReflectionObject", "Namespace", "create", "constructor", "className", "comment", "comments", "TypeError", "reserved", "fromJSON", "enm", "toJSON", "toJSONOptions", "keepComments", "add", "isString", "isInteger", "isReservedId", "isReservedName", "allow_alias", "remove", "Field", "Type", "ruleRe", "extend", "isObject", "toLowerCase", "message", "defaultValue", "extensionField", "declaringField", "_packed", "defineProperty", "getOption", "setOption", "ifNotSet", "resolved", "parent", "lookupTypeOrEnum", "fromNumber", "freeze", "new<PERSON>uffer", "emptyObject", "emptyArray", "ctor", "d", "fieldId", "fieldType", "fieldRule", "decorateType", "decorateEnum", "fieldName", "default", "_configure", "Type_", "build", "load", "root", "Root", "loadSync", "encoder", "decoder", "verifier", "OneOf", "MapField", "Service", "Method", "Message", "wrappers", "Writer", "BufferWriter", "Reader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rpc", "roots", "tokenize", "parse", "resolvedKeyType", "fieldKeyType", "fieldValueType", "properties", "$type", "writer", "encodeDelimited", "reader", "decodeDelimited", "verify", "object", "requestType", "requestStream", "responseStream", "parsedOptions", "resolvedRequestType", "resolvedResponseType", "lookupType", "arrayToJSON", "array", "obj", "_nested<PERSON><PERSON>y", "clearCache", "namespace", "addJSON", "toArray", "nested<PERSON><PERSON><PERSON>", "nested<PERSON><PERSON>", "names", "methods", "getEnum", "prev", "setOptions", "onAdd", "onRemove", "isArray", "ptr", "part", "resolveAll", "lookup", "filterTypes", "parentAlreadyChecked", "found", "lookupEnum", "lookupService", "Service_", "Enum_", "defineProperties", "unshift", "_handleAdd", "_handleRemove", "setParsedOption", "propName", "opt", "newOpt", "find", "hasOwnProperty", "newValue", "setProperty", "Root_", "fieldNames", "addFieldsToParent", "oneofName", "oneOfGetter", "set", "oneOfSetter", "keepCase", "base10Re", "base10NegRe", "base16Re", "base16NegRe", "base8Re", "base8NegRe", "numberRe", "nameRe", "typeRefRe", "fqTypeRefRe", "pkg", "imports", "weakImports", "syntax", "token", "whichImports", "preferTrailingComment", "tn", "alternateCommentMode", "next", "peek", "skip", "cmnt", "head", "isProto3", "applyCase", "camelCase", "illegal", "insideTryCatch", "line", "readString", "readValue", "acceptTypeRef", "parseNumber", "substring", "parseInt", "parseFloat", "readRanges", "target", "acceptStrings", "parseId", "acceptNegative", "parse<PERSON><PERSON><PERSON>", "parseOption", "parseType", "ifBlock", "parseMapField", "valueType", "parseInlineOptions", "parseField", "extensions", "parseEnumValue", "dummy", "parseService", "service", "parseMethod", "commentText", "method", "parseExtension", "reference", "fnIf", "fnElse", "trailingLine", "parseGroup", "lcFirst", "ucFirst", "isCustom", "option", "optionValue", "substr", "parseOptionValue", "result", "prevValue", "concat", "simpleValue", "package", "LongBits", "indexOutOfRange", "write<PERSON><PERSON>th", "RangeError", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "create_array", "readLongVarint", "bits", "readFixed32_end", "readFixed64", "_slice", "subarray", "uint32", "int32", "sint32", "bool", "fixed32", "sfixed32", "float", "double", "skipType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_", "merge", "int64", "uint64", "sint64", "zzDecode", "fixed64", "sfixed64", "utf8Slice", "min", "deferred", "files", "SYNC", "<PERSON><PERSON><PERSON>", "self", "sync", "finish", "cb", "getBundledFileName", "idx", "lastIndexOf", "altname", "process", "parsed", "queued", "weak", "setTimeout", "readFileSync", "isNode", "exposeRe", "tryHandleExtension", "sisterField", "extendedType", "parse_", "common_", "rpcImpl", "requestDelimited", "responseDelimited", "rpcCall", "requestCtor", "responseCtor", "request", "endedByRPC", "_methodsArray", "inherited", "methodsArray", "rpcService", "methodName", "isReserved", "m", "q", "s", "delimRe", "stringDoubleRe", "stringSingleRe", "setCommentRe", "setCommentAltRe", "setCommentSplitRe", "whitespaceRe", "unescapeRe", "unescapeMap", "0", "r", "unescape", "str", "commentType", "commentLine", "commentLineEmpty", "commentIsLeading", "stack", "<PERSON><PERSON><PERSON><PERSON>", "subject", "char<PERSON>t", "setComment", "isLeading", "commentOffset", "lines", "trim", "isDoubleSlashCommentLine", "startOffset", "endOffset", "findEndOfLine", "lineText", "cursor", "re", "match", "lastIndex", "exec", "repeat", "curr", "isDoc", "isLeadingComment", "expected", "actual", "ret", "_fieldsById", "_oneofsArray", "_ctor", "fieldsById", "oneofsArray", "generateConstructor", "ctorProperties", "setup", "originalThis", "wrapper", "fork", "l<PERSON>im", "typeName", "bake", "o", "safePropBackslashRe", "key", "safePropQuoteRe", "camelCaseRe", "toUpperCase", "decorateEnumIndex", "a", "decorateRoot", "enumerable", "dst", "setProp", "zero", "zzEncode", "zeroHash", "from", "fromString", "toLong", "fromHash", "hash", "toHash", "mask", "part0", "part1", "part2", "src", "newError", "CustomError", "captureStackTrace", "pool", "versions", "node", "window", "isFinite", "isset", "isSet", "utf8Write", "_B<PERSON>er_from", "_Buffer_allocUnsafe", "sizeOrArray", "dcodeIO", "key2Re", "key32Re", "key64Re", "longToHash", "longFromHash", "fromBits", "ProtocolError", "fieldMap", "longs", "enums", "encoding", "allocUnsafe", "seenFirstField", "oneofProp", "invalid", "genVerifyKey", "genVerifyValue", "messageName", "Op", "noop", "State", "tail", "states", "writeByte", "VarintOp", "writeVarint64", "writeFixed32", "_push", "writeBytes", "reset", "BufferWriter_", "writeStringBuffer", "writeBytesBuffer", "copy", "byteLength"], "mappings": ";;;;;;CAAA,SAAAA,kBAAA,SAAAC,EAAAC,EAAAC,GAcA,IAAAC,EAPA,SAAAC,EAAAC,GACA,IAAAC,EAAAL,EAAAI,GAGA,OAFAC,GACAN,EAAAK,GAAA,GAAAE,KAAAD,EAAAL,EAAAI,GAAA,CAAAG,QAAA,IAAAJ,EAAAE,EAAAA,EAAAE,SACAF,EAAAE,QAGAJ,CAAAF,EAAA,IAGAC,EAAAM,KAAAC,OAAAP,SAAAA,EAGA,mBAAAQ,QAAAA,OAAAC,KACAD,OAAA,CAAA,QAAA,SAAAE,GAKA,OAJAA,GAAAA,EAAAC,SACAX,EAAAM,KAAAI,KAAAA,EACAV,EAAAY,aAEAZ,IAIA,iBAAAa,QAAAA,QAAAA,OAAAR,UACAQ,OAAAR,QAAAL,GA/BA,CAiCA,CAAAc,EAAA,CAAA,SAAAC,EAAAF,EAAAR,GChCAQ,EAAAR,QAmBA,SAAAW,EAAAC,GACA,IAAAC,EAAAC,MAAAC,UAAAC,OAAA,GACAC,EAAA,EACAC,EAAA,EACAC,GAAA,EACA,KAAAD,EAAAH,UAAAC,QACAH,EAAAI,KAAAF,UAAAG,KACA,OAAA,IAAAE,QAAA,SAAAC,EAAAC,GACAT,EAAAI,GAAA,SAAAM,GACA,GAAAJ,EAEA,GADAA,GAAA,EACAI,EACAD,EAAAC,OACA,CAGA,IAFA,IAAAV,EAAAC,MAAAC,UAAAC,OAAA,GACAC,EAAA,EACAA,EAAAJ,EAAAG,QACAH,EAAAI,KAAAF,UAAAE,GACAI,EAAAG,MAAA,KAAAX,KAIA,IACAF,EAAAa,MAAAZ,GAAA,KAAAC,GACA,MAAAU,GACAJ,IACAA,GAAA,EACAG,EAAAC,S,uBCjCAE,EAAAT,OAAA,SAAAU,GACA,IAAAC,EAAAD,EAAAV,OACA,IAAAW,EACA,OAAA,EAEA,IADA,IAAAC,EAAA,EACA,IAAAD,EAAA,GAAA,MAAAD,EAAAA,EAAAC,IAAAD,OACAE,EACA,OAAAC,KAAAC,KAAA,EAAAJ,EAAAV,QAAA,EAAAY,GAUA,IAxBA,IAkBAG,EAAAjB,MAAA,IAGAkB,EAAAlB,MAAA,KAGAmB,EAAA,EAAAA,EAAA,IACAD,EAAAD,EAAAE,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,EAAAA,EAAA,GAAA,IAAAA,IASAR,EAAAS,OAAA,SAAAC,EAAAC,EAAAC,GAMA,IALA,IAIAC,EAJAC,EAAA,KACAC,EAAA,GACAP,EAAA,EACAQ,EAAA,EAEAL,EAAAC,GAAA,CACA,IAAAK,EAAAP,EAAAC,KACA,OAAAK,GACA,KAAA,EACAD,EAAAP,KAAAF,EAAAW,GAAA,GACAJ,GAAA,EAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,KAAAF,EAAAO,EAAAI,GAAA,GACAJ,GAAA,GAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,KAAAF,EAAAO,EAAAI,GAAA,GACAF,EAAAP,KAAAF,EAAA,GAAAW,GACAD,EAAA,EAGA,KAAAR,KACAM,EAAAA,GAAA,IAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,IACAP,EAAA,GASA,OANAQ,IACAD,EAAAP,KAAAF,EAAAO,GACAE,EAAAP,KAAA,GACA,IAAAQ,IACAD,EAAAP,KAAA,KAEAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,KACAM,EAAAQ,KAAA,KAEAH,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,KAGA,IAAAe,EAAA,mBAUAvB,EAAAwB,OAAA,SAAAvB,EAAAS,EAAAlB,GAIA,IAHA,IAEAqB,EAFAF,EAAAnB,EACAwB,EAAA,EAEAR,EAAA,EAAAA,EAAAP,EAAAV,QAAA,CACA,IAAAkC,EAAAxB,EAAAyB,WAAAlB,KACA,GAAA,IAAAiB,GAAA,EAAAT,EACA,MACA,IAAAS,EAAAlB,EAAAkB,MAAA3D,GACA,MAAA6D,MAAAJ,GACA,OAAAP,GACA,KAAA,EACAH,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,KAAAqB,GAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,MAAA,GAAAqB,IAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,MAAA,EAAAqB,IAAA,EAAAY,EACAT,EAAA,GAIA,GAAA,IAAAA,EACA,MAAAW,MAAAJ,GACA,OAAA/B,EAAAmB,GAQAX,EAAA4B,KAAA,SAAA3B,GACA,MAAA,mEAAA2B,KAAA3B,K,uBC/HA,SAAA4B,EAAAC,EAAAC,GAGA,iBAAAD,IACAC,EAAAD,EACAA,EAAAhE,IAGA,IAAAkE,EAAA,GAYA,SAAAC,EAAAC,GAIA,GAAA,iBAAAA,EAAA,CACA,IAAAC,EAAAC,IAIA,GAHAP,EAAAQ,SACAC,QAAAC,IAAA,YAAAJ,GACAA,EAAA,UAAAA,EACAD,EAAA,CAKA,IAJA,IAAAM,EAAAC,OAAAC,KAAAR,GACAS,EAAAtD,MAAAmD,EAAAjD,OAAA,GACAqD,EAAAvD,MAAAmD,EAAAjD,QACAsD,EAAA,EACAA,EAAAL,EAAAjD,QACAoD,EAAAE,GAAAL,EAAAK,GACAD,EAAAC,GAAAX,EAAAM,EAAAK,MAGA,OADAF,EAAAE,GAAAV,EACAW,SAAA/C,MAAA,KAAA4C,GAAA5C,MAAA,KAAA6C,GAEA,OAAAE,SAAAX,EAAAW,GAMA,IAFA,IAAAC,EAAA1D,MAAAC,UAAAC,OAAA,GACAyD,EAAA,EACAA,EAAAD,EAAAxD,QACAwD,EAAAC,GAAA1D,YAAA0D,GAYA,GAXAA,EAAA,EACAd,EAAAA,EAAAe,QAAA,eAAA,SAAAC,EAAAC,GACA,IAAAC,EAAAL,EAAAC,KACA,OAAAG,GACA,IAAA,IAAA,IAAA,IAAA,MAAAhC,MAAAkC,GAAAD,GACA,IAAA,IAAA,MAAAjC,GAAAf,KAAAkD,MAAAF,GACA,IAAA,IAAA,OAAAG,KAAAC,UAAAJ,GACA,IAAA,IAAA,MAAAjC,GAAAiC,EAEA,MAAA,MAEAJ,IAAAD,EAAAxD,OACA,MAAAoC,MAAA,4BAEA,OADAK,EAAAd,KAAAgB,GACAD,EAGA,SAAAG,EAAAqB,GACA,MAAA,aAAAA,GAAA1B,GAAA,IAAA,KAAAD,GAAAA,EAAAR,KAAA,MAAA,IAAA,SAAAU,EAAAV,KAAA,QAAA,MAIA,OADAW,EAAAG,SAAAA,EACAH,GAhFAlD,EAAAR,QAAAsD,GAiGAQ,SAAA,G,uBCzFA,SAAAqB,IAOAC,KAAAC,EAAA,IAfA7E,EAAAR,QAAAmF,GAyBAG,UAAAC,GAAA,SAAAC,EAAA7E,EAAAC,GAKA,OAJAwE,KAAAC,EAAAG,KAAAJ,KAAAC,EAAAG,GAAA,KAAA7C,KAAA,CACAhC,GAAAA,EACAC,IAAAA,GAAAwE,OAEAA,MASAD,EAAAG,UAAAG,IAAA,SAAAD,EAAA7E,GACA,GAAA6E,IAAAjG,GACA6F,KAAAC,EAAA,QAEA,GAAA1E,IAAApB,GACA6F,KAAAC,EAAAG,GAAA,QAGA,IADA,IAAAE,EAAAN,KAAAC,EAAAG,GACAvD,EAAA,EAAAA,EAAAyD,EAAA1E,QACA0E,EAAAzD,GAAAtB,KAAAA,EACA+E,EAAAC,OAAA1D,EAAA,KAEAA,EAGA,OAAAmD,MASAD,EAAAG,UAAAM,KAAA,SAAAJ,GACA,IAAAE,EAAAN,KAAAC,EAAAG,GACA,GAAAE,EAAA,CAGA,IAFA,IAAAG,EAAA,GACA5D,EAAA,EACAA,EAAAlB,UAAAC,QACA6E,EAAAlD,KAAA5B,UAAAkB,MACA,IAAAA,EAAA,EAAAA,EAAAyD,EAAA1E,QACA0E,EAAAzD,GAAAtB,GAAAa,MAAAkE,EAAAzD,KAAArB,IAAAiF,GAEA,OAAAT,O,uBCzEA5E,EAAAR,QAAA8F,EAEA,IAAAC,EAAArF,EAAA,GAGAsF,EAFAtF,EAAA,EAEAuF,CAAA,MA2BA,SAAAH,EAAAI,EAAAC,EAAAC,GAOA,OAJAD,EAFA,mBAAAA,GACAC,EAAAD,EACA,IACAA,GACA,GAEAC,GAIAD,EAAAE,KAAAL,GAAAA,EAAAM,SACAN,EAAAM,SAAAJ,EAAA,SAAA3E,EAAAgF,GACA,OAAAhF,GAAA,oBAAAiF,eACAV,EAAAO,IAAAH,EAAAC,EAAAC,GACA7E,EACA6E,EAAA7E,GACA6E,EAAA,KAAAD,EAAAM,OAAAF,EAAAA,EAAA1C,SAAA,WAIAiC,EAAAO,IAAAH,EAAAC,EAAAC,GAbAL,EAAAD,EAAAV,KAAAc,EAAAC,GAqCAL,EAAAO,IAAA,SAAAH,EAAAC,EAAAC,GACA,IAAAC,EAAA,IAAAG,eACAH,EAAAK,mBAAA,WAEA,GAAA,IAAAL,EAAAM,WACA,OAAApH,GAKA,GAAA,IAAA8G,EAAAO,QAAA,MAAAP,EAAAO,OACA,OAAAR,EAAAhD,MAAA,UAAAiD,EAAAO,SAIA,GAAAT,EAAAM,OAAA,CAEA,KAAAtE,EADAkE,EAAAQ,UAGA,IAAA,IADA1E,EAAA,GACAF,EAAA,EAAAA,EAAAoE,EAAAS,aAAA9F,SAAAiB,EACAE,EAAAQ,KAAA,IAAA0D,EAAAS,aAAA3D,WAAAlB,IAEA,OAAAmE,EAAA,KAAA,oBAAAW,WAAA,IAAAA,WAAA5E,GAAAA,GAEA,OAAAiE,EAAA,KAAAC,EAAAS,eAGAX,EAAAM,SAEA,qBAAAJ,GACAA,EAAAW,iBAAA,sCACAX,EAAAY,aAAA,eAGAZ,EAAAa,KAAA,MAAAhB,GACAG,EAAAc,S,8BC1BA,SAAAC,EAAApH,GAsDA,SAAAqH,EAAAC,EAAAC,EAAAC,EAAAC,GACA,IAAAC,EAAAH,EAAA,EAAA,EAAA,EAIAD,EADA,KADAC,EADAG,GACAH,EACAA,GACA,EAAA,EAAAA,EAAA,EAAA,WACAI,MAAAJ,GACA,WACA,qBAAAA,GACAG,GAAA,GAAA,cAAA,EACAH,EAAA,uBACAG,GAAA,GAAA7F,KAAA+F,MAAAL,EAAA,yBAAA,GAIAG,GAAA,GAAA,KAFAG,EAAAhG,KAAAkD,MAAAlD,KAAAmC,IAAAuD,GAAA1F,KAAAiG,OAEA,GADA,QAAAjG,KAAA+F,MAAAL,EAAA1F,KAAAkG,IAAA,GAAAF,GAAA,YACA,EAVAL,EAAAC,GAiBA,SAAAO,EAAAC,EAAAT,EAAAC,GACAS,EAAAD,EAAAT,EAAAC,GACAC,EAAA,GAAAQ,GAAA,IAAA,EACAL,EAAAK,IAAA,GAAA,IACAC,GAAA,QACA,OAAA,KAAAN,EACAM,EACAC,IACAC,EAAAA,EAAAX,EACA,GAAAG,EACA,qBAAAH,EAAAS,EACAT,EAAA7F,KAAAkG,IAAA,EAAAF,EAAA,MAAA,QAAAM,GA9EA,SAAAG,EAAAf,EAAAC,EAAAC,GACAc,EAAA,GAAAhB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GAGA,SAAAC,EAAAlB,EAAAC,EAAAC,GACAc,EAAA,GAAAhB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GAQA,SAAAE,EAAAlB,EAAAC,GAKA,OAJAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAc,EAAA,GAGA,SAAAI,EAAAnB,EAAAC,GAKA,OAJAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAc,EAAA,GAxCA,IAEAA,EACAC,EA4FAI,EACAJ,EACAK,EA+DA,SAAAC,EAAAxB,EAAAyB,EAAAC,EAAAzB,EAAAC,EAAAC,GACA,IAaAU,EAbAT,EAAAH,EAAA,EAAA,EAAA,EAGA,KADAA,EADAG,GACAH,EACAA,IACAD,EAAA,EAAAE,EAAAC,EAAAsB,GACAzB,EAAA,EAAA,EAAAC,EAAA,EAAA,WAAAC,EAAAC,EAAAuB,IACArB,MAAAJ,IACAD,EAAA,EAAAE,EAAAC,EAAAsB,GACAzB,EAAA,WAAAE,EAAAC,EAAAuB,IACA,sBAAAzB,GACAD,EAAA,EAAAE,EAAAC,EAAAsB,GACAzB,GAAAI,GAAA,GAAA,cAAA,EAAAF,EAAAC,EAAAuB,IAGAzB,EAAA,wBAEAD,GADAa,EAAAZ,EAAA,UACA,EAAAC,EAAAC,EAAAsB,GACAzB,GAAAI,GAAA,GAAAS,EAAA,cAAA,EAAAX,EAAAC,EAAAuB,KAMA1B,EAAA,kBADAa,EAAAZ,EAAA1F,KAAAkG,IAAA,IADAF,EADA,QADAA,EAAAhG,KAAAkD,MAAAlD,KAAAmC,IAAAuD,GAAA1F,KAAAiG,MAEA,KACAD,OACA,EAAAL,EAAAC,EAAAsB,GACAzB,GAAAI,GAAA,GAAAG,EAAA,MAAA,GAAA,QAAAM,EAAA,WAAA,EAAAX,EAAAC,EAAAuB,IAQA,SAAAC,EAAAhB,EAAAc,EAAAC,EAAAxB,EAAAC,GACAyB,EAAAjB,EAAAT,EAAAC,EAAAsB,GACAI,EAAAlB,EAAAT,EAAAC,EAAAuB,GACAtB,EAAA,GAAAyB,GAAA,IAAA,EACAtB,EAAAsB,IAAA,GAAA,KACAhB,EAAA,YAAA,QAAAgB,GAAAD,EACA,OAAA,MAAArB,EACAM,EACAC,IACAC,EAAAA,EAAAX,EACA,GAAAG,EACA,OAAAH,EAAAS,EACAT,EAAA7F,KAAAkG,IAAA,EAAAF,EAAA,OAAAM,EAAA,kBA1GA,SAAAiB,EAAA7B,EAAAC,EAAAC,GACAmB,EAAA,GAAArB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GAGA,SAAAa,EAAA9B,EAAAC,EAAAC,GACAmB,EAAA,GAAArB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GAQA,SAAAc,EAAA9B,EAAAC,GASA,OARAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAmB,EAAA,GAGA,SAAAW,EAAA/B,EAAAC,GASA,OARAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAmB,EAAA,GAgEA,MArNA,oBAAAY,cAEAjB,EAAA,IAAAiB,aAAA,EAAA,IACAhB,EAAA,IAAAzB,WAAAwB,EAAApG,QACA0G,EAAA,MAAAL,EAAA,GAmBAxI,EAAAyJ,aAAAZ,EAAAP,EAAAG,EAEAzI,EAAA0J,aAAAb,EAAAJ,EAAAH,EAmBAtI,EAAA2J,YAAAd,EAAAH,EAAAC,EAEA3I,EAAA4J,YAAAf,EAAAF,EAAAD,IAwBA1I,EAAAyJ,aAAApC,EAAAwC,KAAA,KAAAC,GACA9J,EAAA0J,aAAArC,EAAAwC,KAAA,KAAAE,GAgBA/J,EAAA2J,YAAA3B,EAAA6B,KAAA,KAAAG,GACAhK,EAAA4J,YAAA5B,EAAA6B,KAAA,KAAAI,IAKA,oBAAAC,cAEAtB,EAAA,IAAAsB,aAAA,EAAA,IACA1B,EAAA,IAAAzB,WAAA6B,EAAAzG,QACA0G,EAAA,MAAAL,EAAA,GA2BAxI,EAAAmK,cAAAtB,EAAAO,EAAAC,EAEArJ,EAAAoK,cAAAvB,EAAAQ,EAAAD,EA2BApJ,EAAAqK,aAAAxB,EAAAS,EAAAC,EAEAvJ,EAAAsK,aAAAzB,EAAAU,EAAAD,IAmCAtJ,EAAAmK,cAAArB,EAAAe,KAAA,KAAAC,EAAA,EAAA,GACA9J,EAAAoK,cAAAtB,EAAAe,KAAA,KAAAE,EAAA,EAAA,GAiBA/J,EAAAqK,aAAApB,EAAAY,KAAA,KAAAG,EAAA,EAAA,GACAhK,EAAAsK,aAAArB,EAAAY,KAAA,KAAAI,EAAA,EAAA,IAIAjK,EAKA,SAAA8J,EAAAvC,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAGA,SAAAwC,EAAAxC,EAAAC,EAAAC,GACAD,EAAAC,GAAAF,IAAA,GACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAA,IAAAF,EAGA,SAAAyC,EAAAxC,EAAAC,GACA,OAAAD,EAAAC,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,MAAA,EAGA,SAAAwC,EAAAzC,EAAAC,GACA,OAAAD,EAAAC,IAAA,GACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,MAAA,EA3UAjH,EAAAR,QAAAoH,EAAAA,I,uBCOA,SAAAnB,EAAAsE,GACA,IACA,IAAAC,EAAAC,KAAA,UAAAA,CAAAF,GACA,GAAAC,IAAAA,EAAAxJ,QAAAkD,OAAAC,KAAAqG,GAAAxJ,QACA,OAAAwJ,EACA,MAAAE,IACA,OAAA,KAdAlK,EAAAR,QAAAiG,G,uBCMA,IAEA0E,EAMAC,EAAAD,WAAA,SAAAC,GACA,MAAA,eAAAvH,KAAAuH,IAGAC,EAMAD,EAAAC,UAAA,SAAAD,GAGA,IAAArI,GAFAqI,EAAAA,EAAAlG,QAAA,MAAA,KACAA,QAAA,UAAA,MACAoG,MAAA,KACAC,EAAAJ,EAAAC,GACAI,EAAA,GACAD,IACAC,EAAAzI,EAAA0I,QAAA,KACA,IAAA,IAAAhJ,EAAA,EAAAA,EAAAM,EAAAvB,QACA,OAAAuB,EAAAN,GACA,EAAAA,GAAA,OAAAM,EAAAN,EAAA,GACAM,EAAAoD,SAAA1D,EAAA,GACA8I,EACAxI,EAAAoD,OAAA1D,EAAA,KAEAA,EACA,MAAAM,EAAAN,GACAM,EAAAoD,OAAA1D,EAAA,KAEAA,EAEA,OAAA+I,EAAAzI,EAAAQ,KAAA,MAUA6H,EAAAvJ,QAAA,SAAA6J,EAAAC,EAAAC,GAGA,OAFAA,IACAD,EAAAN,EAAAM,KACAR,EAAAQ,KAIAD,GADAA,EADAE,EAEAF,EADAL,EAAAK,IACAxG,QAAA,iBAAA,KAAA1D,OAAA6J,EAAAK,EAAA,IAAAC,GAHAA,I,uBC3DA3K,EAAAR,QA6BA,SAAAqL,EAAAvI,EAAAwI,GACA,IAAAC,EAAAD,GAAA,KACAE,EAAAD,IAAA,EACAE,EAAA,KACAxK,EAAAsK,EACA,OAAA,SAAAD,GACA,GAAAA,EAAA,GAAAE,EAAAF,EACA,OAAAD,EAAAC,GACAC,EAAAtK,EAAAqK,IACAG,EAAAJ,EAAAE,GACAtK,EAAA,GAEAuG,EAAA1E,EAAA/C,KAAA0L,EAAAxK,EAAAA,GAAAqK,GAGA,OAFA,EAAArK,IACAA,EAAA,GAAA,EAAAA,IACAuG,K,wBC/BAkE,EAAA1K,OAAA,SAAAU,GAGA,IAFA,IACAwB,EADAyI,EAAA,EAEA1J,EAAA,EAAAA,EAAAP,EAAAV,SAAAiB,GACAiB,EAAAxB,EAAAyB,WAAAlB,IACA,IACA0J,GAAA,EACAzI,EAAA,KACAyI,GAAA,EACA,QAAA,MAAAzI,IAAA,QAAA,MAAAxB,EAAAyB,WAAAlB,EAAA,OACAA,EACA0J,GAAA,GAEAA,GAAA,EAEA,OAAAA,GAUAD,EAAAE,KAAA,SAAAzJ,EAAAC,EAAAC,GAEA,GADAA,EAAAD,EACA,EACA,MAAA,GAKA,IAJA,IAGAE,EAHAC,EAAA,KACAC,EAAA,GACAP,EAAA,EAEAG,EAAAC,IACAC,EAAAH,EAAAC,MACA,IACAI,EAAAP,KAAAK,EACA,IAAAA,GAAAA,EAAA,IACAE,EAAAP,MAAA,GAAAK,IAAA,EAAA,GAAAH,EAAAC,KACA,IAAAE,GAAAA,EAAA,KACAA,IAAA,EAAAA,IAAA,IAAA,GAAAH,EAAAC,OAAA,IAAA,GAAAD,EAAAC,OAAA,EAAA,GAAAD,EAAAC,MAAA,MACAI,EAAAP,KAAA,OAAAK,GAAA,IACAE,EAAAP,KAAA,OAAA,KAAAK,IAEAE,EAAAP,MAAA,GAAAK,IAAA,IAAA,GAAAH,EAAAC,OAAA,EAAA,GAAAD,EAAAC,KACA,KAAAH,KACAM,EAAAA,GAAA,IAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,IACAP,EAAA,GAGA,OAAAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,KACAM,EAAAQ,KAAA,KAEAH,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,KAUAyJ,EAAAG,MAAA,SAAAnK,EAAAS,EAAAlB,GAIA,IAHA,IACA6K,EACAC,EAFA3J,EAAAnB,EAGAgB,EAAA,EAAAA,EAAAP,EAAAV,SAAAiB,GACA6J,EAAApK,EAAAyB,WAAAlB,IACA,IACAE,EAAAlB,KAAA6K,GACAA,EAAA,KACA3J,EAAAlB,KAAA6K,GAAA,EAAA,KAEA,QAAA,MAAAA,IAAA,QAAA,OAAAC,EAAArK,EAAAyB,WAAAlB,EAAA,QAEAA,EACAE,EAAAlB,MAFA6K,EAAA,QAAA,KAAAA,IAAA,KAAA,KAAAC,KAEA,GAAA,IACA5J,EAAAlB,KAAA6K,GAAA,GAAA,GAAA,KAIA3J,EAAAlB,KAAA6K,GAAA,GAAA,IAHA3J,EAAAlB,KAAA6K,GAAA,EAAA,GAAA,KANA3J,EAAAlB,KAAA,GAAA6K,EAAA,KAcA,OAAA7K,EAAAmB,I,wBCtGA5B,EAAAR,QAAAgM,EAEA,IAAAC,EAAA,QAsBA,SAAAD,EAAAnM,EAAAqM,GACAD,EAAA5I,KAAAxD,KACAA,EAAA,mBAAAA,EAAA,SACAqM,EAAA,CAAAC,OAAA,CAAAC,OAAA,CAAAD,OAAA,CAAAxM,SAAA,CAAAwM,OAAAD,QAEAF,EAAAnM,GAAAqM,EAYAF,EAAA,MAAA,CAUAK,IAAA,CACAC,OAAA,CACAC,SAAA,CACAC,KAAA,SACAC,GAAA,GAEA5H,MAAA,CACA2H,KAAA,QACAC,GAAA,OAQAT,EAAA,WAAA,CAUAU,SAAAC,EAAA,CACAL,OAAA,CACAM,QAAA,CACAJ,KAAA,QACAC,GAAA,GAEAI,MAAA,CACAL,KAAA,QACAC,GAAA,OAMAT,EAAA,YAAA,CAUAc,UAAAH,IAGAX,EAAA,QAAA,CAOAe,MAAA,CACAT,OAAA,MAIAN,EAAA,SAAA,CASAgB,OAAA,CACAV,OAAA,CACAA,OAAA,CACAW,QAAA,SACAT,KAAA,QACAC,GAAA,KAkBAS,MAAA,CACAC,OAAA,CACAC,KAAA,CACAC,MAAA,CACA,YACA,cACA,cACA,YACA,cACA,eAIAf,OAAA,CACAgB,UAAA,CACAd,KAAA,YACAC,GAAA,GAEAc,YAAA,CACAf,KAAA,SACAC,GAAA,GAEAe,YAAA,CACAhB,KAAA,SACAC,GAAA,GAEAgB,UAAA,CACAjB,KAAA,OACAC,GAAA,GAEAiB,YAAA,CACAlB,KAAA,SACAC,GAAA,GAEAkB,UAAA,CACAnB,KAAA,YACAC,GAAA,KAKAmB,UAAA,CACAC,OAAA,CACAC,WAAA,IAWAC,UAAA,CACAzB,OAAA,CACAuB,OAAA,CACAG,KAAA,WACAxB,KAAA,QACAC,GAAA,OAMAT,EAAA,WAAA,CASAiC,YAAA,CACA3B,OAAA,CACAzH,MAAA,CACA2H,KAAA,SACAC,GAAA,KAYAyB,WAAA,CACA5B,OAAA,CACAzH,MAAA,CACA2H,KAAA,QACAC,GAAA,KAYA0B,WAAA,CACA7B,OAAA,CACAzH,MAAA,CACA2H,KAAA,QACAC,GAAA,KAYA2B,YAAA,CACA9B,OAAA,CACAzH,MAAA,CACA2H,KAAA,SACAC,GAAA,KAYA4B,WAAA,CACA/B,OAAA,CACAzH,MAAA,CACA2H,KAAA,QACAC,GAAA,KAYA6B,YAAA,CACAhC,OAAA,CACAzH,MAAA,CACA2H,KAAA,SACAC,GAAA,KAYA8B,UAAA,CACAjC,OAAA,CACAzH,MAAA,CACA2H,KAAA,OACAC,GAAA,KAYA+B,YAAA,CACAlC,OAAA,CACAzH,MAAA,CACA2H,KAAA,SACAC,GAAA,KAYAgC,WAAA,CACAnC,OAAA,CACAzH,MAAA,CACA2H,KAAA,QACAC,GAAA,OAMAT,EAAA,aAAA,CASA0C,UAAA,CACApC,OAAA,CACAqC,MAAA,CACAX,KAAA,WACAxB,KAAA,SACAC,GAAA,OAqBAT,EAAA4C,IAAA,SAAAC,GACA,OAAA7C,EAAA6C,IAAA,O,wBCxYA,IAEAC,EAAApO,EAAA,IACAT,EAAAS,EAAA,IAWA,SAAAqO,EAAAC,EAAAC,EAAAC,EAAAC,GAEA,GAAAF,EAAAG,aACA,GAAAH,EAAAG,wBAAAN,EAAA,CAAAE,EACA,eAAAG,GACA,IAAA,IAAAtB,EAAAoB,EAAAG,aAAAvB,OAAA1J,EAAAD,OAAAC,KAAA0J,GAAA5L,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACAgN,EAAAI,UAAAxB,EAAA1J,EAAAlC,MAAAgN,EAAAK,aAAAN,EACA,YACAA,EACA,UAAA7K,EAAAlC,GADA+M,CAEA,WAAAnB,EAAA1J,EAAAlC,IAFA+M,CAGA,SAAAG,EAAAtB,EAAA1J,EAAAlC,IAHA+M,CAIA,SACAA,EACA,UACAA,EACA,4BAAAG,EADAH,CAEA,sBAAAC,EAAAM,SAAA,oBAFAP,CAGA,gCAAAG,EAAAD,EAAAC,OACA,CACA,IAAAK,GAAA,EACA,OAAAP,EAAAzC,MACA,IAAA,SACA,IAAA,QAAAwC,EACA,kBAAAG,EAAAA,GACA,MACA,IAAA,SACA,IAAA,UAAAH,EACA,cAAAG,EAAAA,GACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,WAAAH,EACA,YAAAG,EAAAA,GACA,MACA,IAAA,SACAK,GAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAR,EACA,gBADAA,CAEA,6CAAAG,EAAAA,EAAAK,EAFAR,CAGA,iCAAAG,EAHAH,CAIA,uBAAAG,EAAAA,EAJAH,CAKA,iCAAAG,EALAH,CAMA,UAAAG,EAAAA,EANAH,CAOA,iCAAAG,EAPAH,CAQA,+DAAAG,EAAAA,EAAAA,EAAAK,EAAA,OAAA,IACA,MACA,IAAA,QAAAR,EACA,4BAAAG,EADAH,CAEA,wEAAAG,EAAAA,EAAAA,EAFAH,CAGA,sBAAAG,EAHAH,CAIA,UAAAG,EAAAA,GACA,MACA,IAAA,SAAAH,EACA,kBAAAG,EAAAA,GACA,MACA,IAAA,OAAAH,EACA,mBAAAG,EAAAA,IAOA,OAAAH,EAmEA,SAAAS,EAAAT,EAAAC,EAAAC,EAAAC,GAEA,GAAAF,EAAAG,aACAH,EAAAG,wBAAAN,EAAAE,EACA,iDAAAG,EAAAD,EAAAC,EAAAA,GACAH,EACA,gCAAAG,EAAAD,EAAAC,OACA,CACA,IAAAK,GAAA,EACA,OAAAP,EAAAzC,MACA,IAAA,SACA,IAAA,QAAAwC,EACA,6CAAAG,EAAAA,EAAAA,EAAAA,GACA,MACA,IAAA,SACAK,GAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAR,EACA,4BAAAG,EADAH,CAEA,uCAAAG,EAAAA,EAAAA,EAFAH,CAGA,OAHAA,CAIA,4IAAAG,EAAAA,EAAAA,EAAAA,EAAAK,EAAA,OAAA,GAAAL,GACA,MACA,IAAA,QAAAH,EACA,gHAAAG,EAAAA,EAAAA,EAAAA,EAAAA,GACA,MACA,QAAAH,EACA,UAAAG,EAAAA,IAIA,OAAAH,EA5FAU,EAAAC,WAAA,SAAAC,GAEA,IAAAtD,EAAAsD,EAAAC,YACAb,EAAA/O,EAAAqD,QAAA,CAAA,KAAAsM,EAAA/P,KAAA,cAAAI,CACA,6BADAA,CAEA,YACA,IAAAqM,EAAAtL,OAAA,OAAAgO,EACA,wBACAA,EACA,uBACA,IAAA,IAAA/M,EAAA,EAAAA,EAAAqK,EAAAtL,SAAAiB,EAAA,CACA,IAAAgN,EAAA3C,EAAArK,GAAAZ,UACA8N,EAAAlP,EAAA6P,SAAAb,EAAApP,MAGAoP,EAAAc,KAAAf,EACA,WAAAG,EADAH,CAEA,4BAAAG,EAFAH,CAGA,sBAAAC,EAAAM,SAAA,oBAHAP,CAIA,SAAAG,EAJAH,CAKA,oDAAAG,GACAJ,EAAAC,EAAAC,EAAAhN,EAAAkN,EAAA,UAAAJ,CACA,IADAA,CAEA,MAGAE,EAAAI,UAAAL,EACA,WAAAG,EADAH,CAEA,0BAAAG,EAFAH,CAGA,sBAAAC,EAAAM,SAAA,mBAHAP,CAIA,SAAAG,EAJAH,CAKA,iCAAAG,GACAJ,EAAAC,EAAAC,EAAAhN,EAAAkN,EAAA,MAAAJ,CACA,IADAA,CAEA,OAIAE,EAAAG,wBAAAN,GAAAE,EACA,iBAAAG,GACAJ,EAAAC,EAAAC,EAAAhN,EAAAkN,GACAF,EAAAG,wBAAAN,GAAAE,EACA,MAEA,OAAAA,EACA,aAwDAU,EAAAM,SAAA,SAAAJ,GAEA,IAAAtD,EAAAsD,EAAAC,YAAA/M,QAAAmN,KAAAhQ,EAAAiQ,mBACA,IAAA5D,EAAAtL,OACA,OAAAf,EAAAqD,SAAArD,CAAA,aAUA,IATA,IAAA+O,EAAA/O,EAAAqD,QAAA,CAAA,IAAA,KAAAsM,EAAA/P,KAAA,YAAAI,CACA,SADAA,CAEA,OAFAA,CAGA,YAEAkQ,EAAA,GACAC,EAAA,GACAC,EAAA,GACApO,EAAA,EACAA,EAAAqK,EAAAtL,SAAAiB,EACAqK,EAAArK,GAAAqO,SACAhE,EAAArK,GAAAZ,UAAAgO,SAAAc,EACA7D,EAAArK,GAAA8N,IAAAK,EACAC,GAAA1N,KAAA2J,EAAArK,IAEA,GAAAkO,EAAAnP,OAAA,CAEA,IAFAgO,EACA,6BACA/M,EAAA,EAAAA,EAAAkO,EAAAnP,SAAAiB,EAAA+M,EACA,SAAA/O,EAAA6P,SAAAK,EAAAlO,GAAApC,OACAmP,EACA,KAGA,GAAAoB,EAAApP,OAAA,CAEA,IAFAgO,EACA,8BACA/M,EAAA,EAAAA,EAAAmO,EAAApP,SAAAiB,EAAA+M,EACA,SAAA/O,EAAA6P,SAAAM,EAAAnO,GAAApC,OACAmP,EACA,KAGA,GAAAqB,EAAArP,OAAA,CAEA,IAFAgO,EACA,mBACA/M,EAAA,EAAAA,EAAAoO,EAAArP,SAAAiB,EAAA,CACA,IAWAsO,EAXAtB,EAAAoB,EAAApO,GACAkN,EAAAlP,EAAA6P,SAAAb,EAAApP,MACAoP,EAAAG,wBAAAN,EAAAE,EACA,6BAAAG,EAAAF,EAAAG,aAAAoB,WAAAvB,EAAAK,aAAAL,EAAAK,aACAL,EAAAwB,KAAAzB,EACA,iBADAA,CAEA,gCAAAC,EAAAK,YAAAoB,IAAAzB,EAAAK,YAAAqB,KAAA1B,EAAAK,YAAAsB,SAFA5B,CAGA,oEAAAG,EAHAH,CAIA,QAJAA,CAKA,6BAAAG,EAAAF,EAAAK,YAAAzL,WAAAoL,EAAAK,YAAAuB,YACA5B,EAAA6B,OACAP,EAAA,IAAAzP,MAAAwE,UAAAxC,MAAA/C,KAAAkP,EAAAK,aAAAvM,KAAA,KAAA,IACAiM,EACA,6BAAAG,EAAAvM,OAAAC,aAAArB,MAAAoB,OAAAqM,EAAAK,aADAN,CAEA,QAFAA,CAGA,SAAAG,EAAAoB,EAHAvB,CAIA,6CAAAG,EAAAA,EAJAH,CAKA,MACAA,EACA,SAAAG,EAAAF,EAAAK,aACAN,EACA,KAGA,IADA,IAAA+B,GAAA,EACA9O,EAAA,EAAAA,EAAAqK,EAAAtL,SAAAiB,EAAA,CACA,IAAAgN,EAAA3C,EAAArK,GACAf,EAAA0O,EAAAoB,EAAAC,QAAAhC,GACAE,EAAAlP,EAAA6P,SAAAb,EAAApP,MACAoP,EAAAc,KACAgB,IAAAA,GAAA,EAAA/B,EACA,YACAA,EACA,0CAAAG,EAAAA,EADAH,CAEA,SAAAG,EAFAH,CAGA,kCACAS,EAAAT,EAAAC,EAAA/N,EAAAiO,EAAA,WAAAM,CACA,MACAR,EAAAI,UAAAL,EACA,uBAAAG,EAAAA,EADAH,CAEA,SAAAG,EAFAH,CAGA,iCAAAG,GACAM,EAAAT,EAAAC,EAAA/N,EAAAiO,EAAA,MAAAM,CACA,OACAT,EACA,uCAAAG,EAAAF,EAAApP,MACA4P,EAAAT,EAAAC,EAAA/N,EAAAiO,GACAF,EAAAqB,QAAAtB,EACA,eADAA,CAEA,SAAA/O,EAAA6P,SAAAb,EAAAqB,OAAAzQ,MAAAoP,EAAApP,OAEAmP,EACA,KAEA,OAAAA,EACA,c,mCCjSAxO,EAAAR,QAeA,SAAA4P,GAEA,IAAAZ,EAAA/O,EAAAqD,QAAA,CAAA,IAAA,KAAAsM,EAAA/P,KAAA,UAAAI,CACA,6BADAA,CAEA,qBAFAA,CAGA,qDAAA2P,EAAAC,YAAAqB,OAAA,SAAAjC,GAAA,OAAAA,EAAAc,MAAA/O,OAAA,WAAA,IAHAf,CAIA,kBAJAA,CAKA,oBACA2P,EAAAuB,OAAAnC,EACA,gBADAA,CAEA,SACAA,EACA,kBAGA,IADA,IAAA/M,EAAA,EACAA,EAAA2N,EAAAC,YAAA7O,SAAAiB,EAAA,CACA,IAAAgN,EAAAW,EAAAoB,EAAA/O,GAAAZ,UACAmL,EAAAyC,EAAAG,wBAAAN,EAAA,QAAAG,EAAAzC,KACA4E,EAAA,IAAAnR,EAAA6P,SAAAb,EAAApP,MAAAmP,EACA,WAAAC,EAAAxC,IAGAwC,EAAAc,KAAAf,EACA,4BAAAoC,EADApC,CAEA,QAAAoC,EAFApC,CAGA,6BAEAqC,EAAAC,SAAArC,EAAAhC,WAAA1N,GAAAyP,EACA,OAAAqC,EAAAC,SAAArC,EAAAhC,UACA+B,EACA,UAEAqC,EAAAC,SAAA9E,KAAAjN,GAAAyP,EACA,WAAAqC,EAAAC,SAAA9E,IACAwC,EACA,cAEAA,EACA,mBADAA,CAEA,sBAFAA,CAGA,oBAHAA,CAIA,0BAAAC,EAAAhC,QAJA+B,CAKA,WAEAqC,EAAAE,MAAA/E,KAAAjN,GAAAyP,EACA,uCAAA/M,GACA+M,EACA,eAAAxC,GAEAwC,EACA,QADAA,CAEA,WAFAA,CAGA,qBAHAA,CAIA,QAJAA,CAKA,IALAA,CAMA,KAEAqC,EAAAZ,KAAAxB,EAAAhC,WAAA1N,GAAAyP,EACA,qDAAAoC,GACApC,EACA,cAAAoC,IAGAnC,EAAAI,UAAAL,EAEA,uBAAAoC,EAAAA,EAFApC,CAGA,QAAAoC,GAGAC,EAAAG,OAAAhF,KAAAjN,IAAAyP,EACA,iBADAA,CAEA,0BAFAA,CAGA,kBAHAA,CAIA,kBAAAoC,EAAA5E,EAJAwC,CAKA,SAGAqC,EAAAE,MAAA/E,KAAAjN,GAAAyP,EAAAC,EAAAG,aAAA+B,MACA,+BACA,0CAAAC,EAAAnP,GACA+M,EACA,kBAAAoC,EAAA5E,IAGA6E,EAAAE,MAAA/E,KAAAjN,GAAAyP,EAAAC,EAAAG,aAAA+B,MACA,yBACA,oCAAAC,EAAAnP,GACA+M,EACA,YAAAoC,EAAA5E,GACAwC,EACA,SAWA,IATAA,EACA,WADAA,CAEA,kBAFAA,CAGA,QAHAA,CAKA,IALAA,CAMA,KAGA/M,EAAA,EAAAA,EAAA2N,EAAAoB,EAAAhQ,SAAAiB,EAAA,CACA,IAAAwP,EAAA7B,EAAAoB,EAAA/O,GACAwP,EAAAC,UAAA1C,EACA,4BAAAyC,EAAA5R,KADAmP,CAEA,4CAjHA,qBAiHAyC,EAjHA5R,KAAA,KAoHA,OAAAmP,EACA,aA1HA,IAAAF,EAAApO,EAAA,IACA2Q,EAAA3Q,EAAA,IACAT,EAAAS,EAAA,K,yCCJAF,EAAAR,QA0BA,SAAA4P,GAWA,IATA,IAIAwB,EAJApC,EAAA/O,EAAAqD,QAAA,CAAA,IAAA,KAAAsM,EAAA/P,KAAA,UAAAI,CACA,SADAA,CAEA,qBAKAqM,EAAAsD,EAAAC,YAAA/M,QAAAmN,KAAAhQ,EAAAiQ,mBAEAjO,EAAA,EAAAA,EAAAqK,EAAAtL,SAAAiB,EAAA,CACA,IAAAgN,EAAA3C,EAAArK,GAAAZ,UACAH,EAAA0O,EAAAoB,EAAAC,QAAAhC,GACAzC,EAAAyC,EAAAG,wBAAAN,EAAA,QAAAG,EAAAzC,KACAmF,EAAAN,EAAAE,MAAA/E,GACA4E,EAAA,IAAAnR,EAAA6P,SAAAb,EAAApP,MAGAoP,EAAAc,KACAf,EACA,kDAAAoC,EAAAnC,EAAApP,KADAmP,CAEA,mDAAAoC,EAFApC,CAGA,4CAAAC,EAAAxC,IAAA,EAAA,KAAA,EAAA,EAAA4E,EAAAO,OAAA3C,EAAAhC,SAAAgC,EAAAhC,SACA0E,IAAApS,GAAAyP,EACA,oEAAA9N,EAAAkQ,GACApC,EACA,qCAAA,GAAA2C,EAAAnF,EAAA4E,GACApC,EACA,IADAA,CAEA,MAGAC,EAAAI,UAAAL,EACA,2BAAAoC,EAAAA,GAGAnC,EAAAuC,QAAAH,EAAAG,OAAAhF,KAAAjN,GAAAyP,EAEA,uBAAAC,EAAAxC,IAAA,EAAA,KAAA,EAFAuC,CAGA,+BAAAoC,EAHApC,CAIA,cAAAxC,EAAA4E,EAJApC,CAKA,eAGAA,EAEA,+BAAAoC,GACAO,IAAApS,GACAsS,EAAA7C,EAAAC,EAAA/N,EAAAkQ,EAAA,OACApC,EACA,0BAAAC,EAAAxC,IAAA,EAAAkF,KAAA,EAAAnF,EAAA4E,IAEApC,EACA,OAIAC,EAAA6C,UAAA9C,EACA,iDAAAoC,EAAAnC,EAAApP,MAEA8R,IAAApS,GACAsS,EAAA7C,EAAAC,EAAA/N,EAAAkQ,GACApC,EACA,uBAAAC,EAAAxC,IAAA,EAAAkF,KAAA,EAAAnF,EAAA4E,IAKA,OAAApC,EACA,aA9FA,IAAAF,EAAApO,EAAA,IACA2Q,EAAA3Q,EAAA,IACAT,EAAAS,EAAA,IAWA,SAAAmR,EAAA7C,EAAAC,EAAAC,EAAAkC,GACAnC,EAAAG,aAAA+B,MACAnC,EAAA,+CAAAE,EAAAkC,GAAAnC,EAAAxC,IAAA,EAAA,KAAA,GAAAwC,EAAAxC,IAAA,EAAA,KAAA,GACAuC,EAAA,oDAAAE,EAAAkC,GAAAnC,EAAAxC,IAAA,EAAA,KAAA,K,yCClBAjM,EAAAR,QAAA8O,EAGA,IAAAiD,EAAArR,EAAA,IAGAsR,KAFAlD,EAAAxJ,UAAApB,OAAA+N,OAAAF,EAAAzM,YAAA4M,YAAApD,GAAAqD,UAAA,OAEAzR,EAAA,KACAT,EAAAS,EAAA,IAaA,SAAAoO,EAAAjP,EAAAgO,EAAA1H,EAAAiM,EAAAC,GAGA,GAFAN,EAAAhS,KAAAqF,KAAAvF,EAAAsG,GAEA0H,GAAA,iBAAAA,EACA,MAAAyE,UAAA,4BAoCA,GA9BAlN,KAAAoL,WAAA,GAMApL,KAAAyI,OAAA3J,OAAA+N,OAAA7M,KAAAoL,YAMApL,KAAAgN,QAAAA,EAMAhN,KAAAiN,SAAAA,GAAA,GAMAjN,KAAAmN,SAAAhT,GAMAsO,EACA,IAAA,IAAA1J,EAAAD,OAAAC,KAAA0J,GAAA5L,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACA,iBAAA4L,EAAA1J,EAAAlC,MACAmD,KAAAoL,WAAApL,KAAAyI,OAAA1J,EAAAlC,IAAA4L,EAAA1J,EAAAlC,KAAAkC,EAAAlC,IAiBA6M,EAAA0D,SAAA,SAAA3S,EAAAqM,GACAuG,EAAA,IAAA3D,EAAAjP,EAAAqM,EAAA2B,OAAA3B,EAAA/F,QAAA+F,EAAAkG,QAAAlG,EAAAmG,UAEA,OADAI,EAAAF,SAAArG,EAAAqG,SACAE,GAQA3D,EAAAxJ,UAAAoN,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA3S,EAAA+P,SAAA,CACA,UAAA5K,KAAAe,QACA,SAAAf,KAAAyI,OACA,WAAAzI,KAAAmN,UAAAnN,KAAAmN,SAAAvR,OAAAoE,KAAAmN,SAAAhT,GACA,UAAAqT,EAAAxN,KAAAgN,QAAA7S,GACA,WAAAqT,EAAAxN,KAAAiN,SAAA9S,MAaAuP,EAAAxJ,UAAAuN,IAAA,SAAAhT,EAAA4M,EAAA2F,GAGA,IAAAnS,EAAA6S,SAAAjT,GACA,MAAAyS,UAAA,yBAEA,IAAArS,EAAA8S,UAAAtG,GACA,MAAA6F,UAAA,yBAEA,GAAAlN,KAAAyI,OAAAhO,KAAAN,GACA,MAAA6D,MAAA,mBAAAvD,EAAA,QAAAuF,MAEA,GAAAA,KAAA4N,aAAAvG,GACA,MAAArJ,MAAA,MAAAqJ,EAAA,mBAAArH,MAEA,GAAAA,KAAA6N,eAAApT,GACA,MAAAuD,MAAA,SAAAvD,EAAA,oBAAAuF,MAEA,GAAAA,KAAAoL,WAAA/D,KAAAlN,GAAA,CACA,IAAA6F,KAAAe,UAAAf,KAAAe,QAAA+M,YACA,MAAA9P,MAAA,gBAAAqJ,EAAA,OAAArH,MACAA,KAAAyI,OAAAhO,GAAA4M,OAEArH,KAAAoL,WAAApL,KAAAyI,OAAAhO,GAAA4M,GAAA5M,EAGA,OADAuF,KAAAiN,SAAAxS,GAAAuS,GAAA,KACAhN,MAUA0J,EAAAxJ,UAAA6N,OAAA,SAAAtT,GAEA,IAAAI,EAAA6S,SAAAjT,GACA,MAAAyS,UAAA,yBAEA,IAAA/K,EAAAnC,KAAAyI,OAAAhO,GACA,GAAA,MAAA0H,EACA,MAAAnE,MAAA,SAAAvD,EAAA,uBAAAuF,MAMA,cAJAA,KAAAoL,WAAAjJ,UACAnC,KAAAyI,OAAAhO,UACAuF,KAAAiN,SAAAxS,GAEAuF,MAQA0J,EAAAxJ,UAAA0N,aAAA,SAAAvG,GACA,OAAAuF,EAAAgB,aAAA5N,KAAAmN,SAAA9F,IAQAqC,EAAAxJ,UAAA2N,eAAA,SAAApT,GACA,OAAAmS,EAAAiB,eAAA7N,KAAAmN,SAAA1S,K,yCClLAW,EAAAR,QAAAoT,EAGA,IAOAC,EAPAtB,EAAArR,EAAA,IAGAoO,KAFAsE,EAAA9N,UAAApB,OAAA+N,OAAAF,EAAAzM,YAAA4M,YAAAkB,GAAAjB,UAAA,QAEAzR,EAAA,KACA2Q,EAAA3Q,EAAA,IACAT,EAAAS,EAAA,IAIA4S,EAAA,+BAyCA,SAAAF,EAAAvT,EAAA4M,EAAAD,EAAAwB,EAAAuF,EAAApN,EAAAiM,GAcA,GAZAnS,EAAAuT,SAAAxF,IACAoE,EAAAmB,EACApN,EAAA6H,EACAA,EAAAuF,EAAAhU,IACAU,EAAAuT,SAAAD,KACAnB,EAAAjM,EACAA,EAAAoN,EACAA,EAAAhU,IAGAwS,EAAAhS,KAAAqF,KAAAvF,EAAAsG,IAEAlG,EAAA8S,UAAAtG,IAAAA,EAAA,EACA,MAAA6F,UAAA,qCAEA,IAAArS,EAAA6S,SAAAtG,GACA,MAAA8F,UAAA,yBAEA,GAAAtE,IAAAzO,KAAA+T,EAAAjQ,KAAA2K,EAAAA,EAAAnK,WAAA4P,eACA,MAAAnB,UAAA,8BAEA,GAAAiB,IAAAhU,KAAAU,EAAA6S,SAAAS,GACA,MAAAjB,UAAA,2BASAlN,KAAA4I,MANAA,EADA,oBAAAA,EACA,WAMAA,IAAA,aAAAA,EAAAA,EAAAzO,GAMA6F,KAAAoH,KAAAA,EAMApH,KAAAqH,GAAAA,EAMArH,KAAAmO,OAAAA,GAAAhU,GAMA6F,KAAAsM,SAAA,aAAA1D,EAMA5I,KAAA0M,UAAA1M,KAAAsM,SAMAtM,KAAAiK,SAAA,aAAArB,EAMA5I,KAAA2K,KAAA,EAMA3K,KAAAsO,QAAA,KAMAtO,KAAAkL,OAAA,KAMAlL,KAAAkK,YAAA,KAMAlK,KAAAuO,aAAA,KAMAvO,KAAAqL,OAAAxQ,EAAAI,MAAAgR,EAAAZ,KAAAjE,KAAAjN,GAMA6F,KAAA0L,MAAA,UAAAtE,EAMApH,KAAAgK,aAAA,KAMAhK,KAAAwO,eAAA,KAMAxO,KAAAyO,eAAA,KAOAzO,KAAA0O,EAAA,KAMA1O,KAAAgN,QAAAA,EAhKAgB,EAAAZ,SAAA,SAAA3S,EAAAqM,GACA,OAAA,IAAAkH,EAAAvT,EAAAqM,EAAAO,GAAAP,EAAAM,KAAAN,EAAA8B,KAAA9B,EAAAqH,OAAArH,EAAA/F,QAAA+F,EAAAkG,UAwKAlO,OAAA6P,eAAAX,EAAA9N,UAAA,SAAA,CACAsJ,IAAA,WAIA,OAFA,OAAAxJ,KAAA0O,IACA1O,KAAA0O,GAAA,IAAA1O,KAAA4O,UAAA,WACA5O,KAAA0O,KAOAV,EAAA9N,UAAA2O,UAAA,SAAApU,EAAAgF,EAAAqP,GAGA,MAFA,WAAArU,IACAuF,KAAA0O,EAAA,MACA/B,EAAAzM,UAAA2O,UAAAlU,KAAAqF,KAAAvF,EAAAgF,EAAAqP,IAwBAd,EAAA9N,UAAAoN,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA3S,EAAA+P,SAAA,CACA,OAAA,aAAA5K,KAAA4I,MAAA5I,KAAA4I,MAAAzO,GACA,OAAA6F,KAAAoH,KACA,KAAApH,KAAAqH,GACA,SAAArH,KAAAmO,OACA,UAAAnO,KAAAe,QACA,UAAAyM,EAAAxN,KAAAgN,QAAA7S,MASA6T,EAAA9N,UAAAjE,QAAA,WAEA,OAAA+D,KAAA+O,SACA/O,OAEAA,KAAAkK,YAAA+B,EAAAC,SAAAlM,KAAAoH,SAAAjN,KACA6F,KAAAgK,cAAAhK,KAAAyO,gBAAAzO,MAAAgP,OAAAC,iBAAAjP,KAAAoH,MACApH,KAAAgK,wBAAAiE,EACAjO,KAAAkK,YAAA,KAEAlK,KAAAkK,YAAAlK,KAAAgK,aAAAvB,OAAA3J,OAAAC,KAAAiB,KAAAgK,aAAAvB,QAAA,KAIAzI,KAAAe,SAAA,MAAAf,KAAAe,QAAA,UACAf,KAAAkK,YAAAlK,KAAAe,QAAA,QACAf,KAAAgK,wBAAAN,GAAA,iBAAA1J,KAAAkK,cACAlK,KAAAkK,YAAAlK,KAAAgK,aAAAvB,OAAAzI,KAAAkK,eAIAlK,KAAAe,WACA,IAAAf,KAAAe,QAAAqL,SAAApM,KAAAe,QAAAqL,SAAAjS,KAAA6F,KAAAgK,cAAAhK,KAAAgK,wBAAAN,WACA1J,KAAAe,QAAAqL,OACAtN,OAAAC,KAAAiB,KAAAe,SAAAnF,SACAoE,KAAAe,QAAA5G,KAIA6F,KAAAqL,MACArL,KAAAkK,YAAArP,EAAAI,KAAAiU,WAAAlP,KAAAkK,YAAA,MAAAlK,KAAAoH,KAAA,IAAApH,KAGAlB,OAAAqQ,QACArQ,OAAAqQ,OAAAnP,KAAAkK,cAEAlK,KAAA0L,OAAA,iBAAA1L,KAAAkK,cAEArP,EAAAwB,OAAA4B,KAAA+B,KAAAkK,aACArP,EAAAwB,OAAAwB,OAAAmC,KAAAkK,YAAA9H,EAAAvH,EAAAuU,UAAAvU,EAAAwB,OAAAT,OAAAoE,KAAAkK,cAAA,GAEArP,EAAAyL,KAAAG,MAAAzG,KAAAkK,YAAA9H,EAAAvH,EAAAuU,UAAAvU,EAAAyL,KAAA1K,OAAAoE,KAAAkK,cAAA,GACAlK,KAAAkK,YAAA9H,GAIApC,KAAA2K,IACA3K,KAAAuO,aAAA1T,EAAAwU,YACArP,KAAAiK,SACAjK,KAAAuO,aAAA1T,EAAAyU,WAEAtP,KAAAuO,aAAAvO,KAAAkK,YAGAlK,KAAAgP,kBAAAf,IACAjO,KAAAgP,OAAAO,KAAArP,UAAAF,KAAAvF,MAAAuF,KAAAuO,cAEA5B,EAAAzM,UAAAjE,QAAAtB,KAAAqF,OA5BA,IAQAoC,GA2CA4L,EAAAwB,EAAA,SAAAC,EAAAC,EAAAC,EAAApB,GAUA,MAPA,mBAAAmB,EACAA,EAAA7U,EAAA+U,aAAAF,GAAAjV,KAGAiV,GAAA,iBAAAA,IACAA,EAAA7U,EAAAgV,aAAAH,GAAAjV,MAEA,SAAAyF,EAAA4P,GACAjV,EAAA+U,aAAA1P,EAAA4M,aACAW,IAAA,IAAAO,EAAA8B,EAAAL,EAAAC,EAAAC,EAAA,CAAAI,QAAAxB,OAkBAP,EAAAgC,EAAA,SAAAC,GACAhC,EAAAgC,I,+CCnXA,IAAA1V,EAAAa,EAAAR,QAAAU,EAAA,IAEAf,EAAA2V,MAAA,QAoDA3V,EAAA4V,KAjCA,SAAArP,EAAAsP,EAAApP,GAMA,OAHAoP,EAFA,mBAAAA,GACApP,EAAAoP,EACA,IAAA7V,EAAA8V,MACAD,GACA,IAAA7V,EAAA8V,MACAF,KAAArP,EAAAE,IA2CAzG,EAAA+V,SANA,SAAAxP,EAAAsP,GAGA,OADAA,EADAA,GACA,IAAA7V,EAAA8V,MACAC,SAAAxP,IAMAvG,EAAAgW,QAAAjV,EAAA,IACAf,EAAAiW,QAAAlV,EAAA,IACAf,EAAAkW,SAAAnV,EAAA,IACAf,EAAA+P,UAAAhP,EAAA,IAGAf,EAAAoS,iBAAArR,EAAA,IACAf,EAAAqS,UAAAtR,EAAA,IACAf,EAAA8V,KAAA/U,EAAA,IACAf,EAAAmP,KAAApO,EAAA,IACAf,EAAA0T,KAAA3S,EAAA,IACAf,EAAAyT,MAAA1S,EAAA,IACAf,EAAAmW,MAAApV,EAAA,IACAf,EAAAoW,SAAArV,EAAA,IACAf,EAAAqW,QAAAtV,EAAA,IACAf,EAAAsW,OAAAvV,EAAA,IAGAf,EAAAuW,QAAAxV,EAAA,IACAf,EAAAwW,SAAAzV,EAAA,IAGAf,EAAA0R,MAAA3Q,EAAA,IACAf,EAAAM,KAAAS,EAAA,IAGAf,EAAAoS,iBAAAqD,EAAAzV,EAAA8V,MACA9V,EAAAqS,UAAAoD,EAAAzV,EAAA0T,KAAA1T,EAAAqW,QAAArW,EAAAmP,MACAnP,EAAA8V,KAAAL,EAAAzV,EAAA0T,MACA1T,EAAAyT,MAAAgC,EAAAzV,EAAA0T,O,yICtGA,IAAA1T,EAAAK,EA2BA,SAAAO,IACAZ,EAAAM,KAAAmV,IACAzV,EAAAyW,OAAAhB,EAAAzV,EAAA0W,cACA1W,EAAA2W,OAAAlB,EAAAzV,EAAA4W,cAtBA5W,EAAA2V,MAAA,UAGA3V,EAAAyW,OAAA1V,EAAA,IACAf,EAAA0W,aAAA3V,EAAA,IACAf,EAAA2W,OAAA5V,EAAA,IACAf,EAAA4W,aAAA7V,EAAA,IAGAf,EAAAM,KAAAS,EAAA,IACAf,EAAA6W,IAAA9V,EAAA,IACAf,EAAA8W,MAAA/V,EAAA,IACAf,EAAAY,UAAAA,EAcAA,K,iEClCAZ,EAAAa,EAAAR,QAAAU,EAAA,IAEAf,EAAA2V,MAAA,OAGA3V,EAAA+W,SAAAhW,EAAA,IACAf,EAAAgX,MAAAjW,EAAA,IACAf,EAAAqM,OAAAtL,EAAA,IAGAf,EAAA8V,KAAAL,EAAAzV,EAAA0T,KAAA1T,EAAAgX,MAAAhX,EAAAqM,S,+CCVAxL,EAAAR,QAAA+V,EAGA,IAAA3C,EAAA1S,EAAA,IAGA2Q,KAFA0E,EAAAzQ,UAAApB,OAAA+N,OAAAmB,EAAA9N,YAAA4M,YAAA6D,GAAA5D,UAAA,WAEAzR,EAAA,KACAT,EAAAS,EAAA,IAcA,SAAAqV,EAAAlW,EAAA4M,EAAAQ,EAAAT,EAAArG,EAAAiM,GAIA,GAHAgB,EAAArT,KAAAqF,KAAAvF,EAAA4M,EAAAD,EAAAjN,GAAAA,GAAA4G,EAAAiM,IAGAnS,EAAA6S,SAAA7F,GACA,MAAAqF,UAAA,4BAMAlN,KAAA6H,QAAAA,EAMA7H,KAAAwR,gBAAA,KAGAxR,KAAA2K,KAAA,EAwBAgG,EAAAvD,SAAA,SAAA3S,EAAAqM,GACA,OAAA,IAAA6J,EAAAlW,EAAAqM,EAAAO,GAAAP,EAAAe,QAAAf,EAAAM,KAAAN,EAAA/F,QAAA+F,EAAAkG,UAQA2D,EAAAzQ,UAAAoN,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA3S,EAAA+P,SAAA,CACA,UAAA5K,KAAA6H,QACA,OAAA7H,KAAAoH,KACA,KAAApH,KAAAqH,GACA,SAAArH,KAAAmO,OACA,UAAAnO,KAAAe,QACA,UAAAyM,EAAAxN,KAAAgN,QAAA7S,MAOAwW,EAAAzQ,UAAAjE,QAAA,WACA,GAAA+D,KAAA+O,SACA,OAAA/O,KAGA,GAAAiM,EAAAO,OAAAxM,KAAA6H,WAAA1N,GACA,MAAA6D,MAAA,qBAAAgC,KAAA6H,SAEA,OAAAmG,EAAA9N,UAAAjE,QAAAtB,KAAAqF,OAaA2Q,EAAAnB,EAAA,SAAAC,EAAAgC,EAAAC,GAUA,MAPA,mBAAAA,EACAA,EAAA7W,EAAA+U,aAAA8B,GAAAjX,KAGAiX,GAAA,iBAAAA,IACAA,EAAA7W,EAAAgV,aAAA6B,GAAAjX,MAEA,SAAAyF,EAAA4P,GACAjV,EAAA+U,aAAA1P,EAAA4M,aACAW,IAAA,IAAAkD,EAAAb,EAAAL,EAAAgC,EAAAC,O,yCC1HAtW,EAAAR,QAAAkW,EAEA,IAAAjW,EAAAS,EAAA,IASA,SAAAwV,EAAAa,GAEA,GAAAA,EACA,IAAA,IAAA5S,EAAAD,OAAAC,KAAA4S,GAAA9U,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACAmD,KAAAjB,EAAAlC,IAAA8U,EAAA5S,EAAAlC,IA0BAiU,EAAAjE,OAAA,SAAA8E,GACA,OAAA3R,KAAA4R,MAAA/E,OAAA8E,IAWAb,EAAAhU,OAAA,SAAAwR,EAAAuD,GACA,OAAA7R,KAAA4R,MAAA9U,OAAAwR,EAAAuD,IAWAf,EAAAgB,gBAAA,SAAAxD,EAAAuD,GACA,OAAA7R,KAAA4R,MAAAE,gBAAAxD,EAAAuD,IAYAf,EAAAjT,OAAA,SAAAkU,GACA,OAAA/R,KAAA4R,MAAA/T,OAAAkU,IAYAjB,EAAAkB,gBAAA,SAAAD,GACA,OAAA/R,KAAA4R,MAAAI,gBAAAD,IAUAjB,EAAAmB,OAAA,SAAA3D,GACA,OAAAtO,KAAA4R,MAAAK,OAAA3D,IAUAwC,EAAAvG,WAAA,SAAA2H,GACA,OAAAlS,KAAA4R,MAAArH,WAAA2H,IAWApB,EAAAlG,SAAA,SAAA0D,EAAAvN,GACA,OAAAf,KAAA4R,MAAAhH,SAAA0D,EAAAvN,IAOA+P,EAAA5Q,UAAAoN,OAAA,WACA,OAAAtN,KAAA4R,MAAAhH,SAAA5K,KAAAnF,EAAA0S,iB,6BCtIAnS,EAAAR,QAAAiW,EAGA,IAAAlE,EAAArR,EAAA,IAGAT,KAFAgW,EAAA3Q,UAAApB,OAAA+N,OAAAF,EAAAzM,YAAA4M,YAAA+D,GAAA9D,UAAA,SAEAzR,EAAA,KAiBA,SAAAuV,EAAApW,EAAA2M,EAAA+K,EAAAtQ,EAAAuQ,EAAAC,EAAAtR,EAAAiM,EAAAsF,GAYA,GATAzX,EAAAuT,SAAAgE,IACArR,EAAAqR,EACAA,EAAAC,EAAAlY,IACAU,EAAAuT,SAAAiE,KACAtR,EAAAsR,EACAA,EAAAlY,IAIAiN,IAAAjN,KAAAU,EAAA6S,SAAAtG,GACA,MAAA8F,UAAA,yBAGA,IAAArS,EAAA6S,SAAAyE,GACA,MAAAjF,UAAA,gCAGA,IAAArS,EAAA6S,SAAA7L,GACA,MAAAqL,UAAA,iCAEAP,EAAAhS,KAAAqF,KAAAvF,EAAAsG,GAMAf,KAAAoH,KAAAA,GAAA,MAMApH,KAAAmS,YAAAA,EAMAnS,KAAAoS,gBAAAA,GAAAjY,GAMA6F,KAAA6B,aAAAA,EAMA7B,KAAAqS,iBAAAA,GAAAlY,GAMA6F,KAAAuS,oBAAA,KAMAvS,KAAAwS,qBAAA,KAMAxS,KAAAgN,QAAAA,EAKAhN,KAAAsS,cAAAA,EAuBAzB,EAAAzD,SAAA,SAAA3S,EAAAqM,GACA,OAAA,IAAA+J,EAAApW,EAAAqM,EAAAM,KAAAN,EAAAqL,YAAArL,EAAAjF,aAAAiF,EAAAsL,cAAAtL,EAAAuL,eAAAvL,EAAA/F,QAAA+F,EAAAkG,QAAAlG,EAAAwL,gBAQAzB,EAAA3Q,UAAAoN,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA3S,EAAA+P,SAAA,CACA,OAAA,QAAA5K,KAAAoH,MAAApH,KAAAoH,MAAAjN,GACA,cAAA6F,KAAAmS,YACA,gBAAAnS,KAAAoS,cACA,eAAApS,KAAA6B,aACA,iBAAA7B,KAAAqS,eACA,UAAArS,KAAAe,QACA,UAAAyM,EAAAxN,KAAAgN,QAAA7S,GACA,gBAAA6F,KAAAsS,iBAOAzB,EAAA3Q,UAAAjE,QAAA,WAGA,OAAA+D,KAAA+O,SACA/O,MAEAA,KAAAuS,oBAAAvS,KAAAgP,OAAAyD,WAAAzS,KAAAmS,aACAnS,KAAAwS,qBAAAxS,KAAAgP,OAAAyD,WAAAzS,KAAA6B,cAEA8K,EAAAzM,UAAAjE,QAAAtB,KAAAqF,S,mCC7JA5E,EAAAR,QAAAgS,EAGA,IAOAqB,EACA2C,EACAlH,EATAiD,EAAArR,EAAA,IAGA0S,KAFApB,EAAA1M,UAAApB,OAAA+N,OAAAF,EAAAzM,YAAA4M,YAAAF,GAAAG,UAAA,YAEAzR,EAAA,KACAoV,EAAApV,EAAA,IACAT,EAAAS,EAAA,IAoCA,SAAAoX,EAAAC,EAAApF,GACA,IAAAoF,IAAAA,EAAA/W,OACA,OAAAzB,GAEA,IADA,IAAAyY,EAAA,GACA/V,EAAA,EAAAA,EAAA8V,EAAA/W,SAAAiB,EACA+V,EAAAD,EAAA9V,GAAApC,MAAAkY,EAAA9V,GAAAyQ,OAAAC,GACA,OAAAqF,EA4CA,SAAAhG,EAAAnS,EAAAsG,GACA4L,EAAAhS,KAAAqF,KAAAvF,EAAAsG,GAMAf,KAAA+G,OAAA5M,GAOA6F,KAAA6S,EAAA,KAGA,SAAAC,EAAAC,GAEA,OADAA,EAAAF,EAAA,KACAE,EAhFAnG,EAAAQ,SAAA,SAAA3S,EAAAqM,GACA,OAAA,IAAA8F,EAAAnS,EAAAqM,EAAA/F,SAAAiS,QAAAlM,EAAAC,SAmBA6F,EAAA8F,YAAAA,EAQA9F,EAAAgB,aAAA,SAAAT,EAAA9F,GACA,GAAA8F,EACA,IAAA,IAAAtQ,EAAA,EAAAA,EAAAsQ,EAAAvR,SAAAiB,EACA,GAAA,iBAAAsQ,EAAAtQ,IAAAsQ,EAAAtQ,GAAA,IAAAwK,GAAA8F,EAAAtQ,GAAA,GAAAwK,EACA,OAAA,EACA,OAAA,GASAuF,EAAAiB,eAAA,SAAAV,EAAA1S,GACA,GAAA0S,EACA,IAAA,IAAAtQ,EAAA,EAAAA,EAAAsQ,EAAAvR,SAAAiB,EACA,GAAAsQ,EAAAtQ,KAAApC,EACA,OAAA,EACA,OAAA,GA0CAqE,OAAA6P,eAAA/B,EAAA1M,UAAA,cAAA,CACAsJ,IAAA,WACA,OAAAxJ,KAAA6S,IAAA7S,KAAA6S,EAAAhY,EAAAoY,QAAAjT,KAAA+G,YA6BA6F,EAAA1M,UAAAoN,OAAA,SAAAC,GACA,OAAA1S,EAAA+P,SAAA,CACA,UAAA5K,KAAAe,QACA,SAAA2R,EAAA1S,KAAAkT,YAAA3F,MASAX,EAAA1M,UAAA8S,QAAA,SAAAG,GAGA,GAAAA,EACA,IAAA,IAAApM,EAAAqM,EAAAtU,OAAAC,KAAAoU,GAAAtW,EAAA,EAAAA,EAAAuW,EAAAxX,SAAAiB,EACAkK,EAAAoM,EAAAC,EAAAvW,IAJAmD,KAKAyN,KACA1G,EAAAG,SAAA/M,GACA8T,EACAlH,EAAA0B,SAAAtO,GACAuP,EACA3C,EAAAsM,UAAAlZ,GACAyW,EACA7J,EAAAM,KAAAlN,GACA6T,EACApB,GAPAQ,SAOAgG,EAAAvW,GAAAkK,IAIA,OAAA/G,MAQA4M,EAAA1M,UAAAsJ,IAAA,SAAA/O,GACA,OAAAuF,KAAA+G,QAAA/G,KAAA+G,OAAAtM,IACA,MAUAmS,EAAA1M,UAAAoT,QAAA,SAAA7Y,GACA,GAAAuF,KAAA+G,QAAA/G,KAAA+G,OAAAtM,aAAAiP,EACA,OAAA1J,KAAA+G,OAAAtM,GAAAgO,OACA,MAAAzK,MAAA,iBAAAvD,IAUAmS,EAAA1M,UAAAuN,IAAA,SAAAyE,GAEA,KAAAA,aAAAlE,GAAAkE,EAAA/D,SAAAhU,IAAA+X,aAAAjE,GAAAiE,aAAAxI,GAAAwI,aAAAtB,GAAAsB,aAAAtF,GAAAsF,aAAAxB,GACA,MAAAxD,UAAA,wCAEA,GAAAlN,KAAA+G,OAEA,CACA,IAAAwM,EAAAvT,KAAAwJ,IAAA0I,EAAAzX,MACA,GAAA8Y,EAAA,CACA,KAAAA,aAAA3G,GAAAsF,aAAAtF,IAAA2G,aAAAtF,GAAAsF,aAAA3C,EAWA,MAAA5S,MAAA,mBAAAkU,EAAAzX,KAAA,QAAAuF,MARA,IADA,IAAA+G,EAAAwM,EAAAL,YACArW,EAAA,EAAAA,EAAAkK,EAAAnL,SAAAiB,EACAqV,EAAAzE,IAAA1G,EAAAlK,IACAmD,KAAA+N,OAAAwF,GACAvT,KAAA+G,SACA/G,KAAA+G,OAAA,IACAmL,EAAAsB,WAAAD,EAAAxS,SAAA,SAZAf,KAAA+G,OAAA,GAoBA,OAFA/G,KAAA+G,OAAAmL,EAAAzX,MAAAyX,GACAuB,MAAAzT,MACA8S,EAAA9S,OAUA4M,EAAA1M,UAAA6N,OAAA,SAAAmE,GAEA,KAAAA,aAAAvF,GACA,MAAAO,UAAA,qCACA,GAAAgF,EAAAlD,SAAAhP,KACA,MAAAhC,MAAAkU,EAAA,uBAAAlS,MAOA,cALAA,KAAA+G,OAAAmL,EAAAzX,MACAqE,OAAAC,KAAAiB,KAAA+G,QAAAnL,SACAoE,KAAA+G,OAAA5M,IAEA+X,EAAAwB,SAAA1T,MACA8S,EAAA9S,OASA4M,EAAA1M,UAAAnF,OAAA,SAAAyK,EAAAsB,GAEA,GAAAjM,EAAA6S,SAAAlI,GACAA,EAAAA,EAAAE,MAAA,UACA,IAAAhK,MAAAiY,QAAAnO,GACA,MAAA0H,UAAA,gBACA,GAAA1H,GAAAA,EAAA5J,QAAA,KAAA4J,EAAA,GACA,MAAAxH,MAAA,yBAGA,IADA,IAAA4V,EAAA5T,KACA,EAAAwF,EAAA5J,QAAA,CACA,IAAAiY,EAAArO,EAAAK,QACA,GAAA+N,EAAA7M,QAAA6M,EAAA7M,OAAA8M,IAEA,MADAD,EAAAA,EAAA7M,OAAA8M,cACAjH,GACA,MAAA5O,MAAA,kDAEA4V,EAAAnG,IAAAmG,EAAA,IAAAhH,EAAAiH,IAIA,OAFA/M,GACA8M,EAAAZ,QAAAlM,GACA8M,GAOAhH,EAAA1M,UAAA4T,WAAA,WAEA,IADA,IAAA/M,EAAA/G,KAAAkT,YAAArW,EAAA,EACAA,EAAAkK,EAAAnL,QACAmL,EAAAlK,aAAA+P,EACA7F,EAAAlK,KAAAiX,aAEA/M,EAAAlK,KAAAZ,UACA,OAAA+D,KAAA/D,WAUA2Q,EAAA1M,UAAA6T,OAAA,SAAAvO,EAAAwO,EAAAC,GASA,GANA,kBAAAD,GACAC,EAAAD,EACAA,EAAA7Z,IACA6Z,IAAAtY,MAAAiY,QAAAK,KACAA,EAAA,CAAAA,IAEAnZ,EAAA6S,SAAAlI,IAAAA,EAAA5J,OAAA,CACA,GAAA,MAAA4J,EACA,OAAAxF,KAAAoQ,KACA5K,EAAAA,EAAAE,MAAA,UACA,IAAAF,EAAA5J,OACA,OAAAoE,KAGA,GAAA,KAAAwF,EAAA,GACA,OAAAxF,KAAAoQ,KAAA2D,OAAAvO,EAAA9H,MAAA,GAAAsW,GAGA,IAAAE,EAAAlU,KAAAwJ,IAAAhE,EAAA,IACA,GAAA0O,GACA,GAAA,IAAA1O,EAAA5J,QACA,IAAAoY,IAAAA,EAAAnI,QAAAqI,EAAApH,aACA,OAAAoH,OACA,GAAAA,aAAAtH,IAAAsH,EAAAA,EAAAH,OAAAvO,EAAA9H,MAAA,GAAAsW,GAAA,IACA,OAAAE,OAIA,IAAA,IAAArX,EAAA,EAAAA,EAAAmD,KAAAkT,YAAAtX,SAAAiB,EACA,GAAAmD,KAAA6S,EAAAhW,aAAA+P,IAAAsH,EAAAlU,KAAA6S,EAAAhW,GAAAkX,OAAAvO,EAAAwO,GAAA,IACA,OAAAE,EAGA,OAAA,OAAAlU,KAAAgP,QAAAiF,EACA,KACAjU,KAAAgP,OAAA+E,OAAAvO,EAAAwO,IAqBApH,EAAA1M,UAAAuS,WAAA,SAAAjN,GACA,IAAA0O,EAAAlU,KAAA+T,OAAAvO,EAAA,CAAAyI,IACA,GAAAiG,EAEA,OAAAA,EADA,MAAAlW,MAAA,iBAAAwH,IAWAoH,EAAA1M,UAAAiU,WAAA,SAAA3O,GACA,IAAA0O,EAAAlU,KAAA+T,OAAAvO,EAAA,CAAAkE,IACA,GAAAwK,EAEA,OAAAA,EADA,MAAAlW,MAAA,iBAAAwH,EAAA,QAAAxF,OAWA4M,EAAA1M,UAAA+O,iBAAA,SAAAzJ,GACA,IAAA0O,EAAAlU,KAAA+T,OAAAvO,EAAA,CAAAyI,EAAAvE,IACA,GAAAwK,EAEA,OAAAA,EADA,MAAAlW,MAAA,yBAAAwH,EAAA,QAAAxF,OAWA4M,EAAA1M,UAAAkU,cAAA,SAAA5O,GACA,IAAA0O,EAAAlU,KAAA+T,OAAAvO,EAAA,CAAAoL,IACA,GAAAsD,EAEA,OAAAA,EADA,MAAAlW,MAAA,oBAAAwH,EAAA,QAAAxF,OAKA4M,EAAAoD,EAAA,SAAAC,EAAAoE,EAAAC,GACArG,EAAAgC,EACAW,EAAAyD,EACA3K,EAAA4K,I,gDC/aAlZ,EAAAR,QAAA+R,GAEAI,UAAA,mBAEA,IAEAsD,EAFAxV,EAAAS,EAAA,IAYA,SAAAqR,EAAAlS,EAAAsG,GAEA,IAAAlG,EAAA6S,SAAAjT,GACA,MAAAyS,UAAA,yBAEA,GAAAnM,IAAAlG,EAAAuT,SAAArN,GACA,MAAAmM,UAAA,6BAMAlN,KAAAe,QAAAA,EAMAf,KAAAsS,cAAA,KAMAtS,KAAAvF,KAAAA,EAMAuF,KAAAgP,OAAA,KAMAhP,KAAA+O,UAAA,EAMA/O,KAAAgN,QAAA,KAMAhN,KAAAc,SAAA,KAGAhC,OAAAyV,iBAAA5H,EAAAzM,UAAA,CAQAkQ,KAAA,CACA5G,IAAA,WAEA,IADA,IAAAoK,EAAA5T,KACA,OAAA4T,EAAA5E,QACA4E,EAAAA,EAAA5E,OACA,OAAA4E,IAUAzJ,SAAA,CACAX,IAAA,WAGA,IAFA,IAAAhE,EAAA,CAAAxF,KAAAvF,MACAmZ,EAAA5T,KAAAgP,OACA4E,GACApO,EAAAgP,QAAAZ,EAAAnZ,MACAmZ,EAAAA,EAAA5E,OAEA,OAAAxJ,EAAA7H,KAAA,SAUAgP,EAAAzM,UAAAoN,OAAA,WACA,MAAAtP,SAQA2O,EAAAzM,UAAAuT,MAAA,SAAAzE,GACAhP,KAAAgP,QAAAhP,KAAAgP,SAAAA,GACAhP,KAAAgP,OAAAjB,OAAA/N,MACAA,KAAAgP,OAAAA,EACAhP,KAAA+O,UAAA,EACAqB,EAAApB,EAAAoB,KACAA,aAAAC,GACAD,EAAAqE,EAAAzU,OAQA2M,EAAAzM,UAAAwT,SAAA,SAAA1E,GACAoB,EAAApB,EAAAoB,KACAA,aAAAC,GACAD,EAAAsE,EAAA1U,MACAA,KAAAgP,OAAA,KACAhP,KAAA+O,UAAA,GAOApC,EAAAzM,UAAAjE,QAAA,WACA,OAAA+D,KAAA+O,UAEA/O,KAAAoQ,gBAAAC,IACArQ,KAAA+O,UAAA,GAFA/O,MAWA2M,EAAAzM,UAAA0O,UAAA,SAAAnU,GACA,OAAAuF,KAAAe,QACAf,KAAAe,QAAAtG,GACAN,IAUAwS,EAAAzM,UAAA2O,UAAA,SAAApU,EAAAgF,EAAAqP,GAGA,OAFAA,GAAA9O,KAAAe,SAAAf,KAAAe,QAAAtG,KAAAN,MACA6F,KAAAe,UAAAf,KAAAe,QAAA,KAAAtG,GAAAgF,GACAO,MAUA2M,EAAAzM,UAAAyU,gBAAA,SAAAla,EAAAgF,EAAAmV,GACA5U,KAAAsS,gBACAtS,KAAAsS,cAAA,IAEA,IAIAuC,EAeAC,EAnBAxC,EAAAtS,KAAAsS,cAuBA,OAtBAsC,GAGAC,EAAAvC,EAAAyC,KAAA,SAAAF,GACA,OAAA/V,OAAAoB,UAAA8U,eAAAra,KAAAka,EAAApa,OAIAwa,EAAAJ,EAAApa,GACAI,EAAAqa,YAAAD,EAAAL,EAAAnV,MAGAoV,EAAA,IACApa,GAAAI,EAAAqa,YAAA,GAAAN,EAAAnV,GACA6S,EAAA/U,KAAAsX,MAIAC,EAAA,IACAra,GAAAgF,EACA6S,EAAA/U,KAAAuX,IAEA9U,MASA2M,EAAAzM,UAAAsT,WAAA,SAAAzS,EAAA+N,GACA,GAAA/N,EACA,IAAA,IAAAhC,EAAAD,OAAAC,KAAAgC,GAAAlE,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACAmD,KAAA6O,UAAA9P,EAAAlC,GAAAkE,EAAAhC,EAAAlC,IAAAiS,GACA,OAAA9O,MAOA2M,EAAAzM,UAAAzB,SAAA,WACA,IAAAsO,EAAA/M,KAAA8M,YAAAC,UACA5C,EAAAnK,KAAAmK,SACA,OAAAA,EAAAvO,OACAmR,EAAA,IAAA5C,EACA4C,GAIAJ,EAAAqD,EAAA,SAAAmF,GACA9E,EAAA8E,I,6BChPA/Z,EAAAR,QAAA8V,EAGA,IAAA/D,EAAArR,EAAA,IAGA0S,KAFA0C,EAAAxQ,UAAApB,OAAA+N,OAAAF,EAAAzM,YAAA4M,YAAA4D,GAAA3D,UAAA,QAEAzR,EAAA,KACAT,EAAAS,EAAA,IAYA,SAAAoV,EAAAjW,EAAA2a,EAAArU,EAAAiM,GAQA,GAPAtR,MAAAiY,QAAAyB,KACArU,EAAAqU,EACAA,EAAAjb,IAEAwS,EAAAhS,KAAAqF,KAAAvF,EAAAsG,GAGAqU,IAAAjb,KAAAuB,MAAAiY,QAAAyB,GACA,MAAAlI,UAAA,+BAMAlN,KAAAiI,MAAAmN,GAAA,GAOApV,KAAAyK,YAAA,GAMAzK,KAAAgN,QAAAA,EA0CA,SAAAqI,EAAApN,GACA,GAAAA,EAAA+G,OACA,IAAA,IAAAnS,EAAA,EAAAA,EAAAoL,EAAAwC,YAAA7O,SAAAiB,EACAoL,EAAAwC,YAAA5N,GAAAmS,QACA/G,EAAA+G,OAAAvB,IAAAxF,EAAAwC,YAAA5N,IA7BA6T,EAAAtD,SAAA,SAAA3S,EAAAqM,GACA,OAAA,IAAA4J,EAAAjW,EAAAqM,EAAAmB,MAAAnB,EAAA/F,QAAA+F,EAAAkG,UAQA0D,EAAAxQ,UAAAoN,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA3S,EAAA+P,SAAA,CACA,UAAA5K,KAAAe,QACA,QAAAf,KAAAiI,MACA,UAAAuF,EAAAxN,KAAAgN,QAAA7S,MAuBAuW,EAAAxQ,UAAAuN,IAAA,SAAA5D,GAGA,GAAAA,aAAAmE,EASA,OANAnE,EAAAmF,QAAAnF,EAAAmF,SAAAhP,KAAAgP,QACAnF,EAAAmF,OAAAjB,OAAAlE,GACA7J,KAAAiI,MAAA1K,KAAAsM,EAAApP,MACAuF,KAAAyK,YAAAlN,KAAAsM,GAEAwL,EADAxL,EAAAqB,OAAAlL,MAEAA,KARA,MAAAkN,UAAA,0BAgBAwD,EAAAxQ,UAAA6N,OAAA,SAAAlE,GAGA,KAAAA,aAAAmE,GACA,MAAAd,UAAA,yBAEA,IAAApR,EAAAkE,KAAAyK,YAAAoB,QAAAhC,GAGA,GAAA/N,EAAA,EACA,MAAAkC,MAAA6L,EAAA,uBAAA7J,MAUA,OARAA,KAAAyK,YAAAlK,OAAAzE,EAAA,IAIA,GAHAA,EAAAkE,KAAAiI,MAAA4D,QAAAhC,EAAApP,QAIAuF,KAAAiI,MAAA1H,OAAAzE,EAAA,GAEA+N,EAAAqB,OAAA,KACAlL,MAMA0Q,EAAAxQ,UAAAuT,MAAA,SAAAzE,GACArC,EAAAzM,UAAAuT,MAAA9Y,KAAAqF,KAAAgP,GAGA,IAFA,IAEAnS,EAAA,EAAAA,EAAAmD,KAAAiI,MAAArM,SAAAiB,EAAA,CACA,IAAAgN,EAAAmF,EAAAxF,IAAAxJ,KAAAiI,MAAApL,IACAgN,IAAAA,EAAAqB,SACArB,EAAAqB,OALAlL,MAMAyK,YAAAlN,KAAAsM,GAIAwL,EAAArV,OAMA0Q,EAAAxQ,UAAAwT,SAAA,SAAA1E,GACA,IAAA,IAAAnF,EAAAhN,EAAA,EAAAA,EAAAmD,KAAAyK,YAAA7O,SAAAiB,GACAgN,EAAA7J,KAAAyK,YAAA5N,IAAAmS,QACAnF,EAAAmF,OAAAjB,OAAAlE,GACA8C,EAAAzM,UAAAwT,SAAA/Y,KAAAqF,KAAAgP,IAmBA0B,EAAAlB,EAAA,WAGA,IAFA,IAAA4F,EAAA1Z,MAAAC,UAAAC,QACAE,EAAA,EACAA,EAAAH,UAAAC,QACAwZ,EAAAtZ,GAAAH,UAAAG,KACA,OAAA,SAAAoE,EAAAoV,GACAza,EAAA+U,aAAA1P,EAAA4M,aACAW,IAAA,IAAAiD,EAAA4E,EAAAF,IACAtW,OAAA6P,eAAAzO,EAAAoV,EAAA,CACA9L,IAAA3O,EAAA0a,YAAAH,GACAI,IAAA3a,EAAA4a,YAAAL,Q,0CCtMAha,EAAAR,QAAA2W,GAEAzQ,SAAA,KACAyQ,EAAArF,SAAA,CAAAwJ,UAAA,GAEA,IAAApE,EAAAhW,EAAA,IACA+U,EAAA/U,EAAA,IACA2S,EAAA3S,EAAA,IACA0S,EAAA1S,EAAA,IACAqV,EAAArV,EAAA,IACAoV,EAAApV,EAAA,IACAoO,EAAApO,EAAA,IACAsV,EAAAtV,EAAA,IACAuV,EAAAvV,EAAA,IACA2Q,EAAA3Q,EAAA,IACAT,EAAAS,EAAA,IAEAqa,EAAA,gBACAC,EAAA,kBACAC,EAAA,qBACAC,EAAA,uBACAC,EAAA,YACAC,EAAA,cACAC,EAAA,oDACAC,EAAA,2BACAC,EAAA,+DACAC,EAAA,kCAmCA,SAAA7E,EAAA/S,EAAA4R,EAAArP,GAEAqP,aAAAC,IACAtP,EAAAqP,EACAA,EAAA,IAAAC,GAKA,IASAgG,EACAC,EACAC,EACAC,EA0pBAC,EAjhBAA,EACAC,EAtJAC,GAFA5V,EADAA,GACAwQ,EAAArF,UAEAyK,wBAAA,EACAC,EAAAtF,EAAA9S,EAAAuC,EAAA8V,uBAAA,GACAC,EAAAF,EAAAE,KACAvZ,EAAAqZ,EAAArZ,KACAwZ,EAAAH,EAAAG,KACAC,EAAAJ,EAAAI,KACAC,EAAAL,EAAAK,KAEAC,GAAA,EAKAC,GAAA,EAEAvD,EAAAxD,EAEAgH,EAAArW,EAAA2U,SAAA,SAAAjb,GAAA,OAAAA,GAAAI,EAAAwc,UAGA,SAAAC,EAAAb,EAAAhc,EAAA8c,GACA,IAAAzW,EAAAyQ,EAAAzQ,SAGA,OAFAyW,IACAhG,EAAAzQ,SAAA,MACA9C,MAAA,YAAAvD,GAAA,SAAA,KAAAgc,EAAA,OAAA3V,EAAAA,EAAA,KAAA,IAAA,QAAA8V,EAAAY,KAAA,KAGA,SAAAC,IACA,IACAhB,EADAhO,EAAA,GAEA,GAEA,GAAA,OAAAgO,EAAAK,MAAA,MAAAL,EACA,MAAAa,EAAAb,SAEAhO,EAAAlL,KAAAuZ,KACAE,EAAAP,GAEA,OADAA,EAAAM,MACA,MAAAN,GACA,OAAAhO,EAAA9K,KAAA,IAGA,SAAA+Z,EAAAC,GACA,IAAAlB,EAAAK,IACA,OAAAL,GACA,IAAA,IACA,IAAA,IAEA,OADAlZ,EAAAkZ,GACAgB,IACA,IAAA,OAAA,IAAA,OACA,OAAA,EACA,IAAA,QAAA,IAAA,QACA,OAAA,EAEA,IACAG,IAuBAnB,EAvBAA,EAuBAc,GAvBA,EAwBAjV,EAAA,EAKA,OAJA,MAAAmU,EAAA,IAAAA,MACAnU,GAAA,EACAmU,EAAAA,EAAAoB,UAAA,IAEApB,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAAnU,GAAAW,EAAAA,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAAD,IACA,IAAA,IACA,OAAA,EAEA,GAAA2S,EAAA1X,KAAAwY,GACA,OAAAnU,EAAAwV,SAAArB,EAAA,IACA,GAAAZ,EAAA5X,KAAAwY,GACA,OAAAnU,EAAAwV,SAAArB,EAAA,IACA,GAAAV,EAAA9X,KAAAwY,GACA,OAAAnU,EAAAwV,SAAArB,EAAA,GAGA,GAAAR,EAAAhY,KAAAwY,GACA,OAAAnU,EAAAyV,WAAAtB,GAGA,MAAAa,EAAAb,EAAA,SAAAc,GAhDA,MAAAjS,GAGA,GAAAqS,GAAAxB,EAAAlY,KAAAwY,GACA,OAAAA,EAGA,MAAAa,EAAAb,EAAA,UAIA,SAAAuB,EAAAC,EAAAC,GAEA,IADA,IAAAlb,GAEAkb,GAAA,OAAAzB,EAAAM,MAAA,MAAAN,EAGAwB,EAAA1a,KAAA,CAAAP,EAAAmb,EAAArB,KAAAE,EAAA,MAAA,GAAAmB,EAAArB,KAAA9Z,IAFAib,EAAA1a,KAAAka,KAGAT,EAAA,KAAA,KACAA,EAAA,KAgCA,SAAAmB,EAAA1B,EAAA2B,GACA,OAAA3B,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAA,UACA,IAAA,IACA,OAAA,EAIA,IAAA2B,GAAA,MAAA3B,EAAA,IAAAA,IACA,MAAAa,EAAAb,EAAA,MAEA,GAAAb,EAAA3X,KAAAwY,GACA,OAAAqB,SAAArB,EAAA,IACA,GAAAX,EAAA7X,KAAAwY,GACA,OAAAqB,SAAArB,EAAA,IAGA,GAAAT,EAAA/X,KAAAwY,GACA,OAAAqB,SAAArB,EAAA,GAGA,MAAAa,EAAAb,EAAA,MAmDA,SAAA4B,EAAArJ,EAAAyH,GACA,OAAAA,GAEA,IAAA,SAGA,OAFA6B,EAAAtJ,EAAAyH,GACAO,EAAA,KACA,EAEA,IAAA,UACAuB,IAwCAvJ,EAxCAA,EAwCAyH,EAxCAA,EA2CA,IAAAP,EAAAjY,KAAAwY,EAAAK,KACA,MAAAQ,EAAAb,EAAA,aAEA,IAAArP,EAAA,IAAA6G,EAAAwI,GA7CA,OA8CA+B,EAAApR,EAAA,SAAAqP,GACA,IAAA4B,EAAAjR,EAAAqP,GAGA,OAAAA,GAEA,IAAA,MACAgC,IA4IAzJ,EA5IA5H,EA8IAS,GADAmP,EAAA,KACAF,KAGA,GAAA7K,EAAAO,OAAA3E,KAAA1N,GACA,MAAAmd,EAAAzP,EAAA,QAEAmP,EAAA,KACA,IAAA0B,EAAA5B,IAGA,IAAAX,EAAAlY,KAAAya,GACA,MAAApB,EAAAoB,EAAA,QAEA1B,EAAA,KACA,IAAAvc,EAAAqc,IAGA,IAAAZ,EAAAjY,KAAAxD,GACA,MAAA6c,EAAA7c,EAAA,QAEAuc,EAAA,KACA,IAAAnN,EAAA,IAAA8G,EAAAyG,EAAA3c,GAAA0d,EAAArB,KAAAjP,EAAA6Q,GACAF,EAAA3O,EAAA,SAAA4M,GAGA,GAAA,WAAAA,EAIA,MAAAa,EAAAb,GAHA6B,EAAAzO,EAAA4M,GACAO,EAAA,MAIA,WACA2B,EAAA9O,KAEAmF,EAAAvB,IAAA5D,GA/KA,MAEA,IAAA,WACA,IAAA,WACA+O,EAAAxR,EAAAqP,GACA,MAEA,IAAA,WAGAmC,EAAAxR,EADA+P,EACA,kBAEA,YAEA,MAEA,IAAA,QAkKAnI,EAjKA5H,EAiKAqP,EAjKAA,EAoKA,IAAAP,EAAAjY,KAAAwY,EAAAK,KACA,MAAAQ,EAAAb,EAAA,QAEA,IAAAxO,EAAA,IAAAyI,EAAA0G,EAAAX,IACA+B,EAAAvQ,EAAA,SAAAwO,GACA,WAAAA,GACA6B,EAAArQ,EAAAwO,GACAO,EAAA,OAEAzZ,EAAAkZ,GACAmC,EAAA3Q,EAAA,eAGA+G,EAAAvB,IAAAxF,GAhLA,MAEA,IAAA,aACA+P,EAAA5Q,EAAAyR,aAAAzR,EAAAyR,WAAA,KACA,MAEA,IAAA,WACAb,EAAA5Q,EAAA+F,WAAA/F,EAAA+F,SAAA,KAAA,GACA,MAEA,QAEA,IAAAgK,IAAAhB,EAAAlY,KAAAwY,GACA,MAAAa,EAAAb,GAEAlZ,EAAAkZ,GACAmC,EAAAxR,EAAA,eAIA4H,EAAAvB,IAAArG,GA5FA,EAEA,IAAA,OAyPA4H,EAxPAA,EAwPAyH,EAxPAA,EA2PA,IAAAP,EAAAjY,KAAAwY,EAAAK,KACA,MAAAQ,EAAAb,EAAA,QAEA,IAAApJ,EAAA,IAAA3D,EAAA+M,GA7PA,OA8PA+B,EAAAnL,EAAA,SAAAoJ,GACA,OAAAA,GACA,IAAA,SACA6B,EAAAjL,EAAAoJ,GACAO,EAAA,KACA,MAEA,IAAA,WACAgB,EAAA3K,EAAAF,WAAAE,EAAAF,SAAA,KAAA,GACA,MAEA,QACA2L,IAMA9J,EANA3B,EAMAoJ,EANAA,EASA,IAAAP,EAAAjY,KAAAwY,GACA,MAAAa,EAAAb,EAAA,QAEAO,EAAA,KACA,IAAAvX,EAAA0Y,EAAArB,KAAA,GACAiC,EAAA,GACAP,EAAAO,EAAA,SAAAtC,GAGA,GAAA,WAAAA,EAIA,MAAAa,EAAAb,GAHA6B,EAAAS,EAAAtC,GACAO,EAAA,MAIA,WACA2B,EAAAI,KAEA/J,EAAAvB,IAAAgJ,EAAAhX,EAAAsZ,EAAA/L,YAxBAgC,EAAAvB,IAAAJ,GA7QA,EAEA,IAAA,UACA2L,IAwXAhK,EAxXAA,EAwXAyH,EAxXAA,EA2XA,IAAAP,EAAAjY,KAAAwY,EAAAK,KACA,MAAAQ,EAAAb,EAAA,gBAEA,IAAAwC,EAAA,IAAArI,EAAA6F,GA7XA,OA8XA+B,EAAAS,EAAA,SAAAxC,GACA,IAAA4B,EAAAY,EAAAxC,GAAA,CAIA,GAAA,QAAAA,EAGA,MAAAa,EAAAb,GAFAyC,IAOAlK,EAPAiK,EAUAE,EAAAlC,IAEA7P,EAAAqP,EAGA,IAAAP,EAAAjY,KAAAwY,EAAAK,KACA,MAAAQ,EAAAb,EAAA,QAEA,IACAtE,EAAAC,EACAC,EAFA5X,EAAAgc,EASA,GALAO,EAAA,KACAA,EAAA,UAAA,KACA5E,GAAA,IAGA+D,EAAAlY,KAAAwY,EAAAK,KACA,MAAAQ,EAAAb,GAQA,GANAtE,EAAAsE,EACAO,EAAA,KAAAA,EAAA,WAAAA,EAAA,KACAA,EAAA,UAAA,KACA3E,GAAA,IAGA8D,EAAAlY,KAAAwY,EAAAK,KACA,MAAAQ,EAAAb,GAEA5U,EAAA4U,EACAO,EAAA,KAEA,IAAAoC,EAAA,IAAAvI,EAAApW,EAAA2M,EAAA+K,EAAAtQ,EAAAuQ,EAAAC,GACA+G,EAAApM,QAAAmM,EACAX,EAAAY,EAAA,SAAA3C,GAGA,GAAA,WAAAA,EAIA,MAAAa,EAAAb,GAHA6B,EAAAc,EAAA3C,GACAO,EAAA,OAKAhI,EAAAvB,IAAA2L,MAlDApK,EAAAvB,IAAAwL,GAxYA,EAEA,IAAA,SACAI,IA0bArK,EA1bAA,EA0bAyH,EA1bAA,EA6bA,IAAAN,EAAAlY,KAAAwY,EAAAK,KACA,MAAAQ,EAAAb,EAAA,aAEA,IAAA6C,EAAA7C,EA/bA,OAgcA+B,EAAA,KAAA,SAAA/B,GACA,OAAAA,GAEA,IAAA,WACA,IAAA,WACAmC,EAAA5J,EAAAyH,EAAA6C,GACA,MAEA,IAAA,WAGAV,EAAA5J,EADAmI,EACA,kBAEA,WAFAmC,GAIA,MAEA,QAEA,IAAAnC,IAAAhB,EAAAlY,KAAAwY,GACA,MAAAa,EAAAb,GACAlZ,EAAAkZ,GACAmC,EAAA5J,EAAA,WAAAsK,MAtdA,GAKA,SAAAd,EAAA5F,EAAA2G,EAAAC,GACA,IAQA/C,EARAgD,EAAA7C,EAAAY,KAOA,GANA5E,IACA,iBAAAA,EAAA5F,UACA4F,EAAA5F,QAAAiK,KAEArE,EAAA9R,SAAAyQ,EAAAzQ,UAEAkW,EAAA,KAAA,GAAA,CAEA,KAAA,OAAAP,EAAAK,MACAyC,EAAA9C,GACAO,EAAA,KAAA,QAEAwC,GACAA,IACAxC,EAAA,KACApE,IAAA,iBAAAA,EAAA5F,SAAA2J,KACA/D,EAAA5F,QAAAiK,EAAAwC,IAAA7G,EAAA5F,SA4DA,SAAA4L,EAAA5J,EAAApG,EAAAuF,GACA,IAAA/G,EAAA0P,IACA,GAAA,UAAA1P,EAAA,CACAsS,IAgDA1K,EAhDAA,EAgDApG,EAhDAA,EAiDAnO,EAAAqc,IAGA,IAAAZ,EAAAjY,KAAAxD,GACA,MAAA6c,EAAA7c,EAAA,QAEA,IAAAqV,EAAAjV,EAAA8e,QAAAlf,GAIA4M,GAHA5M,IAAAqV,IACArV,EAAAI,EAAA+e,QAAAnf,IACAuc,EAAA,KACAmB,EAAArB,MACA1P,EAAA,IAAA6G,EAAAxT,GA3DA,OA4DA2M,EAAA2E,OAAA,GAEAlC,EADA,IAAAmE,EAAA8B,EAAAzI,EAAA5M,EAAAmO,IACA9H,SAAAyQ,EAAAzQ,SACA0X,EAAApR,EAAA,SAAAqP,GACA,OAAAA,GAEA,IAAA,SACA6B,EAAAlR,EAAAqP,GACAO,EAAA,KACA,MAEA,IAAA,WACA,IAAA,WACA4B,EAAAxR,EAAAqP,GACA,MAEA,IAAA,WAGAmC,EAAAxR,EADA+P,EACA,kBAEA,YAEA,MAGA,QACA,MAAAG,EAAAb,WAGAzH,EAAAvB,IAAArG,GACAqG,IAAA5D,GAvFA,IAAAsM,EAAAlY,KAAAmJ,GACA,MAAAkQ,EAAAlQ,EAAA,QAEA3M,EAAAqc,IAGA,IAAAZ,EAAAjY,KAAAxD,GACA,MAAA6c,EAAA7c,EAAA,QAEAA,EAAA2c,EAAA3c,GACAuc,EAAA,KAEA,IAAAnN,EAAA,IAAAmE,EAAAvT,EAAA0d,EAAArB,KAAA1P,EAAAwB,EAAAuF,GACAqK,EAAA3O,EAAA,SAAA4M,GAGA,GAAA,WAAAA,EAIA,MAAAa,EAAAb,GAHA6B,EAAAzO,EAAA4M,GACAO,EAAA,MAIA,WACA2B,EAAA9O,KAGA,oBAAAjB,GAEAX,EAAA,IAAAyI,EAAA,IAAAjW,GACAoP,EAAAgF,UAAA,mBAAA,GACA5G,EAAAwF,IAAA5D,GACAmF,EAAAvB,IAAAxF,IAEA+G,EAAAvB,IAAA5D,GAMAsN,IAAAtN,EAAAI,UAAAgC,EAAAG,OAAAhF,KAAAjN,IAAA8R,EAAAE,MAAA/E,KAAAjN,IACA0P,EAAAgF,UAAA,UAAA,GAAA,GA6JA,SAAAyJ,EAAAtJ,EAAAyH,GACA,IAAAoD,EAAA7C,EAAA,KAAA,GAGA,IAAAb,EAAAlY,KAAAwY,EAAAK,KACA,MAAAQ,EAAAb,EAAA,QAEA,IAEA7B,EAFAna,EAAAgc,EACAqD,EAAArf,EAeAsf,GAZAF,IACA7C,EAAA,KAEA8C,EADArf,EAAA,IAAAA,EAAA,IAEAgc,EAAAM,IACAX,EAAAnY,KAAAwY,KACA7B,EAAA6B,EAAAuD,OAAA,GACAvf,GAAAgc,EACAK,MAGAE,EAAA,KAKA,SAAAiD,EAAAjL,EAAAvU,GACA,GAAAuc,EAAA,KAAA,GAAA,CAEA,IADA,IAAAkD,EAAA,IACAlD,EAAA,KAAA,IAAA,CAEA,IAAAd,EAAAjY,KAAAwY,EAAAK,KACA,MAAAQ,EAAAb,EAAA,QAEA,IAAAhX,EACAmV,EAAA6B,EAYA0D,GAXA,MAAApD,IACAtX,EAAAwa,EAAAjL,EAAAvU,EAAA,IAAAgc,IAEAO,EAAA,KACA,MAAAD,IACAtX,EAAAwa,EAAAjL,EAAAvU,EAAA,IAAAgc,IAEAhX,EAAAiY,GAAA,GACA7I,EAAAG,EAAAvU,EAAA,IAAAgc,EAAAhX,KAGAya,EAAAtF,IACAuF,IACA1a,EAAA,GAAA2a,OAAAD,GAAAC,OAAA3a,IACAya,EAAAtF,GAAAnV,EACAuX,EAAA,KAAA,GAEA,OAAAkD,EAGA,IAAAG,EAAA3C,GAAA,GACA7I,EAAAG,EAAAvU,EAAA4f,GACA,OAAAA,EApCAJ,CAAAjL,EAAAvU,IA6CAA,EA5CAqf,EA4CAra,EA5CAsa,EA4CAnF,EA5CAA,GA4CA5F,EA5CAA,GA6CA2F,iBACA3F,EAAA2F,gBAAAla,EAAAgF,EAAAmV,GAPA,SAAA/F,EAAAG,EAAAvU,EAAAgF,GACAuP,EAAAH,WACAG,EAAAH,UAAApU,EAAAgF,GAQA,SAAAkZ,EAAA3J,GACA,GAAAgI,EAAA,KAAA,GAAA,CACA,KACAsB,EAAAtJ,EAAA,UACAgI,EAAA,KAAA,KACAA,EAAA,MA+GA,KAAA,QAAAP,EAAAK,MACA,OAAAL,GAEA,IAAA,UAGA,IAAAS,EACA,MAAAI,EAAAb,GAviBA,GAAAJ,IAAAlc,GACA,MAAAmd,EAAA,WAKA,GAHAjB,EAAAS,KAGAX,EAAAlY,KAAAoY,GACA,MAAAiB,EAAAjB,EAAA,QAEAzC,EAAAA,EAAA7Y,OAAAsb,GACAW,EAAA,KAgiBA,MAEA,IAAA,SAGA,IAAAE,EACA,MAAAI,EAAAb,GAhiBA,OADAC,EADAD,OAAAA,EAAAM,KAGA,IAAA,OACAL,EAAAH,EAAAA,GAAA,GACAO,IACA,MACA,IAAA,SACAA,IAEA,QACAJ,EAAAJ,EAAAA,GAAA,GAGAG,EAAAgB,IACAT,EAAA,KACAN,EAAAnZ,KAAAkZ,GAqhBA,MAEA,IAAA,SAGA,IAAAS,EACA,MAAAI,EAAAb,GAlhBA,GALAO,EAAA,KACAR,EAAAiB,MACAN,EAAA,WAAAX,IAGA,WAAAA,EACA,MAAAc,EAAAd,EAAA,UAEAQ,EAAA,KAkhBA,MAEA,IAAA,SAEAsB,EAAA1E,EAAA6C,GACAO,EAAA,KACA,MAEA,QAGA,GAAAqB,EAAAzE,EAAA6C,GAAA,CACAS,GAAA,EACA,SAIA,MAAAI,EAAAb,GAKA,OADAlF,EAAAzQ,SAAA,KACA,CACAwZ,QAAAjE,EACAC,QAAAA,EACAC,YAAAA,EACAC,OAAAA,EACApG,KAAAA,K,yFCpyBAhV,EAAAR,QAAAsW,EAEA,IAEAC,EAFAtW,EAAAS,EAAA,IAIAif,EAAA1f,EAAA0f,SACAjU,EAAAzL,EAAAyL,KAGA,SAAAkU,EAAAzI,EAAA0I,GACA,OAAAC,WAAA,uBAAA3I,EAAA1P,IAAA,OAAAoY,GAAA,GAAA,MAAA1I,EAAAxL,KASA,SAAA2K,EAAAnU,GAMAiD,KAAAoC,IAAArF,EAMAiD,KAAAqC,IAAA,EAMArC,KAAAuG,IAAAxJ,EAAAnB,OAgBA,SAAAiR,IACA,OAAAhS,EAAA8f,OACA,SAAA5d,GACA,OAAAmU,EAAArE,OAAA,SAAA9P,GACA,OAAAlC,EAAA8f,OAAAC,SAAA7d,GACA,IAAAoU,EAAApU,GAEA8d,EAAA9d,KACAA,IAGA8d,EAxBA,IA4CApb,EA5CAob,EAAA,oBAAAlZ,WACA,SAAA5E,GACA,GAAAA,aAAA4E,YAAAjG,MAAAiY,QAAA5W,GACA,OAAA,IAAAmU,EAAAnU,GACA,MAAAiB,MAAA,mBAGA,SAAAjB,GACA,GAAArB,MAAAiY,QAAA5W,GACA,OAAA,IAAAmU,EAAAnU,GACA,MAAAiB,MAAA,mBAsEA,SAAA8c,IAEA,IAAAC,EAAA,IAAAR,EAAA,EAAA,GACA1d,EAAA,EACA,KAAA,EAAAmD,KAAAuG,IAAAvG,KAAAqC,KAaA,CACA,KAAAxF,EAAA,IAAAA,EAAA,CAEA,GAAAmD,KAAAqC,KAAArC,KAAAuG,IACA,MAAAiU,EAAAxa,MAGA,GADA+a,EAAAjX,IAAAiX,EAAAjX,IAAA,IAAA9D,KAAAoC,IAAApC,KAAAqC,OAAA,EAAAxF,KAAA,EACAmD,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAA0Y,EAIA,OADAA,EAAAjX,IAAAiX,EAAAjX,IAAA,IAAA9D,KAAAoC,IAAApC,KAAAqC,SAAA,EAAAxF,KAAA,EACAke,EAxBA,KAAAle,EAAA,IAAAA,EAGA,GADAke,EAAAjX,IAAAiX,EAAAjX,IAAA,IAAA9D,KAAAoC,IAAApC,KAAAqC,OAAA,EAAAxF,KAAA,EACAmD,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAA0Y,EAKA,GAFAA,EAAAjX,IAAAiX,EAAAjX,IAAA,IAAA9D,KAAAoC,IAAApC,KAAAqC,OAAA,MAAA,EACA0Y,EAAAhX,IAAAgX,EAAAhX,IAAA,IAAA/D,KAAAoC,IAAApC,KAAAqC,OAAA,KAAA,EACArC,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAA0Y,EAgBA,GAfAle,EAAA,EAeA,EAAAmD,KAAAuG,IAAAvG,KAAAqC,KACA,KAAAxF,EAAA,IAAAA,EAGA,GADAke,EAAAhX,IAAAgX,EAAAhX,IAAA,IAAA/D,KAAAoC,IAAApC,KAAAqC,OAAA,EAAAxF,EAAA,KAAA,EACAmD,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAA0Y,OAGA,KAAAle,EAAA,IAAAA,EAAA,CAEA,GAAAmD,KAAAqC,KAAArC,KAAAuG,IACA,MAAAiU,EAAAxa,MAGA,GADA+a,EAAAhX,IAAAgX,EAAAhX,IAAA,IAAA/D,KAAAoC,IAAApC,KAAAqC,OAAA,EAAAxF,EAAA,KAAA,EACAmD,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAA0Y,EAIA,MAAA/c,MAAA,2BAkCA,SAAAgd,EAAA5Y,EAAAnF,GACA,OAAAmF,EAAAnF,EAAA,GACAmF,EAAAnF,EAAA,IAAA,EACAmF,EAAAnF,EAAA,IAAA,GACAmF,EAAAnF,EAAA,IAAA,MAAA,EA+BA,SAAAge,IAGA,GAAAjb,KAAAqC,IAAA,EAAArC,KAAAuG,IACA,MAAAiU,EAAAxa,KAAA,GAEA,OAAA,IAAAua,EAAAS,EAAAhb,KAAAoC,IAAApC,KAAAqC,KAAA,GAAA2Y,EAAAhb,KAAAoC,IAAApC,KAAAqC,KAAA,IA3KA6O,EAAArE,OAAAA,IAEAqE,EAAAhR,UAAAgb,EAAArgB,EAAAa,MAAAwE,UAAAib,UAAAtgB,EAAAa,MAAAwE,UAAAxC,MAOAwT,EAAAhR,UAAAkb,QACA3b,EAAA,WACA,WACA,GAAAA,GAAA,IAAAO,KAAAoC,IAAApC,KAAAqC,QAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EACA,GAAAA,GAAAA,GAAA,IAAAO,KAAAoC,IAAApC,KAAAqC,OAAA,KAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EACA,GAAAA,GAAAA,GAAA,IAAAO,KAAAoC,IAAApC,KAAAqC,OAAA,MAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EACA,GAAAA,GAAAA,GAAA,IAAAO,KAAAoC,IAAApC,KAAAqC,OAAA,MAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EACA,GAAAA,GAAAA,GAAA,GAAAO,KAAAoC,IAAApC,KAAAqC,OAAA,MAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EAGA,IAAAO,KAAAqC,KAAA,GAAArC,KAAAuG,IAEA,MADAvG,KAAAqC,IAAArC,KAAAuG,IACAiU,EAAAxa,KAAA,IAEA,OAAAP,IAQAyR,EAAAhR,UAAAmb,MAAA,WACA,OAAA,EAAArb,KAAAob,UAOAlK,EAAAhR,UAAAob,OAAA,WACA,IAAA7b,EAAAO,KAAAob,SACA,OAAA3b,IAAA,IAAA,EAAAA,GAAA,GAqFAyR,EAAAhR,UAAAqb,KAAA,WACA,OAAA,IAAAvb,KAAAob,UAcAlK,EAAAhR,UAAAsb,QAAA,WAGA,GAAAxb,KAAAqC,IAAA,EAAArC,KAAAuG,IACA,MAAAiU,EAAAxa,KAAA,GAEA,OAAAgb,EAAAhb,KAAAoC,IAAApC,KAAAqC,KAAA,IAOA6O,EAAAhR,UAAAub,SAAA,WAGA,GAAAzb,KAAAqC,IAAA,EAAArC,KAAAuG,IACA,MAAAiU,EAAAxa,KAAA,GAEA,OAAA,EAAAgb,EAAAhb,KAAAoC,IAAApC,KAAAqC,KAAA,IAmCA6O,EAAAhR,UAAAwb,MAAA,WAGA,GAAA1b,KAAAqC,IAAA,EAAArC,KAAAuG,IACA,MAAAiU,EAAAxa,KAAA,GAEA,IAAAP,EAAA5E,EAAA6gB,MAAAnX,YAAAvE,KAAAoC,IAAApC,KAAAqC,KAEA,OADArC,KAAAqC,KAAA,EACA5C,GAQAyR,EAAAhR,UAAAyb,OAAA,WAGA,GAAA3b,KAAAqC,IAAA,EAAArC,KAAAuG,IACA,MAAAiU,EAAAxa,KAAA,GAEA,IAAAP,EAAA5E,EAAA6gB,MAAAzW,aAAAjF,KAAAoC,IAAApC,KAAAqC,KAEA,OADArC,KAAAqC,KAAA,EACA5C,GAOAyR,EAAAhR,UAAAwL,MAAA,WACA,IAAA9P,EAAAoE,KAAAob,SACApe,EAAAgD,KAAAqC,IACApF,EAAA+C,KAAAqC,IAAAzG,EAGA,GAAAqB,EAAA+C,KAAAuG,IACA,MAAAiU,EAAAxa,KAAApE,GAGA,OADAoE,KAAAqC,KAAAzG,EACAF,MAAAiY,QAAA3T,KAAAoC,KACApC,KAAAoC,IAAA1E,MAAAV,EAAAC,GACAD,IAAAC,EACA,IAAA+C,KAAAoC,IAAA0K,YAAA,GACA9M,KAAAkb,EAAAvgB,KAAAqF,KAAAoC,IAAApF,EAAAC,IAOAiU,EAAAhR,UAAA5D,OAAA,WACA,IAAAoP,EAAA1L,KAAA0L,QACA,OAAApF,EAAAE,KAAAkF,EAAA,EAAAA,EAAA9P,SAQAsV,EAAAhR,UAAA8W,KAAA,SAAApb,GACA,GAAA,iBAAAA,EAAA,CAEA,GAAAoE,KAAAqC,IAAAzG,EAAAoE,KAAAuG,IACA,MAAAiU,EAAAxa,KAAApE,GACAoE,KAAAqC,KAAAzG,OAEA,GAEA,GAAAoE,KAAAqC,KAAArC,KAAAuG,IACA,MAAAiU,EAAAxa,YACA,IAAAA,KAAAoC,IAAApC,KAAAqC,QAEA,OAAArC,MAQAkR,EAAAhR,UAAA0b,SAAA,SAAArP,GACA,OAAAA,GACA,KAAA,EACAvM,KAAAgX,OACA,MACA,KAAA,EACAhX,KAAAgX,KAAA,GACA,MACA,KAAA,EACAhX,KAAAgX,KAAAhX,KAAAob,UACA,MACA,KAAA,EACA,KAAA,IAAA7O,EAAA,EAAAvM,KAAAob,WACApb,KAAA4b,SAAArP,GAEA,MACA,KAAA,EACAvM,KAAAgX,KAAA,GACA,MAGA,QACA,MAAAhZ,MAAA,qBAAAuO,EAAA,cAAAvM,KAAAqC,KAEA,OAAArC,MAGAkR,EAAAlB,EAAA,SAAA6L,GACA1K,EAAA0K,EACA3K,EAAArE,OAAAA,IACAsE,EAAAnB,IAEA,IAAAzU,EAAAV,EAAAI,KAAA,SAAA,WACAJ,EAAAihB,MAAA5K,EAAAhR,UAAA,CAEA6b,MAAA,WACA,OAAAjB,EAAAngB,KAAAqF,MAAAzE,IAAA,IAGAygB,OAAA,WACA,OAAAlB,EAAAngB,KAAAqF,MAAAzE,IAAA,IAGA0gB,OAAA,WACA,OAAAnB,EAAAngB,KAAAqF,MAAAkc,WAAA3gB,IAAA,IAGA4gB,QAAA,WACA,OAAAlB,EAAAtgB,KAAAqF,MAAAzE,IAAA,IAGA6gB,SAAA,WACA,OAAAnB,EAAAtgB,KAAAqF,MAAAzE,IAAA,Q,6BCrZAH,EAAAR,QAAAuW,EAGA,IAAAD,EAAA5V,EAAA,IAGAT,IAFAsW,EAAAjR,UAAApB,OAAA+N,OAAAqE,EAAAhR,YAAA4M,YAAAqE,EAEA7V,EAAA,KASA,SAAA6V,EAAApU,GACAmU,EAAAvW,KAAAqF,KAAAjD,GASAoU,EAAAnB,EAAA,WAEAnV,EAAA8f,SACAxJ,EAAAjR,UAAAgb,EAAArgB,EAAA8f,OAAAza,UAAAxC,QAOAyT,EAAAjR,UAAA5D,OAAA,WACA,IAAAiK,EAAAvG,KAAAob,SACA,OAAApb,KAAAoC,IAAAia,UACArc,KAAAoC,IAAAia,UAAArc,KAAAqC,IAAArC,KAAAqC,IAAA5F,KAAA6f,IAAAtc,KAAAqC,IAAAkE,EAAAvG,KAAAuG,MACAvG,KAAAoC,IAAA3D,SAAA,QAAAuB,KAAAqC,IAAArC,KAAAqC,IAAA5F,KAAA6f,IAAAtc,KAAAqC,IAAAkE,EAAAvG,KAAAuG,OAUA4K,EAAAnB,K,mCCjDA5U,EAAAR,QAAAyV,EAGA,IAQApC,EACAsD,EACA3K,EAVAgG,EAAAtR,EAAA,IAGA0S,KAFAqC,EAAAnQ,UAAApB,OAAA+N,OAAAD,EAAA1M,YAAA4M,YAAAuD,GAAAtD,UAAA,OAEAzR,EAAA,KACAoO,EAAApO,EAAA,IACAoV,EAAApV,EAAA,IACAT,EAAAS,EAAA,IAaA,SAAA+U,EAAAtP,GACA6L,EAAAjS,KAAAqF,KAAA,GAAAe,GAMAf,KAAAuc,SAAA,GAMAvc,KAAAwc,MAAA,GAuCA,SAAAC,KA9BApM,EAAAjD,SAAA,SAAAtG,EAAAsJ,GAKA,OAHAA,EADAA,GACA,IAAAC,EACAvJ,EAAA/F,SACAqP,EAAAoD,WAAA1M,EAAA/F,SACAqP,EAAA4C,QAAAlM,EAAAC,SAWAsJ,EAAAnQ,UAAAwc,YAAA7hB,EAAA2K,KAAAvJ,QAUAoU,EAAAnQ,UAAAQ,MAAA7F,EAAA6F,MAaA2P,EAAAnQ,UAAAiQ,KAAA,SAAAA,EAAArP,EAAAC,EAAAC,GACA,mBAAAD,IACAC,EAAAD,EACAA,EAAA5G,IAEA,IAAAwiB,EAAA3c,KACA,IAAAgB,EACA,OAAAnG,EAAA8F,UAAAwP,EAAAwM,EAAA7b,EAAAC,GAEA,IAAA6b,EAAA5b,IAAAyb,EAGA,SAAAI,EAAA1gB,EAAAiU,GAEA,GAAApP,EAAA,CAEA,IAAA8b,EAAA9b,EAEA,GADAA,EAAA,KACA4b,EACA,MAAAzgB,EACA2gB,EAAA3gB,EAAAiU,IAIA,SAAA2M,EAAAjc,GACA,IAAAkc,EAAAlc,EAAAmc,YAAA,oBACA,IAAA,EAAAD,EAAA,CACAE,EAAApc,EAAA+W,UAAAmF,GACA,GAAAE,KAAAtW,EAAA,OAAAsW,EAEA,OAAA,KAIA,SAAAC,EAAArc,EAAAtC,GACA,IAGA,GAFA3D,EAAA6S,SAAAlP,IAAA,MAAAA,EAAA,IAAAA,MACAA,EAAAoB,KAAA2R,MAAA/S,IACA3D,EAAA6S,SAAAlP,GAEA,CACA+S,EAAAzQ,SAAAA,EACA,IACAiO,EADAqO,EAAA7L,EAAA/S,EAAAme,EAAA5b,GAEAlE,EAAA,EACA,GAAAugB,EAAA9G,QACA,KAAAzZ,EAAAugB,EAAA9G,QAAA1a,SAAAiB,GACAkS,EAAAgO,EAAAK,EAAA9G,QAAAzZ,KAAA8f,EAAAD,YAAA5b,EAAAsc,EAAA9G,QAAAzZ,MACA6D,EAAAqO,GACA,GAAAqO,EAAA7G,YACA,IAAA1Z,EAAA,EAAAA,EAAAugB,EAAA7G,YAAA3a,SAAAiB,GACAkS,EAAAgO,EAAAK,EAAA7G,YAAA1Z,KAAA8f,EAAAD,YAAA5b,EAAAsc,EAAA7G,YAAA1Z,MACA6D,EAAAqO,GAAA,QAbA4N,EAAAnJ,WAAAhV,EAAAuC,SAAAiS,QAAAxU,EAAAuI,QAeA,MAAA5K,GACA0gB,EAAA1gB,GAEAygB,GAAAS,GACAR,EAAA,KAAAF,GAIA,SAAAjc,EAAAI,EAAAwc,GAGA,KAAAX,EAAAH,MAAA3Q,QAAA/K,GAKA,GAHA6b,EAAAH,MAAAjf,KAAAuD,GAGAA,KAAA8F,EACAgW,EACAO,EAAArc,EAAA8F,EAAA9F,OAEAuc,EACAE,WAAA,aACAF,EACAF,EAAArc,EAAA8F,EAAA9F,YAOA,GAAA8b,EAAA,CACA,IAAApe,EACA,IACAA,EAAA3D,EAAA+F,GAAA4c,aAAA1c,GAAArC,SAAA,QACA,MAAAtC,GAGA,YAFAmhB,GACAT,EAAA1gB,IAGAghB,EAAArc,EAAAtC,SAEA6e,EACAV,EAAAjc,MAAAI,EAAA,SAAA3E,EAAAqC,KACA6e,EAEArc,IAEA7E,EAEAmhB,EAEAD,GACAR,EAAA,KAAAF,GAFAE,EAAA1gB,GAKAghB,EAAArc,EAAAtC,MAIA,IAAA6e,EAAA,EAIAxiB,EAAA6S,SAAA5M,KACAA,EAAA,CAAAA,IACA,IAAA,IAAAiO,EAAAlS,EAAA,EAAAA,EAAAiE,EAAAlF,SAAAiB,GACAkS,EAAA4N,EAAAD,YAAA,GAAA5b,EAAAjE,MACA6D,EAAAqO,GAEA,OAAA6N,EACAD,GACAU,GACAR,EAAA,KAAAF,GACAxiB,KAgCAkW,EAAAnQ,UAAAoQ,SAAA,SAAAxP,EAAAC,GACA,GAAAlG,EAAA4iB,OAEA,OAAAzd,KAAAmQ,KAAArP,EAAAC,EAAA0b,GADA,MAAAze,MAAA,kBAOAqS,EAAAnQ,UAAA4T,WAAA,WACA,GAAA9T,KAAAuc,SAAA3gB,OACA,MAAAoC,MAAA,4BAAAgC,KAAAuc,SAAA5R,IAAA,SAAAd,GACA,MAAA,WAAAA,EAAAsE,OAAA,QAAAtE,EAAAmF,OAAA7E,WACAxM,KAAA,OACA,OAAAiP,EAAA1M,UAAA4T,WAAAnZ,KAAAqF,OAIA,IAAA0d,EAAA,SAUA,SAAAC,EAAAvN,EAAAvG,GACA,IAEA+T,EAFAC,EAAAhU,EAAAmF,OAAA+E,OAAAlK,EAAAsE,QACA,GAAA0P,EAKA,QAJAD,EAAA,IAAA5P,EAAAnE,EAAAM,SAAAN,EAAAxC,GAAAwC,EAAAzC,KAAAyC,EAAAjB,KAAAzO,GAAA0P,EAAA9I,UACA0N,eAAA5E,GACA2E,eAAAoP,EACAC,EAAApQ,IAAAmQ,GACA,EAWAvN,EAAAnQ,UAAAuU,EAAA,SAAAvC,GACA,GAAAA,aAAAlE,EAEAkE,EAAA/D,SAAAhU,IAAA+X,EAAA1D,gBACAmP,EAAA3d,EAAAkS,IACAlS,KAAAuc,SAAAhf,KAAA2U,QAEA,GAAAA,aAAAxI,EAEAgU,EAAAzf,KAAAiU,EAAAzX,QACAyX,EAAAlD,OAAAkD,EAAAzX,MAAAyX,EAAAzJ,aAEA,KAAAyJ,aAAAxB,GAAA,CAEA,GAAAwB,aAAAjE,EACA,IAAA,IAAApR,EAAA,EAAAA,EAAAmD,KAAAuc,SAAA3gB,QACA+hB,EAAA3d,EAAAA,KAAAuc,SAAA1f,IACAmD,KAAAuc,SAAAhc,OAAA1D,EAAA,KAEAA,EACA,IAAA,IAAAQ,EAAA,EAAAA,EAAA6U,EAAAgB,YAAAtX,SAAAyB,EACA2C,KAAAyU,EAAAvC,EAAAW,EAAAxV,IACAqgB,EAAAzf,KAAAiU,EAAAzX,QACAyX,EAAAlD,OAAAkD,EAAAzX,MAAAyX,KAcA7B,EAAAnQ,UAAAwU,EAAA,SAAAxC,GAGA,IAKApW,EAPA,GAAAoW,aAAAlE,EAEAkE,EAAA/D,SAAAhU,KACA+X,EAAA1D,gBACA0D,EAAA1D,eAAAQ,OAAAjB,OAAAmE,EAAA1D,gBACA0D,EAAA1D,eAAA,OAIA,GAFA1S,EAAAkE,KAAAuc,SAAA1Q,QAAAqG,KAGAlS,KAAAuc,SAAAhc,OAAAzE,EAAA,SAIA,GAAAoW,aAAAxI,EAEAgU,EAAAzf,KAAAiU,EAAAzX,cACAyX,EAAAlD,OAAAkD,EAAAzX,WAEA,GAAAyX,aAAAtF,EAAA,CAEA,IAAA,IAAA/P,EAAA,EAAAA,EAAAqV,EAAAgB,YAAAtX,SAAAiB,EACAmD,KAAA0U,EAAAxC,EAAAW,EAAAhW,IAEA6gB,EAAAzf,KAAAiU,EAAAzX,cACAyX,EAAAlD,OAAAkD,EAAAzX,QAMA4V,EAAAL,EAAA,SAAAC,EAAA6N,EAAAC,GACA9P,EAAAgC,EACAsB,EAAAuM,EACAlX,EAAAmX,I,qDCxWA3iB,EAAAR,QAAA,I,wBCKAA,EA6BAgW,QAAAtV,EAAA,K,6BClCAF,EAAAR,QAAAgW,EAEA,IAAA/V,EAAAS,EAAA,IAsCA,SAAAsV,EAAAoN,EAAAC,EAAAC,GAEA,GAAA,mBAAAF,EACA,MAAA9Q,UAAA,8BAEArS,EAAAkF,aAAApF,KAAAqF,MAMAA,KAAAge,QAAAA,EAMAhe,KAAAie,mBAAAA,EAMAje,KAAAke,oBAAAA,IA1DAtN,EAAA1Q,UAAApB,OAAA+N,OAAAhS,EAAAkF,aAAAG,YAAA4M,YAAA8D,GAwEA1Q,UAAAie,QAAA,SAAAA,EAAA/E,EAAAgF,EAAAC,EAAAC,EAAAtd,GAEA,IAAAsd,EACA,MAAApR,UAAA,6BAEA,IAAAyP,EAAA3c,KACA,IAAAgB,EACA,OAAAnG,EAAA8F,UAAAwd,EAAAxB,EAAAvD,EAAAgF,EAAAC,EAAAC,GAEA,IAAA3B,EAAAqB,QAEA,OADAT,WAAA,WAAAvc,EAAAhD,MAAA,mBAAA,GACA7D,GAGA,IACA,OAAAwiB,EAAAqB,QACA5E,EACAgF,EAAAzB,EAAAsB,iBAAA,kBAAA,UAAAK,GAAAzB,SACA,SAAA1gB,EAAAsF,GAEA,GAAAtF,EAEA,OADAwgB,EAAAnc,KAAA,QAAArE,EAAAid,GACApY,EAAA7E,GAGA,GAAA,OAAAsF,EAEA,OADAkb,EAAA1f,KAAA,GACA9C,GAGA,KAAAsH,aAAA4c,GACA,IACA5c,EAAA4c,EAAA1B,EAAAuB,kBAAA,kBAAA,UAAAzc,GACA,MAAAtF,GAEA,OADAwgB,EAAAnc,KAAA,QAAArE,EAAAid,GACApY,EAAA7E,GAKA,OADAwgB,EAAAnc,KAAA,OAAAiB,EAAA2X,GACApY,EAAA,KAAAS,KAGA,MAAAtF,GAGA,OAFAwgB,EAAAnc,KAAA,QAAArE,EAAAid,GACAmE,WAAA,WAAAvc,EAAA7E,IAAA,GACAhC,KASAyW,EAAA1Q,UAAAjD,IAAA,SAAAshB,GAOA,OANAve,KAAAge,UACAO,GACAve,KAAAge,QAAA,KAAA,KAAA,MACAhe,KAAAge,QAAA,KACAhe,KAAAQ,KAAA,OAAAH,OAEAL,O,6BC3IA5E,EAAAR,QAAAgW,EAGA,IAAAhE,EAAAtR,EAAA,IAGAuV,KAFAD,EAAA1Q,UAAApB,OAAA+N,OAAAD,EAAA1M,YAAA4M,YAAA8D,GAAA7D,UAAA,UAEAzR,EAAA,KACAT,EAAAS,EAAA,IACA8V,EAAA9V,EAAA,IAWA,SAAAsV,EAAAnW,EAAAsG,GACA6L,EAAAjS,KAAAqF,KAAAvF,EAAAsG,GAMAf,KAAAqT,QAAA,GAOArT,KAAAwe,EAAA,KAyDA,SAAA1L,EAAAmG,GAEA,OADAA,EAAAuF,EAAA,KACAvF,EA1CArI,EAAAxD,SAAA,SAAA3S,EAAAqM,GACA,IAAAmS,EAAA,IAAArI,EAAAnW,EAAAqM,EAAA/F,SAEA,GAAA+F,EAAAuM,QACA,IAAA,IAAAD,EAAAtU,OAAAC,KAAA+H,EAAAuM,SAAAxW,EAAA,EAAAA,EAAAuW,EAAAxX,SAAAiB,EACAoc,EAAAxL,IAAAoD,EAAAzD,SAAAgG,EAAAvW,GAAAiK,EAAAuM,QAAAD,EAAAvW,MAIA,OAHAiK,EAAAC,QACAkS,EAAAjG,QAAAlM,EAAAC,QACAkS,EAAAjM,QAAAlG,EAAAkG,QACAiM,GAQArI,EAAA1Q,UAAAoN,OAAA,SAAAC,GACA,IAAAkR,EAAA7R,EAAA1M,UAAAoN,OAAA3S,KAAAqF,KAAAuN,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA3S,EAAA+P,SAAA,CACA,UAAA6T,GAAAA,EAAA1d,SAAA5G,GACA,UAAAyS,EAAA8F,YAAA1S,KAAA0e,aAAAnR,IAAA,GACA,SAAAkR,GAAAA,EAAA1X,QAAA5M,GACA,UAAAqT,EAAAxN,KAAAgN,QAAA7S,MAUA2E,OAAA6P,eAAAiC,EAAA1Q,UAAA,eAAA,CACAsJ,IAAA,WACA,OAAAxJ,KAAAwe,IAAAxe,KAAAwe,EAAA3jB,EAAAoY,QAAAjT,KAAAqT,aAYAzC,EAAA1Q,UAAAsJ,IAAA,SAAA/O,GACA,OAAAuF,KAAAqT,QAAA5Y,IACAmS,EAAA1M,UAAAsJ,IAAA7O,KAAAqF,KAAAvF,IAMAmW,EAAA1Q,UAAA4T,WAAA,WAEA,IADA,IAAAT,EAAArT,KAAA0e,aACA7hB,EAAA,EAAAA,EAAAwW,EAAAzX,SAAAiB,EACAwW,EAAAxW,GAAAZ,UACA,OAAA2Q,EAAA1M,UAAAjE,QAAAtB,KAAAqF,OAMA4Q,EAAA1Q,UAAAuN,IAAA,SAAAyE,GAGA,GAAAlS,KAAAwJ,IAAA0I,EAAAzX,MACA,MAAAuD,MAAA,mBAAAkU,EAAAzX,KAAA,QAAAuF,MAEA,OAAAkS,aAAArB,EAGAiC,GAFA9S,KAAAqT,QAAAnB,EAAAzX,MAAAyX,GACAlD,OAAAhP,MAGA4M,EAAA1M,UAAAuN,IAAA9S,KAAAqF,KAAAkS,IAMAtB,EAAA1Q,UAAA6N,OAAA,SAAAmE,GACA,GAAAA,aAAArB,EAAA,CAGA,GAAA7Q,KAAAqT,QAAAnB,EAAAzX,QAAAyX,EACA,MAAAlU,MAAAkU,EAAA,uBAAAlS,MAIA,cAFAA,KAAAqT,QAAAnB,EAAAzX,MACAyX,EAAAlD,OAAA,KACA8D,EAAA9S,MAEA,OAAA4M,EAAA1M,UAAA6N,OAAApT,KAAAqF,KAAAkS,IAUAtB,EAAA1Q,UAAA2M,OAAA,SAAAmR,EAAAC,EAAAC,GAEA,IADA,IACA9E,EADAuF,EAAA,IAAAvN,EAAAR,QAAAoN,EAAAC,EAAAC,GACArhB,EAAA,EAAAA,EAAAmD,KAAA0e,aAAA9iB,SAAAiB,EAAA,CACA,IAAA+hB,EAAA/jB,EAAA8e,SAAAP,EAAApZ,KAAAwe,EAAA3hB,IAAAZ,UAAAxB,MAAA6E,QAAA,WAAA,IACAqf,EAAAC,GAAA/jB,EAAAqD,QAAA,CAAA,IAAA,KAAArD,EAAAgkB,WAAAD,GAAAA,EAAA,IAAAA,EAAA/jB,CAAA,iCAAAA,CAAA,CACAikB,EAAA1F,EACA2F,EAAA3F,EAAA7G,oBAAAhD,KACAyP,EAAA5F,EAAA5G,qBAAAjD,OAGA,OAAAoP,I,+CCpKAvjB,EAAAR,QAAA0W,EAEA,IAAA2N,EAAA,uBACAC,EAAA,kCACAC,EAAA,kCAEAC,EAAA,aACAC,EAAA,aACAC,EAAA,MACAC,EAAA,KACAC,EAAA,UAEAC,EAAA,CACAC,EAAA,KACAC,EAAA,KACAnjB,EAAA,KACAU,EAAA,MAUA,SAAA0iB,EAAAC,GACA,OAAAA,EAAAvgB,QAAAkgB,EAAA,SAAAjgB,EAAAC,GACA,OAAAA,GACA,IAAA,KACA,IAAA,GACA,OAAAA,EACA,QACA,OAAAigB,EAAAjgB,IAAA,MAgEA,SAAA8R,EAAA9S,EAAAqY,GAEArY,EAAAA,EAAAC,WAEA,IAAA5C,EAAA,EACAD,EAAA4C,EAAA5C,OACA4b,EAAA,EACAsI,EAAA,KACA3G,EAAA,KACA4G,EAAA,EACAC,GAAA,EACAC,GAAA,EAEAC,EAAA,GAEAC,EAAA,KASA,SAAA7I,EAAA8I,GACA,OAAApiB,MAAA,WAAAoiB,EAAA,UAAA5I,EAAA,KA0BA,SAAA6I,EAAAhe,GACA,OAAA7D,EAAAA,EAAA6D,IAAA7D,GAWA,SAAA8hB,EAAAtjB,EAAAC,EAAAsjB,GACAT,EAAAthB,EAAAA,EAAAxB,MAAAwB,GACAuhB,EAAAvI,EACAwI,GAAA,EACAC,EAAAM,EACA,IAOAziB,EADA0iB,EAAAxjB,GALA6Z,EACA,EAEA,GAIA,GACA,KAAA2J,EAAA,GACA,OAAA1iB,EAAAU,EAAAA,EAAAgiB,IAAAhiB,IAAA,CACAwhB,GAAA,EACA,aAEA,MAAAliB,GAAA,OAAAA,GAIA,IAHA,IAAA2iB,EAAAjiB,EACAqZ,UAAA7a,EAAAC,GACAyI,MAAA4Z,GACAziB,EAAA,EAAAA,EAAA4jB,EAAA7kB,SAAAiB,EACA4jB,EAAA5jB,GAAA4jB,EAAA5jB,GACAyC,QAAAuX,EAAAwI,EAAAD,EAAA,IACAsB,OACAvH,EAAAsH,EACA9iB,KAAA,MACA+iB,OAGA,SAAAC,EAAAC,GACA,IAAAC,EAAAC,EAAAF,GAGAG,EAAAviB,EAAAqZ,UAAA+I,EAAAC,GAIA,MADA,cAAA5iB,KAAA8iB,GAIA,SAAAD,EAAAE,GAGA,IADA,IAAAH,EAAAG,EACAH,EAAAjlB,GAAA,OAAAykB,EAAAQ,IACAA,IAEA,OAAAA,EAQA,SAAA/J,IACA,GAAA,EAAAoJ,EAAAtkB,OACA,OAAAskB,EAAAra,QACA,GAAAsa,EAAA,CAzFA,IAAAc,EAAA,MAAAd,EAAAhB,EAAAD,EAEAgC,GADAD,EAAAE,UAAAtlB,EAAA,EACAolB,EAAAG,KAAA5iB,IACA,GAAA0iB,EAKA,OAHArlB,EAAAolB,EAAAE,UACA5jB,EAAA4iB,GACAA,EAAA,KACAP,EAAAsB,EAAA,IAJA,MAAA5J,EAAA,UAuFA,IAAA+J,EACA9N,EACA+N,EACAtkB,EACAukB,EACAC,EAAA,IAAA3lB,EACA,EAAA,CACA,GAAAA,IAAAD,EACA,OAAA,KAEA,IADAylB,GAAA,EACA9B,EAAAthB,KAAAqjB,EAAAjB,EAAAxkB,KAKA,GAJA,OAAAylB,IACAE,GAAA,IACAhK,KAEA3b,IAAAD,EACA,OAAA,KAGA,GAAA,MAAAykB,EAAAxkB,GAAA,CACA,KAAAA,IAAAD,EACA,MAAA0b,EAAA,WAEA,GAAA,MAAA+I,EAAAxkB,GACA,GAAAgb,EAeA,CAIA,GADA0K,GAAA,EACAZ,EAFA3jB,EAAAnB,GAIA,IADA0lB,GAAA,GAEA1lB,EAAAilB,EAAAjlB,MACAD,GAIA+kB,IADA9kB,UAGAA,EAAAY,KAAA6f,IAAA1gB,EAAAklB,EAAAjlB,GAAA,GAEA0lB,GACAjB,EAAAtjB,EAAAnB,EAAA2lB,GAEAhK,IACA6J,GAAA,MAnCA,CAIA,IAFAE,EAAA,MAAAlB,EAAArjB,EAAAnB,EAAA,GAEA,OAAAwkB,IAAAxkB,IACA,GAAAA,IAAAD,EACA,OAAA,OAGAC,EACA0lB,GACAjB,EAAAtjB,EAAAnB,EAAA,EAAA2lB,KAEAhK,EACA6J,GAAA,MAuBA,CAAA,GAAA,OAAAC,EAAAjB,EAAAxkB,IAoBA,MAAA,IAlBAmB,EAAAnB,EAAA,EACA0lB,EAAA1K,GAAA,MAAAwJ,EAAArjB,GACA,GAIA,GAHA,OAAAskB,KACA9J,IAEA3b,IAAAD,EACA,MAAA0b,EAAA,iBAEA/D,EAAA+N,EACAA,EAAAjB,EAAAxkB,GACA,MAAA0X,GAAA,MAAA+N,KACAzlB,EACA0lB,GACAjB,EAAAtjB,EAAAnB,EAAA,EAAA2lB,GAEAH,GAAA,UAKAA,GAIA,IAAApkB,EAAApB,EAGA,GAFAojB,EAAAkC,UAAA,GACAlC,EAAAhhB,KAAAoiB,EAAApjB,MAEA,KAAAA,EAAArB,IAAAqjB,EAAAhhB,KAAAoiB,EAAApjB,OACAA,EACAwZ,EAAAjY,EAAAqZ,UAAAhc,EAAAA,EAAAoB,GAGA,MAFA,KAAAwZ,GAAA,KAAAA,IACA0J,EAAA1J,GACAA,EASA,SAAAlZ,EAAAkZ,GACAyJ,EAAA3iB,KAAAkZ,GAQA,SAAAM,IACA,IAAAmJ,EAAAtkB,OAAA,CACA,IAAA6a,EAAAK,IACA,GAAA,OAAAL,EACA,OAAA,KACAlZ,EAAAkZ,GAEA,OAAAyJ,EAAA,GA+CA,OAAAphB,OAAA6P,eAAA,CACAmI,KAAAA,EACAC,KAAAA,EACAxZ,KAAAA,EACAyZ,KAxCA,SAAAyK,EAAA/U,GACA,IAAAgV,EAAA3K,IAEA,GADA2K,IAAAD,EAGA,OADA3K,KACA,EAEA,GAAApK,EAEA,OAAA,EADA,MAAA4K,EAAA,UAAAoK,EAAA,OAAAD,EAAA,eAiCAxK,KAvBA,SAAAwC,GACA,IAAAkI,EAAA,KAcA,OAbAlI,IAAAtf,GACA4lB,IAAAvI,EAAA,IAAAX,GAAA,MAAAiJ,GAAAE,KACA2B,EAAA1B,EAAA9G,EAAA,OAIA4G,EAAAtG,GACA1C,IAEAgJ,IAAAtG,GAAAuG,IAAAnJ,GAAA,MAAAiJ,IACA6B,EAAA1B,EAAA,KAAA9G,IAGAwI,IASA,OAAA,CACAnY,IAAA,WAAA,OAAAgO,KAxWAlG,EAAAsO,SAAAA,G,wBCtCAxkB,EAAAR,QAAAqT,EAGA,IAAArB,EAAAtR,EAAA,IAGAoO,KAFAuE,EAAA/N,UAAApB,OAAA+N,OAAAD,EAAA1M,YAAA4M,YAAAmB,GAAAlB,UAAA,OAEAzR,EAAA,KACAoV,EAAApV,EAAA,IACA0S,EAAA1S,EAAA,IACAqV,EAAArV,EAAA,IACAsV,EAAAtV,EAAA,IACAwV,EAAAxV,EAAA,IACA4V,EAAA5V,EAAA,IACA0V,EAAA1V,EAAA,IACAT,EAAAS,EAAA,IACAiV,EAAAjV,EAAA,IACAkV,EAAAlV,EAAA,IACAmV,EAAAnV,EAAA,IACAgP,EAAAhP,EAAA,IACAyV,EAAAzV,EAAA,IAUA,SAAA2S,EAAAxT,EAAAsG,GACA6L,EAAAjS,KAAAqF,KAAAvF,EAAAsG,GAMAf,KAAAkH,OAAA,GAMAlH,KAAA+H,OAAA5N,GAMA6F,KAAA6Y,WAAA1e,GAMA6F,KAAAmN,SAAAhT,GAMA6F,KAAA+L,MAAA5R,GAOA6F,KAAA4hB,EAAA,KAOA5hB,KAAA4L,EAAA,KAOA5L,KAAA6hB,EAAA,KAOA7hB,KAAA8hB,EAAA,KA0HA,SAAAhP,EAAA1L,GAKA,OAJAA,EAAAwa,EAAAxa,EAAAwE,EAAAxE,EAAAya,EAAA,YACAza,EAAAtK,cACAsK,EAAAvJ,cACAuJ,EAAA6K,OACA7K,EA5HAtI,OAAAyV,iBAAAtG,EAAA/N,UAAA,CAQA6hB,WAAA,CACAvY,IAAA,WAGA,GAAAxJ,KAAA4hB,EACA,OAAA5hB,KAAA4hB,EAEA5hB,KAAA4hB,EAAA,GACA,IAAA,IAAAxO,EAAAtU,OAAAC,KAAAiB,KAAAkH,QAAArK,EAAA,EAAAA,EAAAuW,EAAAxX,SAAAiB,EAAA,CACA,IAAAgN,EAAA7J,KAAAkH,OAAAkM,EAAAvW,IACAwK,EAAAwC,EAAAxC,GAGA,GAAArH,KAAA4hB,EAAAva,GACA,MAAArJ,MAAA,gBAAAqJ,EAAA,OAAArH,MAEAA,KAAA4hB,EAAAva,GAAAwC,EAEA,OAAA7J,KAAA4hB,IAUAnX,YAAA,CACAjB,IAAA,WACA,OAAAxJ,KAAA4L,IAAA5L,KAAA4L,EAAA/Q,EAAAoY,QAAAjT,KAAAkH,WAUA8a,YAAA,CACAxY,IAAA,WACA,OAAAxJ,KAAA6hB,IAAA7hB,KAAA6hB,EAAAhnB,EAAAoY,QAAAjT,KAAA+H,WAUAwH,KAAA,CACA/F,IAAA,WACA,OAAAxJ,KAAA8hB,IAAA9hB,KAAAuP,KAAAtB,EAAAgU,oBAAAjiB,KAAAiO,KAEAuH,IAAA,SAAAjG,GAmBA,IAhBA,IAAArP,EAAAqP,EAAArP,UAeArD,GAdAqD,aAAA4Q,KACAvB,EAAArP,UAAA,IAAA4Q,GAAAhE,YAAAyC,EACA1U,EAAAihB,MAAAvM,EAAArP,UAAAA,IAIAqP,EAAAqC,MAAArC,EAAArP,UAAA0R,MAAA5R,KAGAnF,EAAAihB,MAAAvM,EAAAuB,GAAA,GAEA9Q,KAAA8hB,EAAAvS,EAGA,GACA1S,EAAAmD,KAAAyK,YAAA7O,SAAAiB,EACAmD,KAAA4L,EAAA/O,GAAAZ,UAIA,IADA,IAAAimB,EAAA,GACArlB,EAAA,EAAAA,EAAAmD,KAAAgiB,YAAApmB,SAAAiB,EACAqlB,EAAAliB,KAAA6hB,EAAAhlB,GAAAZ,UAAAxB,MAAA,CACA+O,IAAA3O,EAAA0a,YAAAvV,KAAA6hB,EAAAhlB,GAAAoL,OACAuN,IAAA3a,EAAA4a,YAAAzV,KAAA6hB,EAAAhlB,GAAAoL,QAEApL,GACAiC,OAAAyV,iBAAAhF,EAAArP,UAAAgiB,OAUAjU,EAAAgU,oBAAA,SAAAzX,GAIA,IAFA,IAEAX,EAFAD,EAAA/O,EAAAqD,QAAA,CAAA,KAAAsM,EAAA/P,MAEAoC,EAAA,EAAAA,EAAA2N,EAAAC,YAAA7O,SAAAiB,GACAgN,EAAAW,EAAAoB,EAAA/O,IAAA8N,IAAAf,EACA,YAAA/O,EAAA6P,SAAAb,EAAApP,OACAoP,EAAAI,UAAAL,EACA,YAAA/O,EAAA6P,SAAAb,EAAApP,OACA,OAAAmP,EACA,wEADAA,CAEA,yBA6BAqE,EAAAb,SAAA,SAAA3S,EAAAqM,GAMA,IALA,IAAAM,EAAA,IAAA6G,EAAAxT,EAAAqM,EAAA/F,SAGAqS,GAFAhM,EAAAyR,WAAA/R,EAAA+R,WACAzR,EAAA+F,SAAArG,EAAAqG,SACArO,OAAAC,KAAA+H,EAAAI,SACArK,EAAA,EACAA,EAAAuW,EAAAxX,SAAAiB,EACAuK,EAAAqG,UACA,IAAA3G,EAAAI,OAAAkM,EAAAvW,IAAAgL,QACA8I,EACA3C,GADAZ,SACAgG,EAAAvW,GAAAiK,EAAAI,OAAAkM,EAAAvW,MAEA,GAAAiK,EAAAiB,OACA,IAAAqL,EAAAtU,OAAAC,KAAA+H,EAAAiB,QAAAlL,EAAA,EAAAA,EAAAuW,EAAAxX,SAAAiB,EACAuK,EAAAqG,IAAAiD,EAAAtD,SAAAgG,EAAAvW,GAAAiK,EAAAiB,OAAAqL,EAAAvW,MACA,GAAAiK,EAAAC,OACA,IAAAqM,EAAAtU,OAAAC,KAAA+H,EAAAC,QAAAlK,EAAA,EAAAA,EAAAuW,EAAAxX,SAAAiB,EAAA,CACA,IAAAkK,EAAAD,EAAAC,OAAAqM,EAAAvW,IACAuK,EAAAqG,KACA1G,EAAAM,KAAAlN,GACA6T,EACAjH,EAAAG,SAAA/M,GACA8T,EACAlH,EAAA0B,SAAAtO,GACAuP,EACA3C,EAAAsM,UAAAlZ,GACAyW,EACAhE,GAPAQ,SAOAgG,EAAAvW,GAAAkK,IAWA,OARAD,EAAA+R,YAAA/R,EAAA+R,WAAAjd,SACAwL,EAAAyR,WAAA/R,EAAA+R,YACA/R,EAAAqG,UAAArG,EAAAqG,SAAAvR,SACAwL,EAAA+F,SAAArG,EAAAqG,UACArG,EAAAiF,QACA3E,EAAA2E,OAAA,GACAjF,EAAAkG,UACA5F,EAAA4F,QAAAlG,EAAAkG,SACA5F,GAQA6G,EAAA/N,UAAAoN,OAAA,SAAAC,GACA,IAAAkR,EAAA7R,EAAA1M,UAAAoN,OAAA3S,KAAAqF,KAAAuN,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA3S,EAAA+P,SAAA,CACA,UAAA6T,GAAAA,EAAA1d,SAAA5G,GACA,SAAAyS,EAAA8F,YAAA1S,KAAAgiB,YAAAzU,GACA,SAAAX,EAAA8F,YAAA1S,KAAAyK,YAAAqB,OAAA,SAAA8G,GAAA,OAAAA,EAAAnE,iBAAAlB,IAAA,GACA,aAAAvN,KAAA6Y,YAAA7Y,KAAA6Y,WAAAjd,OAAAoE,KAAA6Y,WAAA1e,GACA,WAAA6F,KAAAmN,UAAAnN,KAAAmN,SAAAvR,OAAAoE,KAAAmN,SAAAhT,GACA,QAAA6F,KAAA+L,OAAA5R,GACA,SAAAskB,GAAAA,EAAA1X,QAAA5M,GACA,UAAAqT,EAAAxN,KAAAgN,QAAA7S,MAOA8T,EAAA/N,UAAA4T,WAAA,WAEA,IADA,IAAA5M,EAAAlH,KAAAyK,YAAA5N,EAAA,EACAA,EAAAqK,EAAAtL,QACAsL,EAAArK,KAAAZ,UAEA,IADA,IAAA8L,EAAA/H,KAAAgiB,YAAAnlB,EAAA,EACAA,EAAAkL,EAAAnM,QACAmM,EAAAlL,KAAAZ,UACA,OAAA2Q,EAAA1M,UAAA4T,WAAAnZ,KAAAqF,OAMAiO,EAAA/N,UAAAsJ,IAAA,SAAA/O,GACA,OAAAuF,KAAAkH,OAAAzM,IACAuF,KAAA+H,QAAA/H,KAAA+H,OAAAtN,IACAuF,KAAA+G,QAAA/G,KAAA+G,OAAAtM,IACA,MAUAwT,EAAA/N,UAAAuN,IAAA,SAAAyE,GAEA,GAAAlS,KAAAwJ,IAAA0I,EAAAzX,MACA,MAAAuD,MAAA,mBAAAkU,EAAAzX,KAAA,QAAAuF,MAEA,GAAAkS,aAAAlE,GAAAkE,EAAA/D,SAAAhU,GAAA,CAMA,IAAA6F,KAAA4hB,GAAA5hB,KAAA+hB,YAAA7P,EAAA7K,IACA,MAAArJ,MAAA,gBAAAkU,EAAA7K,GAAA,OAAArH,MACA,GAAAA,KAAA4N,aAAAsE,EAAA7K,IACA,MAAArJ,MAAA,MAAAkU,EAAA7K,GAAA,mBAAArH,MACA,GAAAA,KAAA6N,eAAAqE,EAAAzX,MACA,MAAAuD,MAAA,SAAAkU,EAAAzX,KAAA,oBAAAuF,MAOA,OALAkS,EAAAlD,QACAkD,EAAAlD,OAAAjB,OAAAmE,IACAlS,KAAAkH,OAAAgL,EAAAzX,MAAAyX,GACA5D,QAAAtO,KACAkS,EAAAuB,MAAAzT,MACA8S,EAAA9S,MAEA,OAAAkS,aAAAxB,GACA1Q,KAAA+H,SACA/H,KAAA+H,OAAA,KACA/H,KAAA+H,OAAAmK,EAAAzX,MAAAyX,GACAuB,MAAAzT,MACA8S,EAAA9S,OAEA4M,EAAA1M,UAAAuN,IAAA9S,KAAAqF,KAAAkS,IAUAjE,EAAA/N,UAAA6N,OAAA,SAAAmE,GACA,GAAAA,aAAAlE,GAAAkE,EAAA/D,SAAAhU,GAAA,CAIA,GAAA6F,KAAAkH,QAAAlH,KAAAkH,OAAAgL,EAAAzX,QAAAyX,EAMA,cAHAlS,KAAAkH,OAAAgL,EAAAzX,MACAyX,EAAAlD,OAAA,KACAkD,EAAAwB,SAAA1T,MACA8S,EAAA9S,MALA,MAAAhC,MAAAkU,EAAA,uBAAAlS,MAOA,GAAAkS,aAAAxB,EAAA,CAGA,GAAA1Q,KAAA+H,QAAA/H,KAAA+H,OAAAmK,EAAAzX,QAAAyX,EAMA,cAHAlS,KAAA+H,OAAAmK,EAAAzX,MACAyX,EAAAlD,OAAA,KACAkD,EAAAwB,SAAA1T,MACA8S,EAAA9S,MALA,MAAAhC,MAAAkU,EAAA,uBAAAlS,MAOA,OAAA4M,EAAA1M,UAAA6N,OAAApT,KAAAqF,KAAAkS,IAQAjE,EAAA/N,UAAA0N,aAAA,SAAAvG,GACA,OAAAuF,EAAAgB,aAAA5N,KAAAmN,SAAA9F,IAQA4G,EAAA/N,UAAA2N,eAAA,SAAApT,GACA,OAAAmS,EAAAiB,eAAA7N,KAAAmN,SAAA1S,IAQAwT,EAAA/N,UAAA2M,OAAA,SAAA8E,GACA,OAAA,IAAA3R,KAAAuP,KAAAoC,IAOA1D,EAAA/N,UAAAiiB,MAAA,WAMA,IAFA,IAAAhY,EAAAnK,KAAAmK,SACA8B,EAAA,GACApP,EAAA,EAAAA,EAAAmD,KAAAyK,YAAA7O,SAAAiB,EACAoP,EAAA1O,KAAAyC,KAAA4L,EAAA/O,GAAAZ,UAAA+N,cAGAhK,KAAAlD,OAAAyT,EAAAvQ,KAAAuQ,CAAA,CACAS,OAAAA,EACA/E,MAAAA,EACApR,KAAAA,IAEAmF,KAAAnC,OAAA2S,EAAAxQ,KAAAwQ,CAAA,CACAU,OAAAA,EACAjF,MAAAA,EACApR,KAAAA,IAEAmF,KAAAiS,OAAAxB,EAAAzQ,KAAAyQ,CAAA,CACAxE,MAAAA,EACApR,KAAAA,IAEAmF,KAAAuK,WAAAD,EAAAC,WAAAvK,KAAAsK,CAAA,CACA2B,MAAAA,EACApR,KAAAA,IAEAmF,KAAA4K,SAAAN,EAAAM,SAAA5K,KAAAsK,CAAA,CACA2B,MAAAA,EACApR,KAAAA,IAIA,IAEAunB,EAFAC,EAAAtR,EAAA5G,GAaA,OAZAkY,KACAD,EAAAtjB,OAAA+N,OAAA7M,OAEAuK,WAAAvK,KAAAuK,WACAvK,KAAAuK,WAAA8X,EAAA9X,WAAA9F,KAAA2d,GAGAA,EAAAxX,SAAA5K,KAAA4K,SACA5K,KAAA4K,SAAAyX,EAAAzX,SAAAnG,KAAA2d,IAIApiB,MASAiO,EAAA/N,UAAApD,OAAA,SAAAwR,EAAAuD,GACA,OAAA7R,KAAAmiB,QAAArlB,OAAAwR,EAAAuD,IASA5D,EAAA/N,UAAA4R,gBAAA,SAAAxD,EAAAuD,GACA,OAAA7R,KAAAlD,OAAAwR,EAAAuD,GAAAA,EAAAtL,IAAAsL,EAAAyQ,OAAAzQ,GAAA0Q,UAWAtU,EAAA/N,UAAArC,OAAA,SAAAkU,EAAAnW,GACA,OAAAoE,KAAAmiB,QAAAtkB,OAAAkU,EAAAnW,IAUAqS,EAAA/N,UAAA8R,gBAAA,SAAAD,GAGA,OAFAA,aAAAb,IACAa,EAAAb,EAAArE,OAAAkF,IACA/R,KAAAnC,OAAAkU,EAAAA,EAAAqJ,WAQAnN,EAAA/N,UAAA+R,OAAA,SAAA3D,GACA,OAAAtO,KAAAmiB,QAAAlQ,OAAA3D,IAQAL,EAAA/N,UAAAqK,WAAA,SAAA2H,GACA,OAAAlS,KAAAmiB,QAAA5X,WAAA2H,IA4BAjE,EAAA/N,UAAA0K,SAAA,SAAA0D,EAAAvN,GACA,OAAAf,KAAAmiB,QAAAvX,SAAA0D,EAAAvN,IAkBAkN,EAAAuB,EAAA,SAAAgT,GACA,OAAA,SAAAvK,GACApd,EAAA+U,aAAAqI,EAAAuK,M,iHCpkBA,IAEA3nB,EAAAS,EAAA,IAEA0jB,EAAA,CACA,SACA,QACA,QACA,SACA,SACA,UACA,WACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,SAGA,SAAAyD,EAAAha,EAAA5M,GACA,IAAAgB,EAAA,EAAA6lB,EAAA,GAEA,IADA7mB,GAAA,EACAgB,EAAA4L,EAAA7M,QAAA8mB,EAAA1D,EAAAniB,EAAAhB,IAAA4M,EAAA5L,KACA,OAAA6lB,EAuBAzW,EAAAE,MAAAsW,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,IAwBAxW,EAAAC,SAAAuW,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GACA,EACA,GACA5nB,EAAAyU,WACA,OAaArD,EAAAZ,KAAAoX,EAAA,CACA,EACA,EACA,EACA,EACA,GACA,GAmBAxW,EAAAO,OAAAiW,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GACA,GAoBAxW,EAAAG,OAAAqW,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,K,6BC5LA,IAIAxU,EACAvE,EALA7O,EAAAO,EAAAR,QAAAU,EAAA,IAEA+V,EAAA/V,EAAA,IAiDAqnB,GA5CA9nB,EAAAqD,QAAA5C,EAAA,GACAT,EAAA6F,MAAApF,EAAA,GACAT,EAAA2K,KAAAlK,EAAA,GAMAT,EAAA+F,GAAA/F,EAAAgG,QAAA,MAOAhG,EAAAoY,QAAA,SAAAf,GACA,GAAAA,EAAA,CAIA,IAHA,IAAAnT,EAAAD,OAAAC,KAAAmT,GACAS,EAAAjX,MAAAqD,EAAAnD,QACAE,EAAA,EACAA,EAAAiD,EAAAnD,QACA+W,EAAA7W,GAAAoW,EAAAnT,EAAAjD,MACA,OAAA6W,EAEA,MAAA,IAQA9X,EAAA+P,SAAA,SAAA+H,GAGA,IAFA,IAAAT,EAAA,GACApW,EAAA,EACAA,EAAA6W,EAAA/W,QAAA,CACA,IAAAgnB,EAAAjQ,EAAA7W,KACAqG,EAAAwQ,EAAA7W,KACAqG,IAAAhI,KACA+X,EAAA0Q,GAAAzgB,GAEA,OAAA+P,GAGA,OACA2Q,EAAA,KA+BAC,GAxBAjoB,EAAAgkB,WAAA,SAAApkB,GACA,MAAA,uTAAAwD,KAAAxD,IAQAI,EAAA6P,SAAA,SAAAX,GACA,OAAA,YAAA9L,KAAA8L,IAAAlP,EAAAgkB,WAAA9U,GACA,KAAAA,EAAAzK,QAAAqjB,EAAA,QAAArjB,QAAAujB,EAAA,OAAA,KACA,IAAA9Y,GAQAlP,EAAA+e,QAAA,SAAAiG,GACA,OAAAA,EAAA,IAAAA,IAAAkD,cAAAlD,EAAAhI,UAAA,IAGA,aAuDAmL,GAhDAnoB,EAAAwc,UAAA,SAAAwI,GACA,OAAAA,EAAAhI,UAAA,EAAA,GACAgI,EAAAhI,UAAA,GACAvY,QAAAwjB,EAAA,SAAAvjB,EAAAC,GAAA,OAAAA,EAAAujB,iBASAloB,EAAAiQ,kBAAA,SAAAmY,EAAA3lB,GACA,OAAA2lB,EAAA5b,GAAA/J,EAAA+J,IAWAxM,EAAA+U,aAAA,SAAAL,EAAAiT,GAGA,GAAAjT,EAAAqC,MAMA,OALA4Q,GAAAjT,EAAAqC,MAAAnX,OAAA+nB,IACA3nB,EAAAqoB,aAAAnV,OAAAwB,EAAAqC,OACArC,EAAAqC,MAAAnX,KAAA+nB,EACA3nB,EAAAqoB,aAAAzV,IAAA8B,EAAAqC,QAEArC,EAAAqC,MAOAxK,EAAA,IAFA6G,EADAA,GACA3S,EAAA,KAEAknB,GAAAjT,EAAA9U,MAKA,OAJAI,EAAAqoB,aAAAzV,IAAArG,GACAA,EAAAmI,KAAAA,EACAzQ,OAAA6P,eAAAY,EAAA,QAAA,CAAA9P,MAAA2H,EAAA+b,YAAA,IACArkB,OAAA6P,eAAAY,EAAArP,UAAA,QAAA,CAAAT,MAAA2H,EAAA+b,YAAA,IACA/b,GAGA,GAOAvM,EAAAgV,aAAA,SAAAqC,GAGA,GAAAA,EAAAN,MACA,OAAAM,EAAAN,MAMA,IAAAvE,EAAA,IAFA3D,EADAA,GACApO,EAAA,KAEA,OAAA0nB,IAAA9Q,GAGA,OAFArX,EAAAqoB,aAAAzV,IAAAJ,GACAvO,OAAA6P,eAAAuD,EAAA,QAAA,CAAAzS,MAAA4N,EAAA8V,YAAA,IACA9V,GAWAxS,EAAAqa,YAAA,SAAAkO,EAAA5d,EAAA/F,GAiBA,GAAA,iBAAA2jB,EACA,MAAAlW,UAAA,yBACA,GAAA1H,EAIA,OAtBA,SAAA6d,EAAAD,EAAA5d,EAAA/F,GACA,IAAAoU,EAAArO,EAAAK,QACA,MAAA,cAAAgO,GAAA,cAAAA,IAGA,EAAArO,EAAA5J,OACAwnB,EAAAvP,GAAAwP,EAAAD,EAAAvP,IAAA,GAAArO,EAAA/F,KAEA0a,EAAAiJ,EAAAvP,MAEApU,EAAA,GAAA2a,OAAAD,GAAAC,OAAA3a,IACA2jB,EAAAvP,GAAApU,IARA2jB,EAmBAC,CAAAD,EADA5d,EAAAA,EAAAE,MAAA,KACAjG,GAHA,MAAAyN,UAAA,2BAYApO,OAAA6P,eAAA9T,EAAA,eAAA,CACA2O,IAAA,WACA,OAAA6H,EAAA,YAAAA,EAAA,UAAA,IAAA/V,EAAA,U,iEChNAF,EAAAR,QAAA2f,EAEA,IAAA1f,EAAAS,EAAA,IAUA,SAAAif,EAAAzW,EAAAC,GASA/D,KAAA8D,GAAAA,IAAA,EAMA9D,KAAA+D,GAAAA,IAAA,EAQA,IAAAuf,EAAA/I,EAAA+I,KAAA,IAAA/I,EAAA,EAAA,GAoFAxc,GAlFAulB,EAAA7X,SAAA,WAAA,OAAA,GACA6X,EAAAC,SAAAD,EAAApH,SAAA,WAAA,OAAAlc,MACAsjB,EAAA1nB,OAAA,WAAA,OAAA,GAOA2e,EAAAiJ,SAAA,mBAOAjJ,EAAArL,WAAA,SAAAzP,GACA,GAAA,IAAAA,EACA,OAAA6jB,EACA,IAAAhhB,EAAA7C,EAAA,EAGAqE,GADArE,EADA6C,GACA7C,EACAA,KAAA,EACAsE,GAAAtE,EAAAqE,GAAA,aAAA,EAUA,OATAxB,IACAyB,GAAAA,IAAA,EACAD,GAAAA,IAAA,EACA,aAAAA,IACAA,EAAA,EACA,aAAAC,IACAA,EAAA,KAGA,IAAAwW,EAAAzW,EAAAC,IAQAwW,EAAAkJ,KAAA,SAAAhkB,GACA,GAAA,iBAAAA,EACA,OAAA8a,EAAArL,WAAAzP,GACA,GAAA5E,EAAA6S,SAAAjO,GAAA,CAEA,IAAA5E,EAAAI,KAGA,OAAAsf,EAAArL,WAAA4I,SAAArY,EAAA,KAFAA,EAAA5E,EAAAI,KAAAyoB,WAAAjkB,GAIA,OAAAA,EAAA6L,KAAA7L,EAAA8L,KAAA,IAAAgP,EAAA9a,EAAA6L,MAAA,EAAA7L,EAAA8L,OAAA,GAAA+X,GAQA/I,EAAAra,UAAAuL,SAAA,SAAAD,GACA,IAEAzH,EAFA,OAAAyH,GAAAxL,KAAA+D,KAAA,IACAD,EAAA,GAAA9D,KAAA8D,KAAA,EACAC,GAAA/D,KAAA+D,KAAA,IAGAD,EAAA,YADAC,EADAD,EAEAC,EADAA,EAAA,IAAA,KAGA/D,KAAA8D,GAAA,WAAA9D,KAAA+D,IAQAwW,EAAAra,UAAAyjB,OAAA,SAAAnY,GACA,OAAA3Q,EAAAI,KACA,IAAAJ,EAAAI,KAAA,EAAA+E,KAAA8D,GAAA,EAAA9D,KAAA+D,KAAAyH,GAEA,CAAAF,IAAA,EAAAtL,KAAA8D,GAAAyH,KAAA,EAAAvL,KAAA+D,GAAAyH,WAAAA,IAGAhO,OAAA0C,UAAAnC,YAOAwc,EAAAqJ,SAAA,SAAAC,GACA,MAjFAtJ,qBAiFAsJ,EACAP,EACA,IAAA/I,GACAxc,EAAApD,KAAAkpB,EAAA,GACA9lB,EAAApD,KAAAkpB,EAAA,IAAA,EACA9lB,EAAApD,KAAAkpB,EAAA,IAAA,GACA9lB,EAAApD,KAAAkpB,EAAA,IAAA,MAAA,GAEA9lB,EAAApD,KAAAkpB,EAAA,GACA9lB,EAAApD,KAAAkpB,EAAA,IAAA,EACA9lB,EAAApD,KAAAkpB,EAAA,IAAA,GACA9lB,EAAApD,KAAAkpB,EAAA,IAAA,MAAA,IAQAtJ,EAAAra,UAAA4jB,OAAA,WACA,OAAAtmB,OAAAC,aACA,IAAAuC,KAAA8D,GACA9D,KAAA8D,KAAA,EAAA,IACA9D,KAAA8D,KAAA,GAAA,IACA9D,KAAA8D,KAAA,GACA,IAAA9D,KAAA+D,GACA/D,KAAA+D,KAAA,EAAA,IACA/D,KAAA+D,KAAA,GAAA,IACA/D,KAAA+D,KAAA,KAQAwW,EAAAra,UAAAqjB,SAAA,WACA,IAAAQ,EAAA/jB,KAAA+D,IAAA,GAGA,OAFA/D,KAAA+D,KAAA/D,KAAA+D,IAAA,EAAA/D,KAAA8D,KAAA,IAAAigB,KAAA,EACA/jB,KAAA8D,IAAA9D,KAAA8D,IAAA,EAAAigB,KAAA,EACA/jB,MAOAua,EAAAra,UAAAgc,SAAA,WACA,IAAA6H,IAAA,EAAA/jB,KAAA8D,IAGA,OAFA9D,KAAA8D,KAAA9D,KAAA8D,KAAA,EAAA9D,KAAA+D,IAAA,IAAAggB,KAAA,EACA/jB,KAAA+D,IAAA/D,KAAA+D,KAAA,EAAAggB,KAAA,EACA/jB,MAOAua,EAAAra,UAAAtE,OAAA,WACA,IAAAooB,EAAAhkB,KAAA8D,GACAmgB,GAAAjkB,KAAA8D,KAAA,GAAA9D,KAAA+D,IAAA,KAAA,EACAmgB,EAAAlkB,KAAA+D,KAAA,GACA,OAAA,GAAAmgB,EACA,GAAAD,EACAD,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,IAAA,EAAA,K,6BCrMA,IAAArpB,EAAAD,EA2OA,SAAAkhB,EAAAsH,EAAAe,EAAArV,GACA,IAAA,IAAA/P,EAAAD,OAAAC,KAAAolB,GAAAtnB,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACAumB,EAAArkB,EAAAlC,MAAA1C,IAAA2U,IACAsU,EAAArkB,EAAAlC,IAAAsnB,EAAAplB,EAAAlC,KACA,OAAAumB,EAoBA,SAAAgB,EAAA3pB,GAEA,SAAA4pB,EAAA/V,EAAAqD,GAEA,KAAA3R,gBAAAqkB,GACA,OAAA,IAAAA,EAAA/V,EAAAqD,GAKA7S,OAAA6P,eAAA3O,KAAA,UAAA,CAAAwJ,IAAA,WAAA,OAAA8E,KAGAtQ,MAAAsmB,kBACAtmB,MAAAsmB,kBAAAtkB,KAAAqkB,GAEAvlB,OAAA6P,eAAA3O,KAAA,QAAA,CAAAP,MAAAzB,QAAAkiB,OAAA,KAEAvO,GACAmK,EAAA9b,KAAA2R,GAWA,OARA0S,EAAAnkB,UAAApB,OAAA+N,OAAA7O,MAAAkC,YAAA4M,YAAAuX,EAEAvlB,OAAA6P,eAAA0V,EAAAnkB,UAAA,OAAA,CAAAsJ,IAAA,WAAA,OAAA/O,KAEA4pB,EAAAnkB,UAAAzB,SAAA,WACA,OAAAuB,KAAAvF,KAAA,KAAAuF,KAAAsO,SAGA+V,EA9RAxpB,EAAA8F,UAAArF,EAAA,GAGAT,EAAAwB,OAAAf,EAAA,GAGAT,EAAAkF,aAAAzE,EAAA,GAGAT,EAAA6gB,MAAApgB,EAAA,GAGAT,EAAAgG,QAAAvF,EAAA,GAGAT,EAAAyL,KAAAhL,EAAA,IAGAT,EAAA0pB,KAAAjpB,EAAA,GAGAT,EAAA0f,SAAAjf,EAAA,IAOAT,EAAA4iB,UAAA,oBAAA3iB,QACAA,QACAA,OAAAqiB,SACAriB,OAAAqiB,QAAAqH,UACA1pB,OAAAqiB,QAAAqH,SAAAC,MAOA5pB,EAAAC,OAAAD,EAAA4iB,QAAA3iB,QACA,oBAAA4pB,QAAAA,QACA,oBAAA/H,MAAAA,MACA3c,KAQAnF,EAAAyU,WAAAxQ,OAAAqQ,OAAArQ,OAAAqQ,OAAA,IAAA,GAOAtU,EAAAwU,YAAAvQ,OAAAqQ,OAAArQ,OAAAqQ,OAAA,IAAA,GAQAtU,EAAA8S,UAAAjO,OAAAiO,WAAA,SAAAlO,GACA,MAAA,iBAAAA,GAAAklB,SAAAllB,IAAAhD,KAAAkD,MAAAF,KAAAA,GAQA5E,EAAA6S,SAAA,SAAAjO,GACA,MAAA,iBAAAA,GAAAA,aAAAjC,QAQA3C,EAAAuT,SAAA,SAAA3O,GACA,OAAAA,GAAA,iBAAAA,GAWA5E,EAAA+pB,MAQA/pB,EAAAgqB,MAAA,SAAAjS,EAAA7I,GACA,IAAAtK,EAAAmT,EAAA7I,GACA,OAAA,MAAAtK,GAAAmT,EAAAoC,eAAAjL,KACA,iBAAAtK,GAAA,GAAA/D,MAAAiY,QAAAlU,GAAAA,EAAAX,OAAAC,KAAAU,IAAA7D,SAeAf,EAAA8f,OAAA,WACA,IACA,IAAAA,EAAA9f,EAAAgG,QAAA,UAAA8Z,OAEA,OAAAA,EAAAza,UAAA4kB,UAAAnK,EAAA,KACA,MAAArV,GAEA,OAAA,MAPA,GAYAzK,EAAAkqB,EAAA,KAGAlqB,EAAAmqB,EAAA,KAOAnqB,EAAAuU,UAAA,SAAA6V,GAEA,MAAA,iBAAAA,EACApqB,EAAA8f,OACA9f,EAAAmqB,EAAAC,GACA,IAAApqB,EAAAa,MAAAupB,GACApqB,EAAA8f,OACA9f,EAAAkqB,EAAAE,GACA,oBAAAtjB,WACAsjB,EACA,IAAAtjB,WAAAsjB,IAOApqB,EAAAa,MAAA,oBAAAiG,WAAAA,WAAAjG,MAeAb,EAAAI,KAAAJ,EAAAC,OAAAoqB,SAAArqB,EAAAC,OAAAoqB,QAAAjqB,MACAJ,EAAAC,OAAAG,MACAJ,EAAAgG,QAAA,QAOAhG,EAAAsqB,OAAA,mBAOAtqB,EAAAuqB,QAAA,wBAOAvqB,EAAAwqB,QAAA,6CAOAxqB,EAAAyqB,WAAA,SAAA7lB,GACA,OAAAA,EACA5E,EAAA0f,SAAAkJ,KAAAhkB,GAAAqkB,SACAjpB,EAAA0f,SAAAiJ,UASA3oB,EAAA0qB,aAAA,SAAA1B,EAAArY,GACAuP,EAAAlgB,EAAA0f,SAAAqJ,SAAAC,GACA,OAAAhpB,EAAAI,KACAJ,EAAAI,KAAAuqB,SAAAzK,EAAAjX,GAAAiX,EAAAhX,GAAAyH,GACAuP,EAAAtP,WAAAD,IAkBA3Q,EAAAihB,MAAAA,EAOAjhB,EAAA8e,QAAA,SAAAkG,GACA,OAAAA,EAAA,IAAAA,IAAAxR,cAAAwR,EAAAhI,UAAA,IA0CAhd,EAAAupB,SAAAA,EAmBAvpB,EAAA4qB,cAAArB,EAAA,iBAoBAvpB,EAAA0a,YAAA,SAAAH,GAEA,IADA,IAAAsQ,EAAA,GACA7oB,EAAA,EAAAA,EAAAuY,EAAAxZ,SAAAiB,EACA6oB,EAAAtQ,EAAAvY,IAAA,EAOA,OAAA,WACA,IAAA,IAAAkC,EAAAD,OAAAC,KAAAiB,MAAAnD,EAAAkC,EAAAnD,OAAA,GAAA,EAAAiB,IAAAA,EACA,GAAA,IAAA6oB,EAAA3mB,EAAAlC,KAAAmD,KAAAjB,EAAAlC,MAAA1C,IAAA,OAAA6F,KAAAjB,EAAAlC,IACA,OAAAkC,EAAAlC,KAiBAhC,EAAA4a,YAAA,SAAAL,GAQA,OAAA,SAAA3a,GACA,IAAA,IAAAoC,EAAA,EAAAA,EAAAuY,EAAAxZ,SAAAiB,EACAuY,EAAAvY,KAAApC,UACAuF,KAAAoV,EAAAvY,MAoBAhC,EAAA0S,cAAA,CACAoY,MAAAnoB,OACAooB,MAAApoB,OACAkO,MAAAlO,OACAsJ,MAAA,GAIAjM,EAAAmV,EAAA,WACA,IAAA2K,EAAA9f,EAAA8f,OAEAA,GAMA9f,EAAAkqB,EAAApK,EAAA8I,OAAA9hB,WAAA8hB,MAAA9I,EAAA8I,MAEA,SAAAhkB,EAAAomB,GACA,OAAA,IAAAlL,EAAAlb,EAAAomB,IAEAhrB,EAAAmqB,EAAArK,EAAAmL,aAEA,SAAA5f,GACA,OAAA,IAAAyU,EAAAzU,KAbArL,EAAAkqB,EAAAlqB,EAAAmqB,EAAA,O,2DCpZA5pB,EAAAR,QAwHA,SAAA4P,GAGA,IAAAZ,EAAA/O,EAAAqD,QAAA,CAAA,KAAAsM,EAAA/P,KAAA,UAAAI,CACA,oCADAA,CAEA,WAAA,mBACAkN,EAAAyC,EAAAwX,YACA+D,EAAA,GACAhe,EAAAnM,QAAAgO,EACA,YAEA,IAAA,IAAA/M,EAAA,EAAAA,EAAA2N,EAAAC,YAAA7O,SAAAiB,EAAA,CACA,IA2BAmpB,EA3BAnc,EAAAW,EAAAoB,EAAA/O,GAAAZ,UACA+P,EAAA,IAAAnR,EAAA6P,SAAAb,EAAApP,MAEAoP,EAAA6C,UAAA9C,EACA,sCAAAoC,EAAAnC,EAAApP,MAGAoP,EAAAc,KAAAf,EACA,yBAAAoC,EADApC,CAEA,WAAAqc,EAAApc,EAAA,UAFAD,CAGA,wBAAAoC,EAHApC,CAIA,gCAxDA,SAAAA,EAAAC,EAAAmC,GAEA,OAAAnC,EAAAhC,SACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAA+B,EACA,6BAAAoC,EADApC,CAEA,WAAAqc,EAAApc,EAAA,gBACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,6BAAAoC,EADApC,CAEA,WAAAqc,EAAApc,EAAA,qBACA,MACA,IAAA,OAAAD,EACA,4BAAAoC,EADApC,CAEA,WAAAqc,EAAApc,EAAA,iBAoCAqc,CAAAtc,EAAAC,EAAA,QACAsc,EAAAvc,EAAAC,EAAAhN,EAAAmP,EAAA,SAAAma,CACA,MAGAtc,EAAAI,UAAAL,EACA,yBAAAoC,EADApC,CAEA,WAAAqc,EAAApc,EAAA,SAFAD,CAGA,gCAAAoC,GACAma,EAAAvc,EAAAC,EAAAhN,EAAAmP,EAAA,MAAAma,CACA,OAIAtc,EAAAqB,SACA8a,EAAAnrB,EAAA6P,SAAAb,EAAAqB,OAAAzQ,MACA,IAAAsrB,EAAAlc,EAAAqB,OAAAzQ,OAAAmP,EACA,cAAAoc,EADApc,CAEA,WAAAC,EAAAqB,OAAAzQ,KAAA,qBACAsrB,EAAAlc,EAAAqB,OAAAzQ,MAAA,EACAmP,EACA,QAAAoc,IAEAG,EAAAvc,EAAAC,EAAAhN,EAAAmP,IAEAnC,EAAA6C,UAAA9C,EACA,KAEA,OAAAA,EACA,gBA3KA,IAAAF,EAAApO,EAAA,IACAT,EAAAS,EAAA,IAEA,SAAA2qB,EAAApc,EAAA4X,GACA,OAAA5X,EAAApP,KAAA,KAAAgnB,GAAA5X,EAAAI,UAAA,UAAAwX,EAAA,KAAA5X,EAAAc,KAAA,WAAA8W,EAAA,MAAA5X,EAAAhC,QAAA,IAAA,IAAA,YAYA,SAAAse,EAAAvc,EAAAC,EAAAC,EAAAkC,GAEA,GAAAnC,EAAAG,aACA,GAAAH,EAAAG,wBAAAN,EAAA,CAAAE,EACA,cAAAoC,EADApC,CAEA,WAFAA,CAGA,WAAAqc,EAAApc,EAAA,eACA,IAAA,IAAA9K,EAAAD,OAAAC,KAAA8K,EAAAG,aAAAvB,QAAApL,EAAA,EAAAA,EAAA0B,EAAAnD,SAAAyB,EAAAuM,EACA,WAAAC,EAAAG,aAAAvB,OAAA1J,EAAA1B,KACAuM,EACA,QADAA,CAEA,UAEAA,EACA,IADAA,CAEA,8BAAAE,EAAAkC,EAFApC,CAGA,QAHAA,CAIA,aAAAC,EAAApP,KAAA,IAJAmP,CAKA,UAGA,OAAAC,EAAAzC,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAwC,EACA,0BAAAoC,EADApC,CAEA,WAAAqc,EAAApc,EAAA,YACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,kFAAAoC,EAAAA,EAAAA,EAAAA,EADApC,CAEA,WAAAqc,EAAApc,EAAA,iBACA,MACA,IAAA,QACA,IAAA,SAAAD,EACA,2BAAAoC,EADApC,CAEA,WAAAqc,EAAApc,EAAA,WACA,MACA,IAAA,OAAAD,EACA,4BAAAoC,EADApC,CAEA,WAAAqc,EAAApc,EAAA,YACA,MACA,IAAA,SAAAD,EACA,yBAAAoC,EADApC,CAEA,WAAAqc,EAAApc,EAAA,WACA,MACA,IAAA,QAAAD,EACA,4DAAAoC,EAAAA,EAAAA,EADApC,CAEA,WAAAqc,EAAApc,EAAA,WAIA,OAAAD,I,mCCrEA,IAEAkH,EAAAxV,EAAA,IA6BAyV,EAAA,wBAAA,CAEAxG,WAAA,SAAA2H,GAGA,GAAAA,GAAAA,EAAA,SAAA,CAEA,IAKA/K,EALA1M,EAAAyX,EAAA,SAAA2F,UAAA,EAAA3F,EAAA,SAAA+K,YAAA,MACA7V,EAAApH,KAAA+T,OAAAtZ,GAEA,GAAA2M,EAQA,QANAD,EAAA,MAAA+K,EAAA,SAAA,IAAAA,IACAA,EAAA,SAAA8H,OAAA,GAAA9H,EAAA,UAEArG,QAAA,OACA1E,EAAA,IAAAA,GAEAnH,KAAA6M,OAAA,CACA1F,SAAAA,EACA1H,MAAA2H,EAAAtK,OAAAsK,EAAAmD,WAAA2H,IAAA2K,WAKA,OAAA7c,KAAAuK,WAAA2H,IAGAtH,SAAA,SAAA0D,EAAAvN,GAGA,IAkBAmR,EACAkU,EAlBAxgB,EAAA,GACAnL,EAAA,GAeA,OAZAsG,GAAAA,EAAA+F,MAAAwH,EAAAnH,UAAAmH,EAAA7O,QAEAhF,EAAA6T,EAAAnH,SAAA0Q,UAAA,EAAAvJ,EAAAnH,SAAA8V,YAAA,MAEArX,EAAA0I,EAAAnH,SAAA0Q,UAAA,EAAA,EAAAvJ,EAAAnH,SAAA8V,YAAA,OACA7V,EAAApH,KAAA+T,OAAAtZ,MAGA6T,EAAAlH,EAAAvJ,OAAAyQ,EAAA7O,WAIA6O,aAAAtO,KAAAuP,OAAAjB,aAAAwC,GACAoB,EAAA5D,EAAAsD,MAAAhH,SAAA0D,EAAAvN,GACAqlB,EAAA,MAAA9X,EAAAsD,MAAAzH,SAAA,GACAmE,EAAAsD,MAAAzH,SAAA6P,OAAA,GAAA1L,EAAAsD,MAAAzH,SAMA+H,EAAA,SADAzX,GAFAmL,EADA,KAAAA,EAtBA,uBAyBAA,GAAAwgB,EAEAlU,GAGAlS,KAAA4K,SAAA0D,EAAAvN,M,6BClGA3F,EAAAR,QAAAoW,EAEA,IAEAC,EAFApW,EAAAS,EAAA,IAIAif,EAAA1f,EAAA0f,SACAle,EAAAxB,EAAAwB,OACAiK,EAAAzL,EAAAyL,KAWA,SAAA+f,EAAA9qB,EAAAgL,EAAApE,GAMAnC,KAAAzE,GAAAA,EAMAyE,KAAAuG,IAAAA,EAMAvG,KAAA8W,KAAA3c,GAMA6F,KAAAmC,IAAAA,EAIA,SAAAmkB,KAUA,SAAAC,EAAA1U,GAMA7R,KAAAkX,KAAArF,EAAAqF,KAMAlX,KAAAwmB,KAAA3U,EAAA2U,KAMAxmB,KAAAuG,IAAAsL,EAAAtL,IAMAvG,KAAA8W,KAAAjF,EAAA4U,OAQA,SAAAzV,IAMAhR,KAAAuG,IAAA,EAMAvG,KAAAkX,KAAA,IAAAmP,EAAAC,EAAA,EAAA,GAMAtmB,KAAAwmB,KAAAxmB,KAAAkX,KAMAlX,KAAAymB,OAAA,KASA,SAAA5Z,IACA,OAAAhS,EAAA8f,OACA,WACA,OAAA3J,EAAAnE,OAAA,WACA,OAAA,IAAAoE,OAIA,WACA,OAAA,IAAAD,GAuCA,SAAA0V,EAAAvkB,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EAoBA,SAAAwkB,EAAApgB,EAAApE,GACAnC,KAAAuG,IAAAA,EACAvG,KAAA8W,KAAA3c,GACA6F,KAAAmC,IAAAA,EA8CA,SAAAykB,EAAAzkB,EAAAC,EAAAC,GACA,KAAAF,EAAA4B,IACA3B,EAAAC,KAAA,IAAAF,EAAA2B,GAAA,IACA3B,EAAA2B,IAAA3B,EAAA2B,KAAA,EAAA3B,EAAA4B,IAAA,MAAA,EACA5B,EAAA4B,MAAA,EAEA,KAAA,IAAA5B,EAAA2B,IACA1B,EAAAC,KAAA,IAAAF,EAAA2B,GAAA,IACA3B,EAAA2B,GAAA3B,EAAA2B,KAAA,EAEA1B,EAAAC,KAAAF,EAAA2B,GA2CA,SAAA+iB,EAAA1kB,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GA7JA6O,EAAAnE,OAAAA,IAOAmE,EAAA/K,MAAA,SAAAC,GACA,OAAA,IAAArL,EAAAa,MAAAwK,IAKArL,EAAAa,QAAAA,QACAsV,EAAA/K,MAAApL,EAAA0pB,KAAAvT,EAAA/K,MAAApL,EAAAa,MAAAwE,UAAAib,WAUAnK,EAAA9Q,UAAA4mB,EAAA,SAAAvrB,EAAAgL,EAAApE,GAGA,OAFAnC,KAAAwmB,KAAAxmB,KAAAwmB,KAAA1P,KAAA,IAAAuP,EAAA9qB,EAAAgL,EAAApE,GACAnC,KAAAuG,KAAAA,EACAvG,OA8BA2mB,EAAAzmB,UAAApB,OAAA+N,OAAAwZ,EAAAnmB,YACA3E,GAxBA,SAAA4G,EAAAC,EAAAC,GACA,KAAA,IAAAF,GACAC,EAAAC,KAAA,IAAAF,EAAA,IACAA,KAAA,EAEAC,EAAAC,GAAAF,GA0BA6O,EAAA9Q,UAAAkb,OAAA,SAAA3b,GAWA,OARAO,KAAAuG,MAAAvG,KAAAwmB,KAAAxmB,KAAAwmB,KAAA1P,KAAA,IAAA6P,GACAlnB,KAAA,GACA,IAAA,EACAA,EAAA,MAAA,EACAA,EAAA,QAAA,EACAA,EAAA,UAAA,EACA,EACAA,IAAA8G,IACAvG,MASAgR,EAAA9Q,UAAAmb,MAAA,SAAA5b,GACA,OAAAA,EAAA,EACAO,KAAA8mB,EAAAF,EAAA,GAAArM,EAAArL,WAAAzP,IACAO,KAAAob,OAAA3b,IAQAuR,EAAA9Q,UAAAob,OAAA,SAAA7b,GACA,OAAAO,KAAAob,QAAA3b,GAAA,EAAAA,GAAA,MAAA,IAkCAuR,EAAA9Q,UAAA6b,MAZA/K,EAAA9Q,UAAA8b,OAAA,SAAAvc,GACAsb,EAAAR,EAAAkJ,KAAAhkB,GACA,OAAAO,KAAA8mB,EAAAF,EAAA7L,EAAAnf,SAAAmf,IAkBA/J,EAAA9Q,UAAA+b,OAAA,SAAAxc,GACAsb,EAAAR,EAAAkJ,KAAAhkB,GAAA8jB,WACA,OAAAvjB,KAAA8mB,EAAAF,EAAA7L,EAAAnf,SAAAmf,IAQA/J,EAAA9Q,UAAAqb,KAAA,SAAA9b,GACA,OAAAO,KAAA8mB,EAAAJ,EAAA,EAAAjnB,EAAA,EAAA,IAyBAuR,EAAA9Q,UAAAub,SAVAzK,EAAA9Q,UAAAsb,QAAA,SAAA/b,GACA,OAAAO,KAAA8mB,EAAAD,EAAA,EAAApnB,IAAA,IA6BAuR,EAAA9Q,UAAAkc,SAZApL,EAAA9Q,UAAAic,QAAA,SAAA1c,GACAsb,EAAAR,EAAAkJ,KAAAhkB,GACA,OAAAO,KAAA8mB,EAAAD,EAAA,EAAA9L,EAAAjX,IAAAgjB,EAAAD,EAAA,EAAA9L,EAAAhX,KAkBAiN,EAAA9Q,UAAAwb,MAAA,SAAAjc,GACA,OAAAO,KAAA8mB,EAAAjsB,EAAA6gB,MAAArX,aAAA,EAAA5E,IASAuR,EAAA9Q,UAAAyb,OAAA,SAAAlc,GACA,OAAAO,KAAA8mB,EAAAjsB,EAAA6gB,MAAA3W,cAAA,EAAAtF,IAGA,IAAAsnB,EAAAlsB,EAAAa,MAAAwE,UAAAsV,IACA,SAAArT,EAAAC,EAAAC,GACAD,EAAAoT,IAAArT,EAAAE,IAGA,SAAAF,EAAAC,EAAAC,GACA,IAAA,IAAAxF,EAAA,EAAAA,EAAAsF,EAAAvG,SAAAiB,EACAuF,EAAAC,EAAAxF,GAAAsF,EAAAtF,IAQAmU,EAAA9Q,UAAAwL,MAAA,SAAAjM,GACA,IAIA2C,EAJAmE,EAAA9G,EAAA7D,SAAA,EACA,OAAA2K,GAEA1L,EAAA6S,SAAAjO,KACA2C,EAAA4O,EAAA/K,MAAAM,EAAAlK,EAAAT,OAAA6D,IACApD,EAAAwB,OAAA4B,EAAA2C,EAAA,GACA3C,EAAA2C,GAEApC,KAAAob,OAAA7U,GAAAugB,EAAAC,EAAAxgB,EAAA9G,IANAO,KAAA8mB,EAAAJ,EAAA,EAAA,IAcA1V,EAAA9Q,UAAA5D,OAAA,SAAAmD,GACA,IAAA8G,EAAAD,EAAA1K,OAAA6D,GACA,OAAA8G,EACAvG,KAAAob,OAAA7U,GAAAugB,EAAAxgB,EAAAG,MAAAF,EAAA9G,GACAO,KAAA8mB,EAAAJ,EAAA,EAAA,IAQA1V,EAAA9Q,UAAAoiB,KAAA,WAIA,OAHAtiB,KAAAymB,OAAA,IAAAF,EAAAvmB,MACAA,KAAAkX,KAAAlX,KAAAwmB,KAAA,IAAAH,EAAAC,EAAA,EAAA,GACAtmB,KAAAuG,IAAA,EACAvG,MAOAgR,EAAA9Q,UAAA8mB,MAAA,WAUA,OATAhnB,KAAAymB,QACAzmB,KAAAkX,KAAAlX,KAAAymB,OAAAvP,KACAlX,KAAAwmB,KAAAxmB,KAAAymB,OAAAD,KACAxmB,KAAAuG,IAAAvG,KAAAymB,OAAAlgB,IACAvG,KAAAymB,OAAAzmB,KAAAymB,OAAA3P,OAEA9W,KAAAkX,KAAAlX,KAAAwmB,KAAA,IAAAH,EAAAC,EAAA,EAAA,GACAtmB,KAAAuG,IAAA,GAEAvG,MAOAgR,EAAA9Q,UAAAqiB,OAAA,WACA,IAAArL,EAAAlX,KAAAkX,KACAsP,EAAAxmB,KAAAwmB,KACAjgB,EAAAvG,KAAAuG,IAOA,OANAvG,KAAAgnB,QAAA5L,OAAA7U,GACAA,IACAvG,KAAAwmB,KAAA1P,KAAAI,EAAAJ,KACA9W,KAAAwmB,KAAAA,EACAxmB,KAAAuG,KAAAA,GAEAvG,MAOAgR,EAAA9Q,UAAA2c,OAAA,WAIA,IAHA,IAAA3F,EAAAlX,KAAAkX,KAAAJ,KACA1U,EAAApC,KAAA8M,YAAA7G,MAAAjG,KAAAuG,KACAlE,EAAA,EACA6U,GACAA,EAAA3b,GAAA2b,EAAA/U,IAAAC,EAAAC,GACAA,GAAA6U,EAAA3Q,IACA2Q,EAAAA,EAAAJ,KAGA,OAAA1U,GAGA4O,EAAAhB,EAAA,SAAAiX,GACAhW,EAAAgW,EACAjW,EAAAnE,OAAAA,IACAoE,EAAAjB,M,6BC9cA5U,EAAAR,QAAAqW,EAGA,IAAAD,EAAA1V,EAAA,IAGAT,IAFAoW,EAAA/Q,UAAApB,OAAA+N,OAAAmE,EAAA9Q,YAAA4M,YAAAmE,EAEA3V,EAAA,KAQA,SAAA2V,IACAD,EAAArW,KAAAqF,MAwCA,SAAAknB,EAAA/kB,EAAAC,EAAAC,GACAF,EAAAvG,OAAA,GACAf,EAAAyL,KAAAG,MAAAtE,EAAAC,EAAAC,GACAD,EAAA0iB,UACA1iB,EAAA0iB,UAAA3iB,EAAAE,GAEAD,EAAAqE,MAAAtE,EAAAE,GA3CA4O,EAAAjB,EAAA,WAOAiB,EAAAhL,MAAApL,EAAAmqB,EAEA/T,EAAAkW,iBAAAtsB,EAAA8f,QAAA9f,EAAA8f,OAAAza,qBAAAyB,YAAA,QAAA9G,EAAA8f,OAAAza,UAAAsV,IAAA/a,KACA,SAAA0H,EAAAC,EAAAC,GACAD,EAAAoT,IAAArT,EAAAE,IAIA,SAAAF,EAAAC,EAAAC,GACA,GAAAF,EAAAilB,KACAjlB,EAAAilB,KAAAhlB,EAAAC,EAAA,EAAAF,EAAAvG,aACA,IAAA,IAAAiB,EAAA,EAAAA,EAAAsF,EAAAvG,QACAwG,EAAAC,KAAAF,EAAAtF,OAQAoU,EAAA/Q,UAAAwL,MAAA,SAAAjM,GAGA,IAAA8G,GADA9G,EADA5E,EAAA6S,SAAAjO,GACA5E,EAAAkqB,EAAAtlB,EAAA,UACAA,GAAA7D,SAAA,EAIA,OAHAoE,KAAAob,OAAA7U,GACAA,GACAvG,KAAA8mB,EAAA7V,EAAAkW,iBAAA5gB,EAAA9G,GACAO,MAeAiR,EAAA/Q,UAAA5D,OAAA,SAAAmD,GACA,IAAA8G,EAAA1L,EAAA8f,OAAA0M,WAAA5nB,GAIA,OAHAO,KAAAob,OAAA7U,GACAA,GACAvG,KAAA8mB,EAAAI,EAAA3gB,EAAA9G,GACAO,MAWAiR,EAAAjB,8B3CpFA", "file": "protobuf.min.js", "sourcesContent": ["(function prelude(modules, cache, entries) {\n\n    // This is the prelude used to bundle protobuf.js for the browser. Wraps up the CommonJS\n    // sources through a conflict-free require shim and is again wrapped within an iife that\n    // provides a minification-friendly `undefined` var plus a global \"use strict\" directive\n    // so that minification can remove the directives of each module.\n\n    function $require(name) {\n        var $module = cache[name];\n        if (!$module)\n            modules[name][0].call($module = cache[name] = { exports: {} }, $require, $module, $module.exports);\n        return $module.exports;\n    }\n\n    var protobuf = $require(entries[0]);\n\n    // Expose globally\n    protobuf.util.global.protobuf = protobuf;\n\n    // Be nice to AMD\n    if (typeof define === \"function\" && define.amd)\n        define([\"long\"], function(Long) {\n            if (Long && Long.isLong) {\n                protobuf.util.Long = Long;\n                protobuf.configure();\n            }\n            return protobuf;\n        });\n\n    // Be nice to CommonJS\n    if (typeof module === \"object\" && module && module.exports)\n        module.exports = protobuf;\n\n})/* end of prelude */", "\"use strict\";\r\nmodule.exports = asPromise;\r\n\r\n/**\r\n * Callback as used by {@link util.asPromise}.\r\n * @typedef asPromiseCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any\r\n * @param {...*} params Additional arguments\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Returns a promise from a node-style callback function.\r\n * @memberof util\r\n * @param {asPromiseCallback} fn Function to call\r\n * @param {*} ctx Function context\r\n * @param {...*} params Function arguments\r\n * @returns {Promise<*>} Promisified function\r\n */\r\nfunction asPromise(fn, ctx/*, varargs */) {\r\n    var params  = new Array(arguments.length - 1),\r\n        offset  = 0,\r\n        index   = 2,\r\n        pending = true;\r\n    while (index < arguments.length)\r\n        params[offset++] = arguments[index++];\r\n    return new Promise(function executor(resolve, reject) {\r\n        params[offset] = function callback(err/*, varargs */) {\r\n            if (pending) {\r\n                pending = false;\r\n                if (err)\r\n                    reject(err);\r\n                else {\r\n                    var params = new Array(arguments.length - 1),\r\n                        offset = 0;\r\n                    while (offset < params.length)\r\n                        params[offset++] = arguments[offset];\r\n                    resolve.apply(null, params);\r\n                }\r\n            }\r\n        };\r\n        try {\r\n            fn.apply(ctx || null, params);\r\n        } catch (err) {\r\n            if (pending) {\r\n                pending = false;\r\n                reject(err);\r\n            }\r\n        }\r\n    });\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal base64 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar base64 = exports;\r\n\r\n/**\r\n * Calculates the byte length of a base64 encoded string.\r\n * @param {string} string Base64 encoded string\r\n * @returns {number} Byte length\r\n */\r\nbase64.length = function length(string) {\r\n    var p = string.length;\r\n    if (!p)\r\n        return 0;\r\n    var n = 0;\r\n    while (--p % 4 > 1 && string.charAt(p) === \"=\")\r\n        ++n;\r\n    return Math.ceil(string.length * 3) / 4 - n;\r\n};\r\n\r\n// Base64 encoding table\r\nvar b64 = new Array(64);\r\n\r\n// Base64 decoding table\r\nvar s64 = new Array(123);\r\n\r\n// 65..90, 97..122, 48..57, 43, 47\r\nfor (var i = 0; i < 64;)\r\n    s64[b64[i] = i < 26 ? i + 65 : i < 52 ? i + 71 : i < 62 ? i - 4 : i - 59 | 43] = i++;\r\n\r\n/**\r\n * Encodes a buffer to a base64 encoded string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} Base64 encoded string\r\n */\r\nbase64.encode = function encode(buffer, start, end) {\r\n    var parts = null,\r\n        chunk = [];\r\n    var i = 0, // output index\r\n        j = 0, // goto index\r\n        t;     // temporary\r\n    while (start < end) {\r\n        var b = buffer[start++];\r\n        switch (j) {\r\n            case 0:\r\n                chunk[i++] = b64[b >> 2];\r\n                t = (b & 3) << 4;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                chunk[i++] = b64[t | b >> 4];\r\n                t = (b & 15) << 2;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                chunk[i++] = b64[t | b >> 6];\r\n                chunk[i++] = b64[b & 63];\r\n                j = 0;\r\n                break;\r\n        }\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (j) {\r\n        chunk[i++] = b64[t];\r\n        chunk[i++] = 61;\r\n        if (j === 1)\r\n            chunk[i++] = 61;\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\nvar invalidEncoding = \"invalid encoding\";\r\n\r\n/**\r\n * Decodes a base64 encoded string to a buffer.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Number of bytes written\r\n * @throws {Error} If encoding is invalid\r\n */\r\nbase64.decode = function decode(string, buffer, offset) {\r\n    var start = offset;\r\n    var j = 0, // goto index\r\n        t;     // temporary\r\n    for (var i = 0; i < string.length;) {\r\n        var c = string.charCodeAt(i++);\r\n        if (c === 61 && j > 1)\r\n            break;\r\n        if ((c = s64[c]) === undefined)\r\n            throw Error(invalidEncoding);\r\n        switch (j) {\r\n            case 0:\r\n                t = c;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                buffer[offset++] = t << 2 | (c & 48) >> 4;\r\n                t = c;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                buffer[offset++] = (t & 15) << 4 | (c & 60) >> 2;\r\n                t = c;\r\n                j = 3;\r\n                break;\r\n            case 3:\r\n                buffer[offset++] = (t & 3) << 6 | c;\r\n                j = 0;\r\n                break;\r\n        }\r\n    }\r\n    if (j === 1)\r\n        throw Error(invalidEncoding);\r\n    return offset - start;\r\n};\r\n\r\n/**\r\n * Tests if the specified string appears to be base64 encoded.\r\n * @param {string} string String to test\r\n * @returns {boolean} `true` if probably base64 encoded, otherwise false\r\n */\r\nbase64.test = function test(string) {\r\n    return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(string);\r\n};\r\n", "\"use strict\";\r\nmodule.exports = codegen;\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @param {string[]} functionParams Function parameter names\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n */\r\nfunction codegen(functionParams, functionName) {\r\n\r\n    /* istanbul ignore if */\r\n    if (typeof functionParams === \"string\") {\r\n        functionName = functionParams;\r\n        functionParams = undefined;\r\n    }\r\n\r\n    var body = [];\r\n\r\n    /**\r\n     * Appends code to the function's body or finishes generation.\r\n     * @typedef Codegen\r\n     * @type {function}\r\n     * @param {string|Object.<string,*>} [formatStringOrScope] Format string or, to finish the function, an object of additional scope variables, if any\r\n     * @param {...*} [formatParams] Format parameters\r\n     * @returns {Codegen|Function} Itself or the generated function if finished\r\n     * @throws {Error} If format parameter counts do not match\r\n     */\r\n\r\n    function Codegen(formatStringOrScope) {\r\n        // note that explicit array handling below makes this ~50% faster\r\n\r\n        // finish the function\r\n        if (typeof formatStringOrScope !== \"string\") {\r\n            var source = toString();\r\n            if (codegen.verbose)\r\n                console.log(\"codegen: \" + source); // eslint-disable-line no-console\r\n            source = \"return \" + source;\r\n            if (formatStringOrScope) {\r\n                var scopeKeys   = Object.keys(formatStringOrScope),\r\n                    scopeParams = new Array(scopeKeys.length + 1),\r\n                    scopeValues = new Array(scopeKeys.length),\r\n                    scopeOffset = 0;\r\n                while (scopeOffset < scopeKeys.length) {\r\n                    scopeParams[scopeOffset] = scopeKeys[scopeOffset];\r\n                    scopeValues[scopeOffset] = formatStringOrScope[scopeKeys[scopeOffset++]];\r\n                }\r\n                scopeParams[scopeOffset] = source;\r\n                return Function.apply(null, scopeParams).apply(null, scopeValues); // eslint-disable-line no-new-func\r\n            }\r\n            return Function(source)(); // eslint-disable-line no-new-func\r\n        }\r\n\r\n        // otherwise append to body\r\n        var formatParams = new Array(arguments.length - 1),\r\n            formatOffset = 0;\r\n        while (formatOffset < formatParams.length)\r\n            formatParams[formatOffset] = arguments[++formatOffset];\r\n        formatOffset = 0;\r\n        formatStringOrScope = formatStringOrScope.replace(/%([%dfijs])/g, function replace($0, $1) {\r\n            var value = formatParams[formatOffset++];\r\n            switch ($1) {\r\n                case \"d\": case \"f\": return String(Number(value));\r\n                case \"i\": return String(Math.floor(value));\r\n                case \"j\": return JSON.stringify(value);\r\n                case \"s\": return String(value);\r\n            }\r\n            return \"%\";\r\n        });\r\n        if (formatOffset !== formatParams.length)\r\n            throw Error(\"parameter count mismatch\");\r\n        body.push(formatStringOrScope);\r\n        return Codegen;\r\n    }\r\n\r\n    function toString(functionNameOverride) {\r\n        return \"function \" + (functionNameOverride || functionName || \"\") + \"(\" + (functionParams && functionParams.join(\",\") || \"\") + \"){\\n  \" + body.join(\"\\n  \") + \"\\n}\";\r\n    }\r\n\r\n    Codegen.toString = toString;\r\n    return Codegen;\r\n}\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @function codegen\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * When set to `true`, codegen will log generated code to console. Useful for debugging.\r\n * @name util.codegen.verbose\r\n * @type {boolean}\r\n */\r\ncodegen.verbose = false;\r\n", "\"use strict\";\r\nmodule.exports = EventEmitter;\r\n\r\n/**\r\n * Constructs a new event emitter instance.\r\n * @classdesc A minimal event emitter.\r\n * @memberof util\r\n * @constructor\r\n */\r\nfunction EventEmitter() {\r\n\r\n    /**\r\n     * Registered listeners.\r\n     * @type {Object.<string,*>}\r\n     * @private\r\n     */\r\n    this._listeners = {};\r\n}\r\n\r\n/**\r\n * Registers an event listener.\r\n * @param {string} evt Event name\r\n * @param {function} fn Listener\r\n * @param {*} [ctx] Listener context\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.on = function on(evt, fn, ctx) {\r\n    (this._listeners[evt] || (this._listeners[evt] = [])).push({\r\n        fn  : fn,\r\n        ctx : ctx || this\r\n    });\r\n    return this;\r\n};\r\n\r\n/**\r\n * Removes an event listener or any matching listeners if arguments are omitted.\r\n * @param {string} [evt] Event name. Removes all listeners if omitted.\r\n * @param {function} [fn] Listener to remove. Removes all listeners of `evt` if omitted.\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.off = function off(evt, fn) {\r\n    if (evt === undefined)\r\n        this._listeners = {};\r\n    else {\r\n        if (fn === undefined)\r\n            this._listeners[evt] = [];\r\n        else {\r\n            var listeners = this._listeners[evt];\r\n            for (var i = 0; i < listeners.length;)\r\n                if (listeners[i].fn === fn)\r\n                    listeners.splice(i, 1);\r\n                else\r\n                    ++i;\r\n        }\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Emits an event by calling its listeners with the specified arguments.\r\n * @param {string} evt Event name\r\n * @param {...*} args Arguments\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.emit = function emit(evt) {\r\n    var listeners = this._listeners[evt];\r\n    if (listeners) {\r\n        var args = [],\r\n            i = 1;\r\n        for (; i < arguments.length;)\r\n            args.push(arguments[i++]);\r\n        for (i = 0; i < listeners.length;)\r\n            listeners[i].fn.apply(listeners[i++].ctx, args);\r\n    }\r\n    return this;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = fetch;\r\n\r\nvar asPromise = require(1),\r\n    inquire   = require(7);\r\n\r\nvar fs = inquire(\"fs\");\r\n\r\n/**\r\n * Node-style callback as used by {@link util.fetch}.\r\n * @typedef FetchCallback\r\n * @type {function}\r\n * @param {?Error} error Error, if any, otherwise `null`\r\n * @param {string} [contents] File contents, if there hasn't been an error\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Options as used by {@link util.fetch}.\r\n * @typedef FetchOptions\r\n * @type {Object}\r\n * @property {boolean} [binary=false] Whether expecting a binary response\r\n * @property {boolean} [xhr=false] If `true`, forces the use of XMLHttpRequest\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @memberof util\r\n * @param {string} filename File path or url\r\n * @param {FetchOptions} options Fetch options\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n */\r\nfunction fetch(filename, options, callback) {\r\n    if (typeof options === \"function\") {\r\n        callback = options;\r\n        options = {};\r\n    } else if (!options)\r\n        options = {};\r\n\r\n    if (!callback)\r\n        return asPromise(fetch, this, filename, options); // eslint-disable-line no-invalid-this\r\n\r\n    // if a node-like filesystem is present, try it first but fall back to XHR if nothing is found.\r\n    if (!options.xhr && fs && fs.readFile)\r\n        return fs.readFile(filename, function fetchReadFileCallback(err, contents) {\r\n            return err && typeof XMLHttpRequest !== \"undefined\"\r\n                ? fetch.xhr(filename, options, callback)\r\n                : err\r\n                ? callback(err)\r\n                : callback(null, options.binary ? contents : contents.toString(\"utf8\"));\r\n        });\r\n\r\n    // use the XHR version otherwise.\r\n    return fetch.xhr(filename, options, callback);\r\n}\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchOptions} [options] Fetch options\r\n * @returns {Promise<string|Uint8Array>} Promise\r\n * @variation 3\r\n */\r\n\r\n/**/\r\nfetch.xhr = function fetch_xhr(filename, options, callback) {\r\n    var xhr = new XMLHttpRequest();\r\n    xhr.onreadystatechange /* works everywhere */ = function fetchOnReadyStateChange() {\r\n\r\n        if (xhr.readyState !== 4)\r\n            return undefined;\r\n\r\n        // local cors security errors return status 0 / empty string, too. afaik this cannot be\r\n        // reliably distinguished from an actually empty file for security reasons. feel free\r\n        // to send a pull request if you are aware of a solution.\r\n        if (xhr.status !== 0 && xhr.status !== 200)\r\n            return callback(Error(\"status \" + xhr.status));\r\n\r\n        // if binary data is expected, make sure that some sort of array is returned, even if\r\n        // ArrayBuffers are not supported. the binary string fallback, however, is unsafe.\r\n        if (options.binary) {\r\n            var buffer = xhr.response;\r\n            if (!buffer) {\r\n                buffer = [];\r\n                for (var i = 0; i < xhr.responseText.length; ++i)\r\n                    buffer.push(xhr.responseText.charCodeAt(i) & 255);\r\n            }\r\n            return callback(null, typeof Uint8Array !== \"undefined\" ? new Uint8Array(buffer) : buffer);\r\n        }\r\n        return callback(null, xhr.responseText);\r\n    };\r\n\r\n    if (options.binary) {\r\n        // ref: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/Sending_and_Receiving_Binary_Data#Receiving_binary_data_in_older_browsers\r\n        if (\"overrideMimeType\" in xhr)\r\n            xhr.overrideMimeType(\"text/plain; charset=x-user-defined\");\r\n        xhr.responseType = \"arraybuffer\";\r\n    }\r\n\r\n    xhr.open(\"GET\", filename);\r\n    xhr.send();\r\n};\r\n", "\"use strict\";\r\n\r\nmodule.exports = factory(factory);\r\n\r\n/**\r\n * Reads / writes floats / doubles from / to buffers.\r\n * @name util.float\r\n * @namespace\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using little endian byte order.\r\n * @name util.float.writeFloatLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using big endian byte order.\r\n * @name util.float.writeFloatBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using little endian byte order.\r\n * @name util.float.readFloatLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using big endian byte order.\r\n * @name util.float.readFloatBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using little endian byte order.\r\n * @name util.float.writeDoubleLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using big endian byte order.\r\n * @name util.float.writeDoubleBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using little endian byte order.\r\n * @name util.float.readDoubleLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using big endian byte order.\r\n * @name util.float.readDoubleBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n// Factory function for the purpose of node-based testing in modified global environments\r\nfunction factory(exports) {\r\n\r\n    // float: typed array\r\n    if (typeof Float32Array !== \"undefined\") (function() {\r\n\r\n        var f32 = new Float32Array([ -0 ]),\r\n            f8b = new Uint8Array(f32.buffer),\r\n            le  = f8b[3] === 128;\r\n\r\n        function writeFloat_f32_cpy(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n        }\r\n\r\n        function writeFloat_f32_rev(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[3];\r\n            buf[pos + 1] = f8b[2];\r\n            buf[pos + 2] = f8b[1];\r\n            buf[pos + 3] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeFloatLE = le ? writeFloat_f32_cpy : writeFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeFloatBE = le ? writeFloat_f32_rev : writeFloat_f32_cpy;\r\n\r\n        function readFloat_f32_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        function readFloat_f32_rev(buf, pos) {\r\n            f8b[3] = buf[pos    ];\r\n            f8b[2] = buf[pos + 1];\r\n            f8b[1] = buf[pos + 2];\r\n            f8b[0] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readFloatLE = le ? readFloat_f32_cpy : readFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.readFloatBE = le ? readFloat_f32_rev : readFloat_f32_cpy;\r\n\r\n    // float: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeFloat_ieee754(writeUint, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0)\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos);\r\n            else if (isNaN(val))\r\n                writeUint(2143289344, buf, pos);\r\n            else if (val > 3.4028234663852886e+38) // +-Infinity\r\n                writeUint((sign << 31 | 2139095040) >>> 0, buf, pos);\r\n            else if (val < 1.1754943508222875e-38) // denormal\r\n                writeUint((sign << 31 | Math.round(val / 1.401298464324817e-45)) >>> 0, buf, pos);\r\n            else {\r\n                var exponent = Math.floor(Math.log(val) / Math.LN2),\r\n                    mantissa = Math.round(val * Math.pow(2, -exponent) * 8388608) & 8388607;\r\n                writeUint((sign << 31 | exponent + 127 << 23 | mantissa) >>> 0, buf, pos);\r\n            }\r\n        }\r\n\r\n        exports.writeFloatLE = writeFloat_ieee754.bind(null, writeUintLE);\r\n        exports.writeFloatBE = writeFloat_ieee754.bind(null, writeUintBE);\r\n\r\n        function readFloat_ieee754(readUint, buf, pos) {\r\n            var uint = readUint(buf, pos),\r\n                sign = (uint >> 31) * 2 + 1,\r\n                exponent = uint >>> 23 & 255,\r\n                mantissa = uint & 8388607;\r\n            return exponent === 255\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 1.401298464324817e-45 * mantissa\r\n                : sign * Math.pow(2, exponent - 150) * (mantissa + 8388608);\r\n        }\r\n\r\n        exports.readFloatLE = readFloat_ieee754.bind(null, readUintLE);\r\n        exports.readFloatBE = readFloat_ieee754.bind(null, readUintBE);\r\n\r\n    })();\r\n\r\n    // double: typed array\r\n    if (typeof Float64Array !== \"undefined\") (function() {\r\n\r\n        var f64 = new Float64Array([-0]),\r\n            f8b = new Uint8Array(f64.buffer),\r\n            le  = f8b[7] === 128;\r\n\r\n        function writeDouble_f64_cpy(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n            buf[pos + 4] = f8b[4];\r\n            buf[pos + 5] = f8b[5];\r\n            buf[pos + 6] = f8b[6];\r\n            buf[pos + 7] = f8b[7];\r\n        }\r\n\r\n        function writeDouble_f64_rev(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[7];\r\n            buf[pos + 1] = f8b[6];\r\n            buf[pos + 2] = f8b[5];\r\n            buf[pos + 3] = f8b[4];\r\n            buf[pos + 4] = f8b[3];\r\n            buf[pos + 5] = f8b[2];\r\n            buf[pos + 6] = f8b[1];\r\n            buf[pos + 7] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleLE = le ? writeDouble_f64_cpy : writeDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleBE = le ? writeDouble_f64_rev : writeDouble_f64_cpy;\r\n\r\n        function readDouble_f64_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            f8b[4] = buf[pos + 4];\r\n            f8b[5] = buf[pos + 5];\r\n            f8b[6] = buf[pos + 6];\r\n            f8b[7] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        function readDouble_f64_rev(buf, pos) {\r\n            f8b[7] = buf[pos    ];\r\n            f8b[6] = buf[pos + 1];\r\n            f8b[5] = buf[pos + 2];\r\n            f8b[4] = buf[pos + 3];\r\n            f8b[3] = buf[pos + 4];\r\n            f8b[2] = buf[pos + 5];\r\n            f8b[1] = buf[pos + 6];\r\n            f8b[0] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readDoubleLE = le ? readDouble_f64_cpy : readDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.readDoubleBE = le ? readDouble_f64_rev : readDouble_f64_cpy;\r\n\r\n    // double: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeDouble_ieee754(writeUint, off0, off1, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos + off1);\r\n            } else if (isNaN(val)) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(2146959360, buf, pos + off1);\r\n            } else if (val > 1.7976931348623157e+308) { // +-Infinity\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint((sign << 31 | 2146435072) >>> 0, buf, pos + off1);\r\n            } else {\r\n                var mantissa;\r\n                if (val < 2.2250738585072014e-308) { // denormal\r\n                    mantissa = val / 5e-324;\r\n                    writeUint(mantissa >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | mantissa / 4294967296) >>> 0, buf, pos + off1);\r\n                } else {\r\n                    var exponent = Math.floor(Math.log(val) / Math.LN2);\r\n                    if (exponent === 1024)\r\n                        exponent = 1023;\r\n                    mantissa = val * Math.pow(2, -exponent);\r\n                    writeUint(mantissa * 4503599627370496 >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | exponent + 1023 << 20 | mantissa * 1048576 & 1048575) >>> 0, buf, pos + off1);\r\n                }\r\n            }\r\n        }\r\n\r\n        exports.writeDoubleLE = writeDouble_ieee754.bind(null, writeUintLE, 0, 4);\r\n        exports.writeDoubleBE = writeDouble_ieee754.bind(null, writeUintBE, 4, 0);\r\n\r\n        function readDouble_ieee754(readUint, off0, off1, buf, pos) {\r\n            var lo = readUint(buf, pos + off0),\r\n                hi = readUint(buf, pos + off1);\r\n            var sign = (hi >> 31) * 2 + 1,\r\n                exponent = hi >>> 20 & 2047,\r\n                mantissa = 4294967296 * (hi & 1048575) + lo;\r\n            return exponent === 2047\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 5e-324 * mantissa\r\n                : sign * Math.pow(2, exponent - 1075) * (mantissa + 4503599627370496);\r\n        }\r\n\r\n        exports.readDoubleLE = readDouble_ieee754.bind(null, readUintLE, 0, 4);\r\n        exports.readDoubleBE = readDouble_ieee754.bind(null, readUintBE, 4, 0);\r\n\r\n    })();\r\n\r\n    return exports;\r\n}\r\n\r\n// uint helpers\r\n\r\nfunction writeUintLE(val, buf, pos) {\r\n    buf[pos    ] =  val        & 255;\r\n    buf[pos + 1] =  val >>> 8  & 255;\r\n    buf[pos + 2] =  val >>> 16 & 255;\r\n    buf[pos + 3] =  val >>> 24;\r\n}\r\n\r\nfunction writeUintBE(val, buf, pos) {\r\n    buf[pos    ] =  val >>> 24;\r\n    buf[pos + 1] =  val >>> 16 & 255;\r\n    buf[pos + 2] =  val >>> 8  & 255;\r\n    buf[pos + 3] =  val        & 255;\r\n}\r\n\r\nfunction readUintLE(buf, pos) {\r\n    return (buf[pos    ]\r\n          | buf[pos + 1] << 8\r\n          | buf[pos + 2] << 16\r\n          | buf[pos + 3] << 24) >>> 0;\r\n}\r\n\r\nfunction readUintBE(buf, pos) {\r\n    return (buf[pos    ] << 24\r\n          | buf[pos + 1] << 16\r\n          | buf[pos + 2] << 8\r\n          | buf[pos + 3]) >>> 0;\r\n}\r\n", "\"use strict\";\r\nmodule.exports = inquire;\r\n\r\n/**\r\n * Requires a module only if available.\r\n * @memberof util\r\n * @param {string} moduleName Module to require\r\n * @returns {?Object} Required module if available and not empty, otherwise `null`\r\n */\r\nfunction inquire(moduleName) {\r\n    try {\r\n        var mod = eval(\"quire\".replace(/^/,\"re\"))(moduleName); // eslint-disable-line no-eval\r\n        if (mod && (mod.length || Object.keys(mod).length))\r\n            return mod;\r\n    } catch (e) {} // eslint-disable-line no-empty\r\n    return null;\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal path module to resolve Unix, Windows and URL paths alike.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar path = exports;\r\n\r\nvar isAbsolute =\r\n/**\r\n * Tests if the specified path is absolute.\r\n * @param {string} path Path to test\r\n * @returns {boolean} `true` if path is absolute\r\n */\r\npath.isAbsolute = function isAbsolute(path) {\r\n    return /^(?:\\/|\\w+:)/.test(path);\r\n};\r\n\r\nvar normalize =\r\n/**\r\n * Normalizes the specified path.\r\n * @param {string} path Path to normalize\r\n * @returns {string} Normalized path\r\n */\r\npath.normalize = function normalize(path) {\r\n    path = path.replace(/\\\\/g, \"/\")\r\n               .replace(/\\/{2,}/g, \"/\");\r\n    var parts    = path.split(\"/\"),\r\n        absolute = isAbsolute(path),\r\n        prefix   = \"\";\r\n    if (absolute)\r\n        prefix = parts.shift() + \"/\";\r\n    for (var i = 0; i < parts.length;) {\r\n        if (parts[i] === \"..\") {\r\n            if (i > 0 && parts[i - 1] !== \"..\")\r\n                parts.splice(--i, 2);\r\n            else if (absolute)\r\n                parts.splice(i, 1);\r\n            else\r\n                ++i;\r\n        } else if (parts[i] === \".\")\r\n            parts.splice(i, 1);\r\n        else\r\n            ++i;\r\n    }\r\n    return prefix + parts.join(\"/\");\r\n};\r\n\r\n/**\r\n * Resolves the specified include path against the specified origin path.\r\n * @param {string} originPath Path to the origin file\r\n * @param {string} includePath Include path relative to origin path\r\n * @param {boolean} [alreadyNormalized=false] `true` if both paths are already known to be normalized\r\n * @returns {string} Path to the include file\r\n */\r\npath.resolve = function resolve(originPath, includePath, alreadyNormalized) {\r\n    if (!alreadyNormalized)\r\n        includePath = normalize(includePath);\r\n    if (isAbsolute(includePath))\r\n        return includePath;\r\n    if (!alreadyNormalized)\r\n        originPath = normalize(originPath);\r\n    return (originPath = originPath.replace(/(?:\\/|^)[^/]+$/, \"\")).length ? normalize(originPath + \"/\" + includePath) : includePath;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = pool;\r\n\r\n/**\r\n * An allocator as used by {@link util.pool}.\r\n * @typedef PoolAllocator\r\n * @type {function}\r\n * @param {number} size Buffer size\r\n * @returns {Uint8Array} Buffer\r\n */\r\n\r\n/**\r\n * A slicer as used by {@link util.pool}.\r\n * @typedef PoolSlicer\r\n * @type {function}\r\n * @param {number} start Start offset\r\n * @param {number} end End offset\r\n * @returns {Uint8Array} Buffer slice\r\n * @this {Uint8Array}\r\n */\r\n\r\n/**\r\n * A general purpose buffer pool.\r\n * @memberof util\r\n * @function\r\n * @param {PoolAllocator} alloc Allocator\r\n * @param {PoolSlicer} slice Slicer\r\n * @param {number} [size=8192] Slab size\r\n * @returns {PoolAllocator} Pooled allocator\r\n */\r\nfunction pool(alloc, slice, size) {\r\n    var SIZE   = size || 8192;\r\n    var MAX    = SIZE >>> 1;\r\n    var slab   = null;\r\n    var offset = SIZE;\r\n    return function pool_alloc(size) {\r\n        if (size < 1 || size > MAX)\r\n            return alloc(size);\r\n        if (offset + size > SIZE) {\r\n            slab = alloc(SIZE);\r\n            offset = 0;\r\n        }\r\n        var buf = slice.call(slab, offset, offset += size);\r\n        if (offset & 7) // align to 32 bit\r\n            offset = (offset | 7) + 1;\r\n        return buf;\r\n    };\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal UTF8 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar utf8 = exports;\r\n\r\n/**\r\n * Calculates the UTF8 byte length of a string.\r\n * @param {string} string String\r\n * @returns {number} Byte length\r\n */\r\nutf8.length = function utf8_length(string) {\r\n    var len = 0,\r\n        c = 0;\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c = string.charCodeAt(i);\r\n        if (c < 128)\r\n            len += 1;\r\n        else if (c < 2048)\r\n            len += 2;\r\n        else if ((c & 0xFC00) === 0xD800 && (string.charCodeAt(i + 1) & 0xFC00) === 0xDC00) {\r\n            ++i;\r\n            len += 4;\r\n        } else\r\n            len += 3;\r\n    }\r\n    return len;\r\n};\r\n\r\n/**\r\n * Reads UTF8 bytes as a string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} String read\r\n */\r\nutf8.read = function utf8_read(buffer, start, end) {\r\n    var len = end - start;\r\n    if (len < 1)\r\n        return \"\";\r\n    var parts = null,\r\n        chunk = [],\r\n        i = 0, // char offset\r\n        t;     // temporary\r\n    while (start < end) {\r\n        t = buffer[start++];\r\n        if (t < 128)\r\n            chunk[i++] = t;\r\n        else if (t > 191 && t < 224)\r\n            chunk[i++] = (t & 31) << 6 | buffer[start++] & 63;\r\n        else if (t > 239 && t < 365) {\r\n            t = ((t & 7) << 18 | (buffer[start++] & 63) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63) - 0x10000;\r\n            chunk[i++] = 0xD800 + (t >> 10);\r\n            chunk[i++] = 0xDC00 + (t & 1023);\r\n        } else\r\n            chunk[i++] = (t & 15) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63;\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\n/**\r\n * Writes a string as UTF8 bytes.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Bytes written\r\n */\r\nutf8.write = function utf8_write(string, buffer, offset) {\r\n    var start = offset,\r\n        c1, // character 1\r\n        c2; // character 2\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c1 = string.charCodeAt(i);\r\n        if (c1 < 128) {\r\n            buffer[offset++] = c1;\r\n        } else if (c1 < 2048) {\r\n            buffer[offset++] = c1 >> 6       | 192;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else if ((c1 & 0xFC00) === 0xD800 && ((c2 = string.charCodeAt(i + 1)) & 0xFC00) === 0xDC00) {\r\n            c1 = 0x10000 + ((c1 & 0x03FF) << 10) + (c2 & 0x03FF);\r\n            ++i;\r\n            buffer[offset++] = c1 >> 18      | 240;\r\n            buffer[offset++] = c1 >> 12 & 63 | 128;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else {\r\n            buffer[offset++] = c1 >> 12      | 224;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        }\r\n    }\r\n    return offset - start;\r\n};\r\n", "\"use strict\";\nmodule.exports = common;\n\nvar commonRe = /\\/|\\./;\n\n/**\n * Provides common type definitions.\n * Can also be used to provide additional google types or your own custom types.\n * @param {string} name Short name as in `google/protobuf/[name].proto` or full file name\n * @param {Object.<string,*>} json JSON definition within `google.protobuf` if a short name, otherwise the file's root definition\n * @returns {undefined}\n * @property {INamespace} google/protobuf/any.proto Any\n * @property {INamespace} google/protobuf/duration.proto Duration\n * @property {INamespace} google/protobuf/empty.proto Empty\n * @property {INamespace} google/protobuf/field_mask.proto FieldMask\n * @property {INamespace} google/protobuf/struct.proto Struct, Value, NullValue and ListValue\n * @property {INamespace} google/protobuf/timestamp.proto Timestamp\n * @property {INamespace} google/protobuf/wrappers.proto Wrappers\n * @example\n * // manually provides descriptor.proto (assumes google/protobuf/ namespace and .proto extension)\n * protobuf.common(\"descriptor\", descriptorJson);\n *\n * // manually provides a custom definition (uses my.foo namespace)\n * protobuf.common(\"my/foo/bar.proto\", myFooBarJson);\n */\nfunction common(name, json) {\n    if (!commonRe.test(name)) {\n        name = \"google/protobuf/\" + name + \".proto\";\n        json = { nested: { google: { nested: { protobuf: { nested: json } } } } };\n    }\n    common[name] = json;\n}\n\n// Not provided because of limited use (feel free to discuss or to provide yourself):\n//\n// google/protobuf/descriptor.proto\n// google/protobuf/source_context.proto\n// google/protobuf/type.proto\n//\n// Stripped and pre-parsed versions of these non-bundled files are instead available as part of\n// the repository or package within the google/protobuf directory.\n\ncommon(\"any\", {\n\n    /**\n     * Properties of a google.protobuf.Any message.\n     * @interface IAny\n     * @type {Object}\n     * @property {string} [typeUrl]\n     * @property {Uint8Array} [bytes]\n     * @memberof common\n     */\n    Any: {\n        fields: {\n            type_url: {\n                type: \"string\",\n                id: 1\n            },\n            value: {\n                type: \"bytes\",\n                id: 2\n            }\n        }\n    }\n});\n\nvar timeType;\n\ncommon(\"duration\", {\n\n    /**\n     * Properties of a google.protobuf.Duration message.\n     * @interface IDuration\n     * @type {Object}\n     * @property {number|Long} [seconds]\n     * @property {number} [nanos]\n     * @memberof common\n     */\n    Duration: timeType = {\n        fields: {\n            seconds: {\n                type: \"int64\",\n                id: 1\n            },\n            nanos: {\n                type: \"int32\",\n                id: 2\n            }\n        }\n    }\n});\n\ncommon(\"timestamp\", {\n\n    /**\n     * Properties of a google.protobuf.Timestamp message.\n     * @interface ITimestamp\n     * @type {Object}\n     * @property {number|Long} [seconds]\n     * @property {number} [nanos]\n     * @memberof common\n     */\n    Timestamp: timeType\n});\n\ncommon(\"empty\", {\n\n    /**\n     * Properties of a google.protobuf.Empty message.\n     * @interface IEmpty\n     * @memberof common\n     */\n    Empty: {\n        fields: {}\n    }\n});\n\ncommon(\"struct\", {\n\n    /**\n     * Properties of a google.protobuf.Struct message.\n     * @interface IStruct\n     * @type {Object}\n     * @property {Object.<string,IValue>} [fields]\n     * @memberof common\n     */\n    Struct: {\n        fields: {\n            fields: {\n                keyType: \"string\",\n                type: \"Value\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Value message.\n     * @interface IValue\n     * @type {Object}\n     * @property {string} [kind]\n     * @property {0} [nullValue]\n     * @property {number} [numberValue]\n     * @property {string} [stringValue]\n     * @property {boolean} [boolValue]\n     * @property {IStruct} [structValue]\n     * @property {IListValue} [listValue]\n     * @memberof common\n     */\n    Value: {\n        oneofs: {\n            kind: {\n                oneof: [\n                    \"nullValue\",\n                    \"numberValue\",\n                    \"stringValue\",\n                    \"boolValue\",\n                    \"structValue\",\n                    \"listValue\"\n                ]\n            }\n        },\n        fields: {\n            nullValue: {\n                type: \"NullValue\",\n                id: 1\n            },\n            numberValue: {\n                type: \"double\",\n                id: 2\n            },\n            stringValue: {\n                type: \"string\",\n                id: 3\n            },\n            boolValue: {\n                type: \"bool\",\n                id: 4\n            },\n            structValue: {\n                type: \"Struct\",\n                id: 5\n            },\n            listValue: {\n                type: \"ListValue\",\n                id: 6\n            }\n        }\n    },\n\n    NullValue: {\n        values: {\n            NULL_VALUE: 0\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.ListValue message.\n     * @interface IListValue\n     * @type {Object}\n     * @property {Array.<IValue>} [values]\n     * @memberof common\n     */\n    ListValue: {\n        fields: {\n            values: {\n                rule: \"repeated\",\n                type: \"Value\",\n                id: 1\n            }\n        }\n    }\n});\n\ncommon(\"wrappers\", {\n\n    /**\n     * Properties of a google.protobuf.DoubleValue message.\n     * @interface IDoubleValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    DoubleValue: {\n        fields: {\n            value: {\n                type: \"double\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.FloatValue message.\n     * @interface IFloatValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    FloatValue: {\n        fields: {\n            value: {\n                type: \"float\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Int64Value message.\n     * @interface IInt64Value\n     * @type {Object}\n     * @property {number|Long} [value]\n     * @memberof common\n     */\n    Int64Value: {\n        fields: {\n            value: {\n                type: \"int64\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.UInt64Value message.\n     * @interface IUInt64Value\n     * @type {Object}\n     * @property {number|Long} [value]\n     * @memberof common\n     */\n    UInt64Value: {\n        fields: {\n            value: {\n                type: \"uint64\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Int32Value message.\n     * @interface IInt32Value\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    Int32Value: {\n        fields: {\n            value: {\n                type: \"int32\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.UInt32Value message.\n     * @interface IUInt32Value\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    UInt32Value: {\n        fields: {\n            value: {\n                type: \"uint32\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.BoolValue message.\n     * @interface IBoolValue\n     * @type {Object}\n     * @property {boolean} [value]\n     * @memberof common\n     */\n    BoolValue: {\n        fields: {\n            value: {\n                type: \"bool\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.StringValue message.\n     * @interface IStringValue\n     * @type {Object}\n     * @property {string} [value]\n     * @memberof common\n     */\n    StringValue: {\n        fields: {\n            value: {\n                type: \"string\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.BytesValue message.\n     * @interface IBytesValue\n     * @type {Object}\n     * @property {Uint8Array} [value]\n     * @memberof common\n     */\n    BytesValue: {\n        fields: {\n            value: {\n                type: \"bytes\",\n                id: 1\n            }\n        }\n    }\n});\n\ncommon(\"field_mask\", {\n\n    /**\n     * Properties of a google.protobuf.FieldMask message.\n     * @interface IDoubleValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    FieldMask: {\n        fields: {\n            paths: {\n                rule: \"repeated\",\n                type: \"string\",\n                id: 1\n            }\n        }\n    }\n});\n\n/**\n * Gets the root definition of the specified common proto file.\n *\n * Bundled definitions are:\n * - google/protobuf/any.proto\n * - google/protobuf/duration.proto\n * - google/protobuf/empty.proto\n * - google/protobuf/field_mask.proto\n * - google/protobuf/struct.proto\n * - google/protobuf/timestamp.proto\n * - google/protobuf/wrappers.proto\n *\n * @param {string} file Proto file name\n * @returns {INamespace|null} Root definition or `null` if not defined\n */\ncommon.get = function get(file) {\n    return common[file] || null;\n};\n", "\"use strict\";\n/**\n * Runtime message from/to plain object converters.\n * @namespace\n */\nvar converter = exports;\n\nvar Enum = require(15),\n    util = require(37);\n\n/**\n * Generates a partial value fromObject conveter.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} prop Property reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genValuePartial_fromObject(gen, field, fieldIndex, prop) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) { gen\n            (\"switch(d%s){\", prop);\n            for (var values = field.resolvedType.values, keys = Object.keys(values), i = 0; i < keys.length; ++i) {\n                if (field.repeated && values[keys[i]] === field.typeDefault) gen\n                (\"default:\");\n                gen\n                (\"case%j:\", keys[i])\n                (\"case %i:\", values[keys[i]])\n                    (\"m%s=%j\", prop, values[keys[i]])\n                    (\"break\");\n            } gen\n            (\"}\");\n        } else gen\n            (\"if(typeof d%s!==\\\"object\\\")\", prop)\n                (\"throw TypeError(%j)\", field.fullName + \": object expected\")\n            (\"m%s=types[%i].fromObject(d%s)\", prop, fieldIndex, prop);\n    } else {\n        var isUnsigned = false;\n        switch (field.type) {\n            case \"double\":\n            case \"float\": gen\n                (\"m%s=Number(d%s)\", prop, prop); // also catches \"NaN\", \"Infinity\"\n                break;\n            case \"uint32\":\n            case \"fixed32\": gen\n                (\"m%s=d%s>>>0\", prop, prop);\n                break;\n            case \"int32\":\n            case \"sint32\":\n            case \"sfixed32\": gen\n                (\"m%s=d%s|0\", prop, prop);\n                break;\n            case \"uint64\":\n                isUnsigned = true;\n                // eslint-disable-line no-fallthrough\n            case \"int64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n                (\"if(util.Long)\")\n                    (\"(m%s=util.Long.fromValue(d%s)).unsigned=%j\", prop, prop, isUnsigned)\n                (\"else if(typeof d%s===\\\"string\\\")\", prop)\n                    (\"m%s=parseInt(d%s,10)\", prop, prop)\n                (\"else if(typeof d%s===\\\"number\\\")\", prop)\n                    (\"m%s=d%s\", prop, prop)\n                (\"else if(typeof d%s===\\\"object\\\")\", prop)\n                    (\"m%s=new util.LongBits(d%s.low>>>0,d%s.high>>>0).toNumber(%s)\", prop, prop, prop, isUnsigned ? \"true\" : \"\");\n                break;\n            case \"bytes\": gen\n                (\"if(typeof d%s===\\\"string\\\")\", prop)\n                    (\"util.base64.decode(d%s,m%s=util.newBuffer(util.base64.length(d%s)),0)\", prop, prop, prop)\n                (\"else if(d%s.length)\", prop)\n                    (\"m%s=d%s\", prop, prop);\n                break;\n            case \"string\": gen\n                (\"m%s=String(d%s)\", prop, prop);\n                break;\n            case \"bool\": gen\n                (\"m%s=Boolean(d%s)\", prop, prop);\n                break;\n            /* default: gen\n                (\"m%s=d%s\", prop, prop);\n                break; */\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n\n/**\n * Generates a plain object to runtime message converter specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nconverter.fromObject = function fromObject(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var fields = mtype.fieldsArray;\n    var gen = util.codegen([\"d\"], mtype.name + \"$fromObject\")\n    (\"if(d instanceof this.ctor)\")\n        (\"return d\");\n    if (!fields.length) return gen\n    (\"return new this.ctor\");\n    gen\n    (\"var m=new this.ctor\");\n    for (var i = 0; i < fields.length; ++i) {\n        var field  = fields[i].resolve(),\n            prop   = util.safeProp(field.name);\n\n        // Map fields\n        if (field.map) { gen\n    (\"if(d%s){\", prop)\n        (\"if(typeof d%s!==\\\"object\\\")\", prop)\n            (\"throw TypeError(%j)\", field.fullName + \": object expected\")\n        (\"m%s={}\", prop)\n        (\"for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){\", prop);\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[ks[i]]\")\n        (\"}\")\n    (\"}\");\n\n        // Repeated fields\n        } else if (field.repeated) { gen\n    (\"if(d%s){\", prop)\n        (\"if(!Array.isArray(d%s))\", prop)\n            (\"throw TypeError(%j)\", field.fullName + \": array expected\")\n        (\"m%s=[]\", prop)\n        (\"for(var i=0;i<d%s.length;++i){\", prop);\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[i]\")\n        (\"}\")\n    (\"}\");\n\n        // Non-repeated fields\n        } else {\n            if (!(field.resolvedType instanceof Enum)) gen // no need to test for null/undefined if an enum (uses switch)\n    (\"if(d%s!=null){\", prop); // !== undefined && !== null\n        genValuePartial_fromObject(gen, field, /* not sorted */ i, prop);\n            if (!(field.resolvedType instanceof Enum)) gen\n    (\"}\");\n        }\n    } return gen\n    (\"return m\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n};\n\n/**\n * Generates a partial value toObject converter.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} prop Property reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genValuePartial_toObject(gen, field, fieldIndex, prop) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) gen\n            (\"d%s=o.enums===String?types[%i].values[m%s]:m%s\", prop, fieldIndex, prop, prop);\n        else gen\n            (\"d%s=types[%i].toObject(m%s,o)\", prop, fieldIndex, prop);\n    } else {\n        var isUnsigned = false;\n        switch (field.type) {\n            case \"double\":\n            case \"float\": gen\n            (\"d%s=o.json&&!isFinite(m%s)?String(m%s):m%s\", prop, prop, prop, prop);\n                break;\n            case \"uint64\":\n                isUnsigned = true;\n                // eslint-disable-line no-fallthrough\n            case \"int64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n            (\"if(typeof m%s===\\\"number\\\")\", prop)\n                (\"d%s=o.longs===String?String(m%s):m%s\", prop, prop, prop)\n            (\"else\") // Long-like\n                (\"d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s\", prop, prop, prop, prop, isUnsigned ? \"true\": \"\", prop);\n                break;\n            case \"bytes\": gen\n            (\"d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s\", prop, prop, prop, prop, prop);\n                break;\n            default: gen\n            (\"d%s=m%s\", prop, prop);\n                break;\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n\n/**\n * Generates a runtime message to plain object converter specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nconverter.toObject = function toObject(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var fields = mtype.fieldsArray.slice().sort(util.compareFieldsById);\n    if (!fields.length)\n        return util.codegen()(\"return {}\");\n    var gen = util.codegen([\"m\", \"o\"], mtype.name + \"$toObject\")\n    (\"if(!o)\")\n        (\"o={}\")\n    (\"var d={}\");\n\n    var repeatedFields = [],\n        mapFields = [],\n        normalFields = [],\n        i = 0;\n    for (; i < fields.length; ++i)\n        if (!fields[i].partOf)\n            ( fields[i].resolve().repeated ? repeatedFields\n            : fields[i].map ? mapFields\n            : normalFields).push(fields[i]);\n\n    if (repeatedFields.length) { gen\n    (\"if(o.arrays||o.defaults){\");\n        for (i = 0; i < repeatedFields.length; ++i) gen\n        (\"d%s=[]\", util.safeProp(repeatedFields[i].name));\n        gen\n    (\"}\");\n    }\n\n    if (mapFields.length) { gen\n    (\"if(o.objects||o.defaults){\");\n        for (i = 0; i < mapFields.length; ++i) gen\n        (\"d%s={}\", util.safeProp(mapFields[i].name));\n        gen\n    (\"}\");\n    }\n\n    if (normalFields.length) { gen\n    (\"if(o.defaults){\");\n        for (i = 0; i < normalFields.length; ++i) {\n            var field = normalFields[i],\n                prop  = util.safeProp(field.name);\n            if (field.resolvedType instanceof Enum) gen\n        (\"d%s=o.enums===String?%j:%j\", prop, field.resolvedType.valuesById[field.typeDefault], field.typeDefault);\n            else if (field.long) gen\n        (\"if(util.Long){\")\n            (\"var n=new util.Long(%i,%i,%j)\", field.typeDefault.low, field.typeDefault.high, field.typeDefault.unsigned)\n            (\"d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n\", prop)\n        (\"}else\")\n            (\"d%s=o.longs===String?%j:%i\", prop, field.typeDefault.toString(), field.typeDefault.toNumber());\n            else if (field.bytes) {\n                var arrayDefault = \"[\" + Array.prototype.slice.call(field.typeDefault).join(\",\") + \"]\";\n                gen\n        (\"if(o.bytes===String)d%s=%j\", prop, String.fromCharCode.apply(String, field.typeDefault))\n        (\"else{\")\n            (\"d%s=%s\", prop, arrayDefault)\n            (\"if(o.bytes!==Array)d%s=util.newBuffer(d%s)\", prop, prop)\n        (\"}\");\n            } else gen\n        (\"d%s=%j\", prop, field.typeDefault); // also messages (=null)\n        } gen\n    (\"}\");\n    }\n    var hasKs2 = false;\n    for (i = 0; i < fields.length; ++i) {\n        var field = fields[i],\n            index = mtype._fieldsArray.indexOf(field),\n            prop  = util.safeProp(field.name);\n        if (field.map) {\n            if (!hasKs2) { hasKs2 = true; gen\n    (\"var ks2\");\n            } gen\n    (\"if(m%s&&(ks2=Object.keys(m%s)).length){\", prop, prop)\n        (\"d%s={}\", prop)\n        (\"for(var j=0;j<ks2.length;++j){\");\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[ks2[j]]\")\n        (\"}\");\n        } else if (field.repeated) { gen\n    (\"if(m%s&&m%s.length){\", prop, prop)\n        (\"d%s=[]\", prop)\n        (\"for(var j=0;j<m%s.length;++j){\", prop);\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[j]\")\n        (\"}\");\n        } else { gen\n    (\"if(m%s!=null&&m.hasOwnProperty(%j)){\", prop, field.name); // !== undefined && !== null\n        genValuePartial_toObject(gen, field, /* sorted */ index, prop);\n        if (field.partOf) gen\n        (\"if(o.oneofs)\")\n            (\"d%s=%j\", util.safeProp(field.partOf.name), field.name);\n        }\n        gen\n    (\"}\");\n    }\n    return gen\n    (\"return d\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n};\n", "\"use strict\";\nmodule.exports = decoder;\n\nvar Enum    = require(15),\n    types   = require(36),\n    util    = require(37);\n\nfunction missing(field) {\n    return \"missing required '\" + field.name + \"'\";\n}\n\n/**\n * Generates a decoder specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction decoder(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n    var gen = util.codegen([\"r\", \"l\"], mtype.name + \"$decode\")\n    (\"if(!(r instanceof Reader))\")\n        (\"r=Reader.create(r)\")\n    (\"var c=l===undefined?r.len:r.pos+l,m=new this.ctor\" + (mtype.fieldsArray.filter(function(field) { return field.map; }).length ? \",k,value\" : \"\"))\n    (\"while(r.pos<c){\")\n        (\"var t=r.uint32()\");\n    if (mtype.group) gen\n        (\"if((t&7)===4)\")\n            (\"break\");\n    gen\n        (\"switch(t>>>3){\");\n\n    var i = 0;\n    for (; i < /* initializes */ mtype.fieldsArray.length; ++i) {\n        var field = mtype._fieldsArray[i].resolve(),\n            type  = field.resolvedType instanceof Enum ? \"int32\" : field.type,\n            ref   = \"m\" + util.safeProp(field.name); gen\n            (\"case %i:\", field.id);\n\n        // Map fields\n        if (field.map) { gen\n                (\"if(%s===util.emptyObject)\", ref)\n                    (\"%s={}\", ref)\n                (\"var c2 = r.uint32()+r.pos\");\n\n            if (types.defaults[field.keyType] !== undefined) gen\n                (\"k=%j\", types.defaults[field.keyType]);\n            else gen\n                (\"k=null\");\n\n            if (types.defaults[type] !== undefined) gen\n                (\"value=%j\", types.defaults[type]);\n            else gen\n                (\"value=null\");\n\n            gen\n                (\"while(r.pos<c2){\")\n                    (\"var tag2=r.uint32()\")\n                    (\"switch(tag2>>>3){\")\n                        (\"case 1: k=r.%s(); break\", field.keyType)\n                        (\"case 2:\");\n\n            if (types.basic[type] === undefined) gen\n                            (\"value=types[%i].decode(r,r.uint32())\", i); // can't be groups\n            else gen\n                            (\"value=r.%s()\", type);\n\n            gen\n                            (\"break\")\n                        (\"default:\")\n                            (\"r.skipType(tag2&7)\")\n                            (\"break\")\n                    (\"}\")\n                (\"}\");\n\n            if (types.long[field.keyType] !== undefined) gen\n                (\"%s[typeof k===\\\"object\\\"?util.longToHash(k):k]=value\", ref);\n            else gen\n                (\"%s[k]=value\", ref);\n\n        // Repeated fields\n        } else if (field.repeated) { gen\n\n                (\"if(!(%s&&%s.length))\", ref, ref)\n                    (\"%s=[]\", ref);\n\n            // Packable (always check for forward and backward compatiblity)\n            if (types.packed[type] !== undefined) gen\n                (\"if((t&7)===2){\")\n                    (\"var c2=r.uint32()+r.pos\")\n                    (\"while(r.pos<c2)\")\n                        (\"%s.push(r.%s())\", ref, type)\n                (\"}else\");\n\n            // Non-packed\n            if (types.basic[type] === undefined) gen(field.resolvedType.group\n                    ? \"%s.push(types[%i].decode(r))\"\n                    : \"%s.push(types[%i].decode(r,r.uint32()))\", ref, i);\n            else gen\n                    (\"%s.push(r.%s())\", ref, type);\n\n        // Non-repeated\n        } else if (types.basic[type] === undefined) gen(field.resolvedType.group\n                ? \"%s=types[%i].decode(r)\"\n                : \"%s=types[%i].decode(r,r.uint32())\", ref, i);\n        else gen\n                (\"%s=r.%s()\", ref, type);\n        gen\n                (\"break\");\n    // Unknown fields\n    } gen\n            (\"default:\")\n                (\"r.skipType(t&7)\")\n                (\"break\")\n\n        (\"}\")\n    (\"}\");\n\n    // Field presence\n    for (i = 0; i < mtype._fieldsArray.length; ++i) {\n        var rfield = mtype._fieldsArray[i];\n        if (rfield.required) gen\n    (\"if(!m.hasOwnProperty(%j))\", rfield.name)\n        (\"throw util.ProtocolError(%j,{instance:m})\", missing(rfield));\n    }\n\n    return gen\n    (\"return m\");\n    /* eslint-enable no-unexpected-multiline */\n}\n", "\"use strict\";\nmodule.exports = encoder;\n\nvar Enum     = require(15),\n    types    = require(36),\n    util     = require(37);\n\n/**\n * Generates a partial message type encoder.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genTypePartial(gen, field, fieldIndex, ref) {\n    return field.resolvedType.group\n        ? gen(\"types[%i].encode(%s,w.uint32(%i)).uint32(%i)\", fieldIndex, ref, (field.id << 3 | 3) >>> 0, (field.id << 3 | 4) >>> 0)\n        : gen(\"types[%i].encode(%s,w.uint32(%i).fork()).ldelim()\", fieldIndex, ref, (field.id << 3 | 2) >>> 0);\n}\n\n/**\n * Generates an encoder specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction encoder(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var gen = util.codegen([\"m\", \"w\"], mtype.name + \"$encode\")\n    (\"if(!w)\")\n        (\"w=Writer.create()\");\n\n    var i, ref;\n\n    // \"when a message is serialized its known fields should be written sequentially by field number\"\n    var fields = /* initializes */ mtype.fieldsArray.slice().sort(util.compareFieldsById);\n\n    for (var i = 0; i < fields.length; ++i) {\n        var field    = fields[i].resolve(),\n            index    = mtype._fieldsArray.indexOf(field),\n            type     = field.resolvedType instanceof Enum ? \"int32\" : field.type,\n            wireType = types.basic[type];\n            ref      = \"m\" + util.safeProp(field.name);\n\n        // Map fields\n        if (field.map) {\n            gen\n    (\"if(%s!=null&&Object.hasOwnProperty.call(m,%j)){\", ref, field.name) // !== undefined && !== null\n        (\"for(var ks=Object.keys(%s),i=0;i<ks.length;++i){\", ref)\n            (\"w.uint32(%i).fork().uint32(%i).%s(ks[i])\", (field.id << 3 | 2) >>> 0, 8 | types.mapKey[field.keyType], field.keyType);\n            if (wireType === undefined) gen\n            (\"types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()\", index, ref); // can't be groups\n            else gen\n            (\".uint32(%i).%s(%s[ks[i]]).ldelim()\", 16 | wireType, type, ref);\n            gen\n        (\"}\")\n    (\"}\");\n\n            // Repeated fields\n        } else if (field.repeated) { gen\n    (\"if(%s!=null&&%s.length){\", ref, ref); // !== undefined && !== null\n\n            // Packed repeated\n            if (field.packed && types.packed[type] !== undefined) { gen\n\n        (\"w.uint32(%i).fork()\", (field.id << 3 | 2) >>> 0)\n        (\"for(var i=0;i<%s.length;++i)\", ref)\n            (\"w.%s(%s[i])\", type, ref)\n        (\"w.ldelim()\");\n\n            // Non-packed\n            } else { gen\n\n        (\"for(var i=0;i<%s.length;++i)\", ref);\n                if (wireType === undefined)\n            genTypePartial(gen, field, index, ref + \"[i]\");\n                else gen\n            (\"w.uint32(%i).%s(%s[i])\", (field.id << 3 | wireType) >>> 0, type, ref);\n\n            } gen\n    (\"}\");\n\n        // Non-repeated\n        } else {\n            if (field.optional) gen\n    (\"if(%s!=null&&Object.hasOwnProperty.call(m,%j))\", ref, field.name); // !== undefined && !== null\n\n            if (wireType === undefined)\n        genTypePartial(gen, field, index, ref);\n            else gen\n        (\"w.uint32(%i).%s(%s)\", (field.id << 3 | wireType) >>> 0, type, ref);\n\n        }\n    }\n\n    return gen\n    (\"return w\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n", "\"use strict\";\nmodule.exports = Enum;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Enum.prototype = Object.create(ReflectionObject.prototype)).constructor = Enum).className = \"Enum\";\n\nvar Namespace = require(23),\n    util = require(37);\n\n/**\n * Constructs a new enum instance.\n * @classdesc Reflected enum.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {Object.<string,number>} [values] Enum values as an object, by name\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] The comment for this enum\n * @param {Object.<string,string>} [comments] The value comments for this enum\n */\nfunction Enum(name, values, options, comment, comments) {\n    ReflectionObject.call(this, name, options);\n\n    if (values && typeof values !== \"object\")\n        throw TypeError(\"values must be an object\");\n\n    /**\n     * Enum values by id.\n     * @type {Object.<number,string>}\n     */\n    this.valuesById = {};\n\n    /**\n     * Enum values by name.\n     * @type {Object.<string,number>}\n     */\n    this.values = Object.create(this.valuesById); // toJSON, marker\n\n    /**\n     * Enum comment text.\n     * @type {string|null}\n     */\n    this.comment = comment;\n\n    /**\n     * Value comment texts, if any.\n     * @type {Object.<string,string>}\n     */\n    this.comments = comments || {};\n\n    /**\n     * Reserved ranges, if any.\n     * @type {Array.<number[]|string>}\n     */\n    this.reserved = undefined; // toJSON\n\n    // Note that values inherit valuesById on their prototype which makes them a TypeScript-\n    // compatible enum. This is used by pbts to write actual enum definitions that work for\n    // static and reflection code alike instead of emitting generic object definitions.\n\n    if (values)\n        for (var keys = Object.keys(values), i = 0; i < keys.length; ++i)\n            if (typeof values[keys[i]] === \"number\") // use forward entries only\n                this.valuesById[ this.values[keys[i]] = values[keys[i]] ] = keys[i];\n}\n\n/**\n * Enum descriptor.\n * @interface IEnum\n * @property {Object.<string,number>} values Enum values\n * @property {Object.<string,*>} [options] Enum options\n */\n\n/**\n * Constructs an enum from an enum descriptor.\n * @param {string} name Enum name\n * @param {IEnum} json Enum descriptor\n * @returns {Enum} Created enum\n * @throws {TypeError} If arguments are invalid\n */\nEnum.fromJSON = function fromJSON(name, json) {\n    var enm = new Enum(name, json.values, json.options, json.comment, json.comments);\n    enm.reserved = json.reserved;\n    return enm;\n};\n\n/**\n * Converts this enum to an enum descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IEnum} Enum descriptor\n */\nEnum.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\"  , this.options,\n        \"values\"   , this.values,\n        \"reserved\" , this.reserved && this.reserved.length ? this.reserved : undefined,\n        \"comment\"  , keepComments ? this.comment : undefined,\n        \"comments\" , keepComments ? this.comments : undefined\n    ]);\n};\n\n/**\n * Adds a value to this enum.\n * @param {string} name Value name\n * @param {number} id Value id\n * @param {string} [comment] Comment, if any\n * @returns {Enum} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a value with this name or id\n */\nEnum.prototype.add = function add(name, id, comment) {\n    // utilized by the parser but not by .fromJSON\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    if (!util.isInteger(id))\n        throw TypeError(\"id must be an integer\");\n\n    if (this.values[name] !== undefined)\n        throw Error(\"duplicate name '\" + name + \"' in \" + this);\n\n    if (this.isReservedId(id))\n        throw Error(\"id \" + id + \" is reserved in \" + this);\n\n    if (this.isReservedName(name))\n        throw Error(\"name '\" + name + \"' is reserved in \" + this);\n\n    if (this.valuesById[id] !== undefined) {\n        if (!(this.options && this.options.allow_alias))\n            throw Error(\"duplicate id \" + id + \" in \" + this);\n        this.values[name] = id;\n    } else\n        this.valuesById[this.values[name] = id] = name;\n\n    this.comments[name] = comment || null;\n    return this;\n};\n\n/**\n * Removes a value from this enum\n * @param {string} name Value name\n * @returns {Enum} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `name` is not a name of this enum\n */\nEnum.prototype.remove = function remove(name) {\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    var val = this.values[name];\n    if (val == null)\n        throw Error(\"name '\" + name + \"' does not exist in \" + this);\n\n    delete this.valuesById[val];\n    delete this.values[name];\n    delete this.comments[name];\n\n    return this;\n};\n\n/**\n * Tests if the specified id is reserved.\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nEnum.prototype.isReservedId = function isReservedId(id) {\n    return Namespace.isReservedId(this.reserved, id);\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nEnum.prototype.isReservedName = function isReservedName(name) {\n    return Namespace.isReservedName(this.reserved, name);\n};\n", "\"use strict\";\nmodule.exports = Field;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Field.prototype = Object.create(ReflectionObject.prototype)).constructor = Field).className = \"Field\";\n\nvar Enum  = require(15),\n    types = require(36),\n    util  = require(37);\n\nvar Type; // cyclic\n\nvar ruleRe = /^required|optional|repeated$/;\n\n/**\n * Constructs a new message field instance. Note that {@link MapField|map fields} have their own class.\n * @name Field\n * @classdesc Reflected message field.\n * @extends FieldBase\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} type Value type\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\n * @param {Object.<string,*>} [options] Declared options\n */\n\n/**\n * Constructs a field from a field descriptor.\n * @param {string} name Field name\n * @param {IField} json Field descriptor\n * @returns {Field} Created field\n * @throws {TypeError} If arguments are invalid\n */\nField.fromJSON = function fromJSON(name, json) {\n    return new Field(name, json.id, json.type, json.rule, json.extend, json.options, json.comment);\n};\n\n/**\n * Not an actual constructor. Use {@link Field} instead.\n * @classdesc Base class of all reflected message fields. This is not an actual class but here for the sake of having consistent type definitions.\n * @exports FieldBase\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} type Value type\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction Field(name, id, type, rule, extend, options, comment) {\n\n    if (util.isObject(rule)) {\n        comment = extend;\n        options = rule;\n        rule = extend = undefined;\n    } else if (util.isObject(extend)) {\n        comment = options;\n        options = extend;\n        extend = undefined;\n    }\n\n    ReflectionObject.call(this, name, options);\n\n    if (!util.isInteger(id) || id < 0)\n        throw TypeError(\"id must be a non-negative integer\");\n\n    if (!util.isString(type))\n        throw TypeError(\"type must be a string\");\n\n    if (rule !== undefined && !ruleRe.test(rule = rule.toString().toLowerCase()))\n        throw TypeError(\"rule must be a string rule\");\n\n    if (extend !== undefined && !util.isString(extend))\n        throw TypeError(\"extend must be a string\");\n\n    if (rule === \"proto3_optional\") {\n        rule = \"optional\";\n    }\n    /**\n     * Field rule, if any.\n     * @type {string|undefined}\n     */\n    this.rule = rule && rule !== \"optional\" ? rule : undefined; // toJSON\n\n    /**\n     * Field type.\n     * @type {string}\n     */\n    this.type = type; // toJSON\n\n    /**\n     * Unique field id.\n     * @type {number}\n     */\n    this.id = id; // toJSON, marker\n\n    /**\n     * Extended type if different from parent.\n     * @type {string|undefined}\n     */\n    this.extend = extend || undefined; // toJSON\n\n    /**\n     * Whether this field is required.\n     * @type {boolean}\n     */\n    this.required = rule === \"required\";\n\n    /**\n     * Whether this field is optional.\n     * @type {boolean}\n     */\n    this.optional = !this.required;\n\n    /**\n     * Whether this field is repeated.\n     * @type {boolean}\n     */\n    this.repeated = rule === \"repeated\";\n\n    /**\n     * Whether this field is a map or not.\n     * @type {boolean}\n     */\n    this.map = false;\n\n    /**\n     * Message this field belongs to.\n     * @type {Type|null}\n     */\n    this.message = null;\n\n    /**\n     * OneOf this field belongs to, if any,\n     * @type {OneOf|null}\n     */\n    this.partOf = null;\n\n    /**\n     * The field type's default value.\n     * @type {*}\n     */\n    this.typeDefault = null;\n\n    /**\n     * The field's default value on prototypes.\n     * @type {*}\n     */\n    this.defaultValue = null;\n\n    /**\n     * Whether this field's value should be treated as a long.\n     * @type {boolean}\n     */\n    this.long = util.Long ? types.long[type] !== undefined : /* istanbul ignore next */ false;\n\n    /**\n     * Whether this field's value is a buffer.\n     * @type {boolean}\n     */\n    this.bytes = type === \"bytes\";\n\n    /**\n     * Resolved type if not a basic type.\n     * @type {Type|Enum|null}\n     */\n    this.resolvedType = null;\n\n    /**\n     * Sister-field within the extended type if a declaring extension field.\n     * @type {Field|null}\n     */\n    this.extensionField = null;\n\n    /**\n     * Sister-field within the declaring namespace if an extended field.\n     * @type {Field|null}\n     */\n    this.declaringField = null;\n\n    /**\n     * Internally remembers whether this field is packed.\n     * @type {boolean|null}\n     * @private\n     */\n    this._packed = null;\n\n    /**\n     * Comment for this field.\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Determines whether this field is packed. Only relevant when repeated and working with proto2.\n * @name Field#packed\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(Field.prototype, \"packed\", {\n    get: function() {\n        // defaults to packed=true if not explicity set to false\n        if (this._packed === null)\n            this._packed = this.getOption(\"packed\") !== false;\n        return this._packed;\n    }\n});\n\n/**\n * @override\n */\nField.prototype.setOption = function setOption(name, value, ifNotSet) {\n    if (name === \"packed\") // clear cached before setting\n        this._packed = null;\n    return ReflectionObject.prototype.setOption.call(this, name, value, ifNotSet);\n};\n\n/**\n * Field descriptor.\n * @interface IField\n * @property {string} [rule=\"optional\"] Field rule\n * @property {string} type Field type\n * @property {number} id Field id\n * @property {Object.<string,*>} [options] Field options\n */\n\n/**\n * Extension field descriptor.\n * @interface IExtensionField\n * @extends IField\n * @property {string} extend Extended type\n */\n\n/**\n * Converts this field to a field descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IField} Field descriptor\n */\nField.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"rule\"    , this.rule !== \"optional\" && this.rule || undefined,\n        \"type\"    , this.type,\n        \"id\"      , this.id,\n        \"extend\"  , this.extend,\n        \"options\" , this.options,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Resolves this field's type references.\n * @returns {Field} `this`\n * @throws {Error} If any reference cannot be resolved\n */\nField.prototype.resolve = function resolve() {\n\n    if (this.resolved)\n        return this;\n\n    if ((this.typeDefault = types.defaults[this.type]) === undefined) { // if not a basic type, resolve it\n        this.resolvedType = (this.declaringField ? this.declaringField.parent : this.parent).lookupTypeOrEnum(this.type);\n        if (this.resolvedType instanceof Type)\n            this.typeDefault = null;\n        else // instanceof Enum\n            this.typeDefault = this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]; // first defined\n    }\n\n    // use explicitly set default value if present\n    if (this.options && this.options[\"default\"] != null) {\n        this.typeDefault = this.options[\"default\"];\n        if (this.resolvedType instanceof Enum && typeof this.typeDefault === \"string\")\n            this.typeDefault = this.resolvedType.values[this.typeDefault];\n    }\n\n    // remove unnecessary options\n    if (this.options) {\n        if (this.options.packed === true || this.options.packed !== undefined && this.resolvedType && !(this.resolvedType instanceof Enum))\n            delete this.options.packed;\n        if (!Object.keys(this.options).length)\n            this.options = undefined;\n    }\n\n    // convert to internal data type if necesssary\n    if (this.long) {\n        this.typeDefault = util.Long.fromNumber(this.typeDefault, this.type.charAt(0) === \"u\");\n\n        /* istanbul ignore else */\n        if (Object.freeze)\n            Object.freeze(this.typeDefault); // long instances are meant to be immutable anyway (i.e. use small int cache that even requires it)\n\n    } else if (this.bytes && typeof this.typeDefault === \"string\") {\n        var buf;\n        if (util.base64.test(this.typeDefault))\n            util.base64.decode(this.typeDefault, buf = util.newBuffer(util.base64.length(this.typeDefault)), 0);\n        else\n            util.utf8.write(this.typeDefault, buf = util.newBuffer(util.utf8.length(this.typeDefault)), 0);\n        this.typeDefault = buf;\n    }\n\n    // take special care of maps and repeated fields\n    if (this.map)\n        this.defaultValue = util.emptyObject;\n    else if (this.repeated)\n        this.defaultValue = util.emptyArray;\n    else\n        this.defaultValue = this.typeDefault;\n\n    // ensure proper value on prototype\n    if (this.parent instanceof Type)\n        this.parent.ctor.prototype[this.name] = this.defaultValue;\n\n    return ReflectionObject.prototype.resolve.call(this);\n};\n\n/**\n * Decorator function as returned by {@link Field.d} and {@link MapField.d} (TypeScript).\n * @typedef FieldDecorator\n * @type {function}\n * @param {Object} prototype Target prototype\n * @param {string} fieldName Field name\n * @returns {undefined}\n */\n\n/**\n * Field decorator (TypeScript).\n * @name Field.d\n * @function\n * @param {number} fieldId Field id\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"string\"|\"bool\"|\"bytes\"|Object} fieldType Field type\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\n * @param {T} [defaultValue] Default value\n * @returns {FieldDecorator} Decorator function\n * @template T extends number | number[] | Long | Long[] | string | string[] | boolean | boolean[] | Uint8Array | Uint8Array[] | Buffer | Buffer[]\n */\nField.d = function decorateField(fieldId, fieldType, fieldRule, defaultValue) {\n\n    // submessage: decorate the submessage and use its name as the type\n    if (typeof fieldType === \"function\")\n        fieldType = util.decorateType(fieldType).name;\n\n    // enum reference: create a reflected copy of the enum and keep reuseing it\n    else if (fieldType && typeof fieldType === \"object\")\n        fieldType = util.decorateEnum(fieldType).name;\n\n    return function fieldDecorator(prototype, fieldName) {\n        util.decorateType(prototype.constructor)\n            .add(new Field(fieldName, fieldId, fieldType, fieldRule, { \"default\": defaultValue }));\n    };\n};\n\n/**\n * Field decorator (TypeScript).\n * @name Field.d\n * @function\n * @param {number} fieldId Field id\n * @param {Constructor<T>|string} fieldType Field type\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\n * @returns {FieldDecorator} Decorator function\n * @template T extends Message<T>\n * @variation 2\n */\n// like Field.d but without a default value\n\n// Sets up cyclic dependencies (called in index-light)\nField._configure = function configure(Type_) {\n    Type = Type_;\n};\n", "\"use strict\";\nvar protobuf = module.exports = require(18);\n\nprotobuf.build = \"light\";\n\n/**\n * A node-style callback as used by {@link load} and {@link Root#load}.\n * @typedef LoadCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Root} [root] Root, if there hasn't been an error\n * @returns {undefined}\n */\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} root Root namespace, defaults to create a new one if omitted.\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @see {@link Root#load}\n */\nfunction load(filename, root, callback) {\n    if (typeof root === \"function\") {\n        callback = root;\n        root = new protobuf.Root();\n    } else if (!root)\n        root = new protobuf.Root();\n    return root.load(filename, callback);\n}\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\n * @name load\n * @function\n * @param {string|string[]} filename One or multiple files to load\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @see {@link Root#load}\n * @variation 2\n */\n// function load(filename:string, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and returns a promise.\n * @name load\n * @function\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\n * @returns {Promise<Root>} Promise\n * @see {@link Root#load}\n * @variation 3\n */\n// function load(filename:string, [root:Root]):Promise<Root>\n\nprotobuf.load = load;\n\n/**\n * Synchronously loads one or multiple .proto or preprocessed .json files into a common root namespace (node only).\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\n * @returns {Root} Root namespace\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\n * @see {@link Root#loadSync}\n */\nfunction loadSync(filename, root) {\n    if (!root)\n        root = new protobuf.Root();\n    return root.loadSync(filename);\n}\n\nprotobuf.loadSync = loadSync;\n\n// Serialization\nprotobuf.encoder          = require(14);\nprotobuf.decoder          = require(13);\nprotobuf.verifier         = require(40);\nprotobuf.converter        = require(12);\n\n// Reflection\nprotobuf.ReflectionObject = require(24);\nprotobuf.Namespace        = require(23);\nprotobuf.Root             = require(29);\nprotobuf.Enum             = require(15);\nprotobuf.Type             = require(35);\nprotobuf.Field            = require(16);\nprotobuf.OneOf            = require(25);\nprotobuf.MapField         = require(20);\nprotobuf.Service          = require(33);\nprotobuf.Method           = require(22);\n\n// Runtime\nprotobuf.Message          = require(21);\nprotobuf.wrappers         = require(41);\n\n// Utility\nprotobuf.types            = require(36);\nprotobuf.util             = require(37);\n\n// Set up possibly cyclic reflection dependencies\nprotobuf.ReflectionObject._configure(protobuf.Root);\nprotobuf.Namespace._configure(protobuf.Type, protobuf.Service, protobuf.Enum);\nprotobuf.Root._configure(protobuf.Type);\nprotobuf.Field._configure(protobuf.Type);\n", "\"use strict\";\nvar protobuf = exports;\n\n/**\n * Build type, one of `\"full\"`, `\"light\"` or `\"minimal\"`.\n * @name build\n * @type {string}\n * @const\n */\nprotobuf.build = \"minimal\";\n\n// Serialization\nprotobuf.Writer       = require(42);\nprotobuf.BufferWriter = require(43);\nprotobuf.Reader       = require(27);\nprotobuf.BufferReader = require(28);\n\n// Utility\nprotobuf.util         = require(39);\nprotobuf.rpc          = require(31);\nprotobuf.roots        = require(30);\nprotobuf.configure    = configure;\n\n/* istanbul ignore next */\n/**\n * Reconfigures the library according to the environment.\n * @returns {undefined}\n */\nfunction configure() {\n    protobuf.util._configure();\n    protobuf.Writer._configure(protobuf.BufferWriter);\n    protobuf.Reader._configure(protobuf.BufferReader);\n}\n\n// Set up buffer utility according to the environment\nconfigure();\n", "\"use strict\";\nvar protobuf = module.exports = require(17);\n\nprotobuf.build = \"full\";\n\n// Parser\nprotobuf.tokenize         = require(34);\nprotobuf.parse            = require(26);\nprotobuf.common           = require(11);\n\n// Configure parser\nprotobuf.Root._configure(protobuf.Type, protobuf.parse, protobuf.common);\n", "\"use strict\";\nmodule.exports = MapField;\n\n// extends Field\nvar Field = require(16);\n((MapField.prototype = Object.create(Field.prototype)).constructor = MapField).className = \"MapField\";\n\nvar types   = require(36),\n    util    = require(37);\n\n/**\n * Constructs a new map field instance.\n * @classdesc Reflected map field.\n * @extends FieldBase\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} keyType Key type\n * @param {string} type Value type\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction MapField(name, id, keyType, type, options, comment) {\n    Field.call(this, name, id, type, undefined, undefined, options, comment);\n\n    /* istanbul ignore if */\n    if (!util.isString(keyType))\n        throw TypeError(\"keyType must be a string\");\n\n    /**\n     * Key type.\n     * @type {string}\n     */\n    this.keyType = keyType; // toJSON, marker\n\n    /**\n     * Resolved key type if not a basic type.\n     * @type {ReflectionObject|null}\n     */\n    this.resolvedKeyType = null;\n\n    // Overrides Field#map\n    this.map = true;\n}\n\n/**\n * Map field descriptor.\n * @interface IMapField\n * @extends {IField}\n * @property {string} keyType Key type\n */\n\n/**\n * Extension map field descriptor.\n * @interface IExtensionMapField\n * @extends IMapField\n * @property {string} extend Extended type\n */\n\n/**\n * Constructs a map field from a map field descriptor.\n * @param {string} name Field name\n * @param {IMapField} json Map field descriptor\n * @returns {MapField} Created map field\n * @throws {TypeError} If arguments are invalid\n */\nMapField.fromJSON = function fromJSON(name, json) {\n    return new MapField(name, json.id, json.keyType, json.type, json.options, json.comment);\n};\n\n/**\n * Converts this map field to a map field descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IMapField} Map field descriptor\n */\nMapField.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"keyType\" , this.keyType,\n        \"type\"    , this.type,\n        \"id\"      , this.id,\n        \"extend\"  , this.extend,\n        \"options\" , this.options,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nMapField.prototype.resolve = function resolve() {\n    if (this.resolved)\n        return this;\n\n    // Besides a value type, map fields have a key type that may be \"any scalar type except for floating point types and bytes\"\n    if (types.mapKey[this.keyType] === undefined)\n        throw Error(\"invalid key type: \" + this.keyType);\n\n    return Field.prototype.resolve.call(this);\n};\n\n/**\n * Map field decorator (TypeScript).\n * @name MapField.d\n * @function\n * @param {number} fieldId Field id\n * @param {\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"} fieldKeyType Field key type\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"|\"bytes\"|Object|Constructor<{}>} fieldValueType Field value type\n * @returns {FieldDecorator} Decorator function\n * @template T extends { [key: string]: number | Long | string | boolean | Uint8Array | Buffer | number[] | Message<{}> }\n */\nMapField.d = function decorateMapField(fieldId, fieldKeyType, fieldValueType) {\n\n    // submessage value: decorate the submessage and use its name as the type\n    if (typeof fieldValueType === \"function\")\n        fieldValueType = util.decorateType(fieldValueType).name;\n\n    // enum reference value: create a reflected copy of the enum and keep reuseing it\n    else if (fieldValueType && typeof fieldValueType === \"object\")\n        fieldValueType = util.decorateEnum(fieldValueType).name;\n\n    return function mapFieldDecorator(prototype, fieldName) {\n        util.decorateType(prototype.constructor)\n            .add(new MapField(fieldName, fieldId, fieldKeyType, fieldValueType));\n    };\n};\n", "\"use strict\";\nmodule.exports = Message;\n\nvar util = require(39);\n\n/**\n * Constructs a new message instance.\n * @classdesc Abstract runtime message.\n * @constructor\n * @param {Properties<T>} [properties] Properties to set\n * @template T extends object = object\n */\nfunction Message(properties) {\n    // not used internally\n    if (properties)\n        for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n            this[keys[i]] = properties[keys[i]];\n}\n\n/**\n * Reference to the reflected type.\n * @name Message.$type\n * @type {Type}\n * @readonly\n */\n\n/**\n * Reference to the reflected type.\n * @name Message#$type\n * @type {Type}\n * @readonly\n */\n\n/*eslint-disable valid-jsdoc*/\n\n/**\n * Creates a new message of this type using the specified properties.\n * @param {Object.<string,*>} [properties] Properties to set\n * @returns {Message<T>} Message instance\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.create = function create(properties) {\n    return this.$type.create(properties);\n};\n\n/**\n * Encodes a message of this type.\n * @param {T|Object.<string,*>} message Message to encode\n * @param {Writer} [writer] Writer to use\n * @returns {Writer} Writer\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.encode = function encode(message, writer) {\n    return this.$type.encode(message, writer);\n};\n\n/**\n * Encodes a message of this type preceeded by its length as a varint.\n * @param {T|Object.<string,*>} message Message to encode\n * @param {Writer} [writer] Writer to use\n * @returns {Writer} Writer\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.encodeDelimited = function encodeDelimited(message, writer) {\n    return this.$type.encodeDelimited(message, writer);\n};\n\n/**\n * Decodes a message of this type.\n * @name Message.decode\n * @function\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\n * @returns {T} Decoded message\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.decode = function decode(reader) {\n    return this.$type.decode(reader);\n};\n\n/**\n * Decodes a message of this type preceeded by its length as a varint.\n * @name Message.decodeDelimited\n * @function\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\n * @returns {T} Decoded message\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.decodeDelimited = function decodeDelimited(reader) {\n    return this.$type.decodeDelimited(reader);\n};\n\n/**\n * Verifies a message of this type.\n * @name Message.verify\n * @function\n * @param {Object.<string,*>} message Plain object to verify\n * @returns {string|null} `null` if valid, otherwise the reason why it is not\n */\nMessage.verify = function verify(message) {\n    return this.$type.verify(message);\n};\n\n/**\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\n * @param {Object.<string,*>} object Plain object\n * @returns {T} Message instance\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.fromObject = function fromObject(object) {\n    return this.$type.fromObject(object);\n};\n\n/**\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\n * @param {T} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.toObject = function toObject(message, options) {\n    return this.$type.toObject(message, options);\n};\n\n/**\n * Converts this message to JSON.\n * @returns {Object.<string,*>} JSON object\n */\nMessage.prototype.toJSON = function toJSON() {\n    return this.$type.toObject(this, util.toJSONOptions);\n};\n\n/*eslint-enable valid-jsdoc*/", "\"use strict\";\nmodule.exports = Method;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Method.prototype = Object.create(ReflectionObject.prototype)).constructor = Method).className = \"Method\";\n\nvar util = require(37);\n\n/**\n * Constructs a new service method instance.\n * @classdesc Reflected service method.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Method name\n * @param {string|undefined} type Method type, usually `\"rpc\"`\n * @param {string} requestType Request message type\n * @param {string} responseType Response message type\n * @param {boolean|Object.<string,*>} [requestStream] Whether the request is streamed\n * @param {boolean|Object.<string,*>} [responseStream] Whether the response is streamed\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] The comment for this method\n * @param {Object.<string,*>} [parsedOptions] Declared options, properly parsed into an object\n */\nfunction Method(name, type, requestType, responseType, requestStream, responseStream, options, comment, parsedOptions) {\n\n    /* istanbul ignore next */\n    if (util.isObject(requestStream)) {\n        options = requestStream;\n        requestStream = responseStream = undefined;\n    } else if (util.isObject(responseStream)) {\n        options = responseStream;\n        responseStream = undefined;\n    }\n\n    /* istanbul ignore if */\n    if (!(type === undefined || util.isString(type)))\n        throw TypeError(\"type must be a string\");\n\n    /* istanbul ignore if */\n    if (!util.isString(requestType))\n        throw TypeError(\"requestType must be a string\");\n\n    /* istanbul ignore if */\n    if (!util.isString(responseType))\n        throw TypeError(\"responseType must be a string\");\n\n    ReflectionObject.call(this, name, options);\n\n    /**\n     * Method type.\n     * @type {string}\n     */\n    this.type = type || \"rpc\"; // toJSON\n\n    /**\n     * Request type.\n     * @type {string}\n     */\n    this.requestType = requestType; // toJSON, marker\n\n    /**\n     * Whether requests are streamed or not.\n     * @type {boolean|undefined}\n     */\n    this.requestStream = requestStream ? true : undefined; // toJSON\n\n    /**\n     * Response type.\n     * @type {string}\n     */\n    this.responseType = responseType; // toJSON\n\n    /**\n     * Whether responses are streamed or not.\n     * @type {boolean|undefined}\n     */\n    this.responseStream = responseStream ? true : undefined; // toJSON\n\n    /**\n     * Resolved request type.\n     * @type {Type|null}\n     */\n    this.resolvedRequestType = null;\n\n    /**\n     * Resolved response type.\n     * @type {Type|null}\n     */\n    this.resolvedResponseType = null;\n\n    /**\n     * Comment for this method\n     * @type {string|null}\n     */\n    this.comment = comment;\n\n    /**\n     * Options properly parsed into an object\n     */\n    this.parsedOptions = parsedOptions;\n}\n\n/**\n * Method descriptor.\n * @interface IMethod\n * @property {string} [type=\"rpc\"] Method type\n * @property {string} requestType Request type\n * @property {string} responseType Response type\n * @property {boolean} [requestStream=false] Whether requests are streamed\n * @property {boolean} [responseStream=false] Whether responses are streamed\n * @property {Object.<string,*>} [options] Method options\n * @property {string} comment Method comments\n * @property {Object.<string,*>} [parsedOptions] Method options properly parsed into an object\n */\n\n/**\n * Constructs a method from a method descriptor.\n * @param {string} name Method name\n * @param {IMethod} json Method descriptor\n * @returns {Method} Created method\n * @throws {TypeError} If arguments are invalid\n */\nMethod.fromJSON = function fromJSON(name, json) {\n    return new Method(name, json.type, json.requestType, json.responseType, json.requestStream, json.responseStream, json.options, json.comment, json.parsedOptions);\n};\n\n/**\n * Converts this method to a method descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IMethod} Method descriptor\n */\nMethod.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"type\"           , this.type !== \"rpc\" && /* istanbul ignore next */ this.type || undefined,\n        \"requestType\"    , this.requestType,\n        \"requestStream\"  , this.requestStream,\n        \"responseType\"   , this.responseType,\n        \"responseStream\" , this.responseStream,\n        \"options\"        , this.options,\n        \"comment\"        , keepComments ? this.comment : undefined,\n        \"parsedOptions\"  , this.parsedOptions,\n    ]);\n};\n\n/**\n * @override\n */\nMethod.prototype.resolve = function resolve() {\n\n    /* istanbul ignore if */\n    if (this.resolved)\n        return this;\n\n    this.resolvedRequestType = this.parent.lookupType(this.requestType);\n    this.resolvedResponseType = this.parent.lookupType(this.responseType);\n\n    return ReflectionObject.prototype.resolve.call(this);\n};\n", "\"use strict\";\nmodule.exports = Namespace;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Namespace.prototype = Object.create(ReflectionObject.prototype)).constructor = Namespace).className = \"Namespace\";\n\nvar Field    = require(16),\n    OneOf    = require(25),\n    util     = require(37);\n\nvar Type,    // cyclic\n    Service,\n    Enum;\n\n/**\n * Constructs a new namespace instance.\n * @name Namespace\n * @classdesc Reflected namespace.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Namespace name\n * @param {Object.<string,*>} [options] Declared options\n */\n\n/**\n * Constructs a namespace from JSON.\n * @memberof Namespace\n * @function\n * @param {string} name Namespace name\n * @param {Object.<string,*>} json JSON object\n * @returns {Namespace} Created namespace\n * @throws {TypeError} If arguments are invalid\n */\nNamespace.fromJSON = function fromJSON(name, json) {\n    return new Namespace(name, json.options).addJSON(json.nested);\n};\n\n/**\n * Converts an array of reflection objects to JSON.\n * @memberof Namespace\n * @param {ReflectionObject[]} array Object array\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {Object.<string,*>|undefined} JSON object or `undefined` when array is empty\n */\nfunction arrayToJSON(array, toJSONOptions) {\n    if (!(array && array.length))\n        return undefined;\n    var obj = {};\n    for (var i = 0; i < array.length; ++i)\n        obj[array[i].name] = array[i].toJSON(toJSONOptions);\n    return obj;\n}\n\nNamespace.arrayToJSON = arrayToJSON;\n\n/**\n * Tests if the specified id is reserved.\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nNamespace.isReservedId = function isReservedId(reserved, id) {\n    if (reserved)\n        for (var i = 0; i < reserved.length; ++i)\n            if (typeof reserved[i] !== \"string\" && reserved[i][0] <= id && reserved[i][1] > id)\n                return true;\n    return false;\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nNamespace.isReservedName = function isReservedName(reserved, name) {\n    if (reserved)\n        for (var i = 0; i < reserved.length; ++i)\n            if (reserved[i] === name)\n                return true;\n    return false;\n};\n\n/**\n * Not an actual constructor. Use {@link Namespace} instead.\n * @classdesc Base class of all reflection objects containing nested objects. This is not an actual class but here for the sake of having consistent type definitions.\n * @exports NamespaceBase\n * @extends ReflectionObject\n * @abstract\n * @constructor\n * @param {string} name Namespace name\n * @param {Object.<string,*>} [options] Declared options\n * @see {@link Namespace}\n */\nfunction Namespace(name, options) {\n    ReflectionObject.call(this, name, options);\n\n    /**\n     * Nested objects by name.\n     * @type {Object.<string,ReflectionObject>|undefined}\n     */\n    this.nested = undefined; // toJSON\n\n    /**\n     * Cached nested objects as an array.\n     * @type {ReflectionObject[]|null}\n     * @private\n     */\n    this._nestedArray = null;\n}\n\nfunction clearCache(namespace) {\n    namespace._nestedArray = null;\n    return namespace;\n}\n\n/**\n * Nested objects of this namespace as an array for iteration.\n * @name NamespaceBase#nestedArray\n * @type {ReflectionObject[]}\n * @readonly\n */\nObject.defineProperty(Namespace.prototype, \"nestedArray\", {\n    get: function() {\n        return this._nestedArray || (this._nestedArray = util.toArray(this.nested));\n    }\n});\n\n/**\n * Namespace descriptor.\n * @interface INamespace\n * @property {Object.<string,*>} [options] Namespace options\n * @property {Object.<string,AnyNestedObject>} [nested] Nested object descriptors\n */\n\n/**\n * Any extension field descriptor.\n * @typedef AnyExtensionField\n * @type {IExtensionField|IExtensionMapField}\n */\n\n/**\n * Any nested object descriptor.\n * @typedef AnyNestedObject\n * @type {IEnum|IType|IService|AnyExtensionField|INamespace}\n */\n// ^ BEWARE: VSCode hangs forever when using more than 5 types (that's why AnyExtensionField exists in the first place)\n\n/**\n * Converts this namespace to a namespace descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {INamespace} Namespace descriptor\n */\nNamespace.prototype.toJSON = function toJSON(toJSONOptions) {\n    return util.toObject([\n        \"options\" , this.options,\n        \"nested\"  , arrayToJSON(this.nestedArray, toJSONOptions)\n    ]);\n};\n\n/**\n * Adds nested objects to this namespace from nested object descriptors.\n * @param {Object.<string,AnyNestedObject>} nestedJson Any nested object descriptors\n * @returns {Namespace} `this`\n */\nNamespace.prototype.addJSON = function addJSON(nestedJson) {\n    var ns = this;\n    /* istanbul ignore else */\n    if (nestedJson) {\n        for (var names = Object.keys(nestedJson), i = 0, nested; i < names.length; ++i) {\n            nested = nestedJson[names[i]];\n            ns.add( // most to least likely\n                ( nested.fields !== undefined\n                ? Type.fromJSON\n                : nested.values !== undefined\n                ? Enum.fromJSON\n                : nested.methods !== undefined\n                ? Service.fromJSON\n                : nested.id !== undefined\n                ? Field.fromJSON\n                : Namespace.fromJSON )(names[i], nested)\n            );\n        }\n    }\n    return this;\n};\n\n/**\n * Gets the nested object of the specified name.\n * @param {string} name Nested object name\n * @returns {ReflectionObject|null} The reflection object or `null` if it doesn't exist\n */\nNamespace.prototype.get = function get(name) {\n    return this.nested && this.nested[name]\n        || null;\n};\n\n/**\n * Gets the values of the nested {@link Enum|enum} of the specified name.\n * This methods differs from {@link Namespace#get|get} in that it returns an enum's values directly and throws instead of returning `null`.\n * @param {string} name Nested enum name\n * @returns {Object.<string,number>} Enum values\n * @throws {Error} If there is no such enum\n */\nNamespace.prototype.getEnum = function getEnum(name) {\n    if (this.nested && this.nested[name] instanceof Enum)\n        return this.nested[name].values;\n    throw Error(\"no such enum: \" + name);\n};\n\n/**\n * Adds a nested object to this namespace.\n * @param {ReflectionObject} object Nested object to add\n * @returns {Namespace} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a nested object with this name\n */\nNamespace.prototype.add = function add(object) {\n\n    if (!(object instanceof Field && object.extend !== undefined || object instanceof Type || object instanceof Enum || object instanceof Service || object instanceof Namespace || object instanceof OneOf))\n        throw TypeError(\"object must be a valid nested object\");\n\n    if (!this.nested)\n        this.nested = {};\n    else {\n        var prev = this.get(object.name);\n        if (prev) {\n            if (prev instanceof Namespace && object instanceof Namespace && !(prev instanceof Type || prev instanceof Service)) {\n                // replace plain namespace but keep existing nested elements and options\n                var nested = prev.nestedArray;\n                for (var i = 0; i < nested.length; ++i)\n                    object.add(nested[i]);\n                this.remove(prev);\n                if (!this.nested)\n                    this.nested = {};\n                object.setOptions(prev.options, true);\n\n            } else\n                throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n        }\n    }\n    this.nested[object.name] = object;\n    object.onAdd(this);\n    return clearCache(this);\n};\n\n/**\n * Removes a nested object from this namespace.\n * @param {ReflectionObject} object Nested object to remove\n * @returns {Namespace} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `object` is not a member of this namespace\n */\nNamespace.prototype.remove = function remove(object) {\n\n    if (!(object instanceof ReflectionObject))\n        throw TypeError(\"object must be a ReflectionObject\");\n    if (object.parent !== this)\n        throw Error(object + \" is not a member of \" + this);\n\n    delete this.nested[object.name];\n    if (!Object.keys(this.nested).length)\n        this.nested = undefined;\n\n    object.onRemove(this);\n    return clearCache(this);\n};\n\n/**\n * Defines additial namespaces within this one if not yet existing.\n * @param {string|string[]} path Path to create\n * @param {*} [json] Nested types to create from JSON\n * @returns {Namespace} Pointer to the last namespace created or `this` if path is empty\n */\nNamespace.prototype.define = function define(path, json) {\n\n    if (util.isString(path))\n        path = path.split(\".\");\n    else if (!Array.isArray(path))\n        throw TypeError(\"illegal path\");\n    if (path && path.length && path[0] === \"\")\n        throw Error(\"path must be relative\");\n\n    var ptr = this;\n    while (path.length > 0) {\n        var part = path.shift();\n        if (ptr.nested && ptr.nested[part]) {\n            ptr = ptr.nested[part];\n            if (!(ptr instanceof Namespace))\n                throw Error(\"path conflicts with non-namespace objects\");\n        } else\n            ptr.add(ptr = new Namespace(part));\n    }\n    if (json)\n        ptr.addJSON(json);\n    return ptr;\n};\n\n/**\n * Resolves this namespace's and all its nested objects' type references. Useful to validate a reflection tree, but comes at a cost.\n * @returns {Namespace} `this`\n */\nNamespace.prototype.resolveAll = function resolveAll() {\n    var nested = this.nestedArray, i = 0;\n    while (i < nested.length)\n        if (nested[i] instanceof Namespace)\n            nested[i++].resolveAll();\n        else\n            nested[i++].resolve();\n    return this.resolve();\n};\n\n/**\n * Recursively looks up the reflection object matching the specified path in the scope of this namespace.\n * @param {string|string[]} path Path to look up\n * @param {*|Array.<*>} filterTypes Filter types, any combination of the constructors of `protobuf.Type`, `protobuf.Enum`, `protobuf.Service` etc.\n * @param {boolean} [parentAlreadyChecked=false] If known, whether the parent has already been checked\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n */\nNamespace.prototype.lookup = function lookup(path, filterTypes, parentAlreadyChecked) {\n\n    /* istanbul ignore next */\n    if (typeof filterTypes === \"boolean\") {\n        parentAlreadyChecked = filterTypes;\n        filterTypes = undefined;\n    } else if (filterTypes && !Array.isArray(filterTypes))\n        filterTypes = [ filterTypes ];\n\n    if (util.isString(path) && path.length) {\n        if (path === \".\")\n            return this.root;\n        path = path.split(\".\");\n    } else if (!path.length)\n        return this;\n\n    // Start at root if path is absolute\n    if (path[0] === \"\")\n        return this.root.lookup(path.slice(1), filterTypes);\n\n    // Test if the first part matches any nested object, and if so, traverse if path contains more\n    var found = this.get(path[0]);\n    if (found) {\n        if (path.length === 1) {\n            if (!filterTypes || filterTypes.indexOf(found.constructor) > -1)\n                return found;\n        } else if (found instanceof Namespace && (found = found.lookup(path.slice(1), filterTypes, true)))\n            return found;\n\n    // Otherwise try each nested namespace\n    } else\n        for (var i = 0; i < this.nestedArray.length; ++i)\n            if (this._nestedArray[i] instanceof Namespace && (found = this._nestedArray[i].lookup(path, filterTypes, true)))\n                return found;\n\n    // If there hasn't been a match, try again at the parent\n    if (this.parent === null || parentAlreadyChecked)\n        return null;\n    return this.parent.lookup(path, filterTypes);\n};\n\n/**\n * Looks up the reflection object at the specified path, relative to this namespace.\n * @name NamespaceBase#lookup\n * @function\n * @param {string|string[]} path Path to look up\n * @param {boolean} [parentAlreadyChecked=false] Whether the parent has already been checked\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n * @variation 2\n */\n// lookup(path: string, [parentAlreadyChecked: boolean])\n\n/**\n * Looks up the {@link Type|type} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Type} Looked up type\n * @throws {Error} If `path` does not point to a type\n */\nNamespace.prototype.lookupType = function lookupType(path) {\n    var found = this.lookup(path, [ Type ]);\n    if (!found)\n        throw Error(\"no such type: \" + path);\n    return found;\n};\n\n/**\n * Looks up the values of the {@link Enum|enum} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Enum} Looked up enum\n * @throws {Error} If `path` does not point to an enum\n */\nNamespace.prototype.lookupEnum = function lookupEnum(path) {\n    var found = this.lookup(path, [ Enum ]);\n    if (!found)\n        throw Error(\"no such Enum '\" + path + \"' in \" + this);\n    return found;\n};\n\n/**\n * Looks up the {@link Type|type} or {@link Enum|enum} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Type} Looked up type or enum\n * @throws {Error} If `path` does not point to a type or enum\n */\nNamespace.prototype.lookupTypeOrEnum = function lookupTypeOrEnum(path) {\n    var found = this.lookup(path, [ Type, Enum ]);\n    if (!found)\n        throw Error(\"no such Type or Enum '\" + path + \"' in \" + this);\n    return found;\n};\n\n/**\n * Looks up the {@link Service|service} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Service} Looked up service\n * @throws {Error} If `path` does not point to a service\n */\nNamespace.prototype.lookupService = function lookupService(path) {\n    var found = this.lookup(path, [ Service ]);\n    if (!found)\n        throw Error(\"no such Service '\" + path + \"' in \" + this);\n    return found;\n};\n\n// Sets up cyclic dependencies (called in index-light)\nNamespace._configure = function(Type_, Service_, Enum_) {\n    Type    = Type_;\n    Service = Service_;\n    Enum    = Enum_;\n};\n", "\"use strict\";\nmodule.exports = ReflectionObject;\n\nReflectionObject.className = \"ReflectionObject\";\n\nvar util = require(37);\n\nvar Root; // cyclic\n\n/**\n * Constructs a new reflection object instance.\n * @classdesc Base class of all reflection objects.\n * @constructor\n * @param {string} name Object name\n * @param {Object.<string,*>} [options] Declared options\n * @abstract\n */\nfunction ReflectionObject(name, options) {\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    if (options && !util.isObject(options))\n        throw TypeError(\"options must be an object\");\n\n    /**\n     * Options.\n     * @type {Object.<string,*>|undefined}\n     */\n    this.options = options; // toJSON\n\n    /**\n     * Parsed Options.\n     * @type {Array.<Object.<string,*>>|undefined}\n     */\n    this.parsedOptions = null;\n\n    /**\n     * Unique name within its namespace.\n     * @type {string}\n     */\n    this.name = name;\n\n    /**\n     * Parent namespace.\n     * @type {Namespace|null}\n     */\n    this.parent = null;\n\n    /**\n     * Whether already resolved or not.\n     * @type {boolean}\n     */\n    this.resolved = false;\n\n    /**\n     * Comment text, if any.\n     * @type {string|null}\n     */\n    this.comment = null;\n\n    /**\n     * Defining file name.\n     * @type {string|null}\n     */\n    this.filename = null;\n}\n\nObject.defineProperties(ReflectionObject.prototype, {\n\n    /**\n     * Reference to the root namespace.\n     * @name ReflectionObject#root\n     * @type {Root}\n     * @readonly\n     */\n    root: {\n        get: function() {\n            var ptr = this;\n            while (ptr.parent !== null)\n                ptr = ptr.parent;\n            return ptr;\n        }\n    },\n\n    /**\n     * Full name including leading dot.\n     * @name ReflectionObject#fullName\n     * @type {string}\n     * @readonly\n     */\n    fullName: {\n        get: function() {\n            var path = [ this.name ],\n                ptr = this.parent;\n            while (ptr) {\n                path.unshift(ptr.name);\n                ptr = ptr.parent;\n            }\n            return path.join(\".\");\n        }\n    }\n});\n\n/**\n * Converts this reflection object to its descriptor representation.\n * @returns {Object.<string,*>} Descriptor\n * @abstract\n */\nReflectionObject.prototype.toJSON = /* istanbul ignore next */ function toJSON() {\n    throw Error(); // not implemented, shouldn't happen\n};\n\n/**\n * Called when this object is added to a parent.\n * @param {ReflectionObject} parent Parent added to\n * @returns {undefined}\n */\nReflectionObject.prototype.onAdd = function onAdd(parent) {\n    if (this.parent && this.parent !== parent)\n        this.parent.remove(this);\n    this.parent = parent;\n    this.resolved = false;\n    var root = parent.root;\n    if (root instanceof Root)\n        root._handleAdd(this);\n};\n\n/**\n * Called when this object is removed from a parent.\n * @param {ReflectionObject} parent Parent removed from\n * @returns {undefined}\n */\nReflectionObject.prototype.onRemove = function onRemove(parent) {\n    var root = parent.root;\n    if (root instanceof Root)\n        root._handleRemove(this);\n    this.parent = null;\n    this.resolved = false;\n};\n\n/**\n * Resolves this objects type references.\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.resolve = function resolve() {\n    if (this.resolved)\n        return this;\n    if (this.root instanceof Root)\n        this.resolved = true; // only if part of a root\n    return this;\n};\n\n/**\n * Gets an option value.\n * @param {string} name Option name\n * @returns {*} Option value or `undefined` if not set\n */\nReflectionObject.prototype.getOption = function getOption(name) {\n    if (this.options)\n        return this.options[name];\n    return undefined;\n};\n\n/**\n * Sets an option.\n * @param {string} name Option name\n * @param {*} value Option value\n * @param {boolean} [ifNotSet] Sets the option only if it isn't currently set\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setOption = function setOption(name, value, ifNotSet) {\n    if (!ifNotSet || !this.options || this.options[name] === undefined)\n        (this.options || (this.options = {}))[name] = value;\n    return this;\n};\n\n/**\n * Sets a parsed option.\n * @param {string} name parsed Option name\n * @param {*} value Option value\n * @param {string} propName dot '.' delimited full path of property within the option to set. if undefined\\empty, will add a new option with that value\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setParsedOption = function setParsedOption(name, value, propName) {\n    if (!this.parsedOptions) {\n        this.parsedOptions = [];\n    }\n    var parsedOptions = this.parsedOptions;\n    if (propName) {\n        // If setting a sub property of an option then try to merge it\n        // with an existing option\n        var opt = parsedOptions.find(function (opt) {\n            return Object.prototype.hasOwnProperty.call(opt, name);\n        });\n        if (opt) {\n            // If we found an existing option - just merge the property value\n            var newValue = opt[name];\n            util.setProperty(newValue, propName, value);\n        } else {\n            // otherwise, create a new option, set it's property and add it to the list\n            opt = {};\n            opt[name] = util.setProperty({}, propName, value);\n            parsedOptions.push(opt);\n        }\n    } else {\n        // Always create a new option when setting the value of the option itself\n        var newOpt = {};\n        newOpt[name] = value;\n        parsedOptions.push(newOpt);\n    }\n    return this;\n};\n\n/**\n * Sets multiple options.\n * @param {Object.<string,*>} options Options to set\n * @param {boolean} [ifNotSet] Sets an option only if it isn't currently set\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setOptions = function setOptions(options, ifNotSet) {\n    if (options)\n        for (var keys = Object.keys(options), i = 0; i < keys.length; ++i)\n            this.setOption(keys[i], options[keys[i]], ifNotSet);\n    return this;\n};\n\n/**\n * Converts this instance to its string representation.\n * @returns {string} Class name[, space, full name]\n */\nReflectionObject.prototype.toString = function toString() {\n    var className = this.constructor.className,\n        fullName  = this.fullName;\n    if (fullName.length)\n        return className + \" \" + fullName;\n    return className;\n};\n\n// Sets up cyclic dependencies (called in index-light)\nReflectionObject._configure = function(Root_) {\n    Root = Root_;\n};\n", "\"use strict\";\nmodule.exports = OneOf;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((OneOf.prototype = Object.create(ReflectionObject.prototype)).constructor = OneOf).className = \"OneOf\";\n\nvar Field = require(16),\n    util  = require(37);\n\n/**\n * Constructs a new oneof instance.\n * @classdesc Reflected oneof.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Oneof name\n * @param {string[]|Object.<string,*>} [fieldNames] Field names\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction OneOf(name, fieldNames, options, comment) {\n    if (!Array.isArray(fieldNames)) {\n        options = fieldNames;\n        fieldNames = undefined;\n    }\n    ReflectionObject.call(this, name, options);\n\n    /* istanbul ignore if */\n    if (!(fieldNames === undefined || Array.isArray(fieldNames)))\n        throw TypeError(\"fieldNames must be an Array\");\n\n    /**\n     * Field names that belong to this oneof.\n     * @type {string[]}\n     */\n    this.oneof = fieldNames || []; // toJSON, marker\n\n    /**\n     * Fields that belong to this oneof as an array for iteration.\n     * @type {Field[]}\n     * @readonly\n     */\n    this.fieldsArray = []; // declared readonly for conformance, possibly not yet added to parent\n\n    /**\n     * Comment for this field.\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Oneof descriptor.\n * @interface IOneOf\n * @property {Array.<string>} oneof Oneof field names\n * @property {Object.<string,*>} [options] Oneof options\n */\n\n/**\n * Constructs a oneof from a oneof descriptor.\n * @param {string} name Oneof name\n * @param {IOneOf} json Oneof descriptor\n * @returns {OneOf} Created oneof\n * @throws {TypeError} If arguments are invalid\n */\nOneOf.fromJSON = function fromJSON(name, json) {\n    return new OneOf(name, json.oneof, json.options, json.comment);\n};\n\n/**\n * Converts this oneof to a oneof descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IOneOf} Oneof descriptor\n */\nOneOf.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\" , this.options,\n        \"oneof\"   , this.oneof,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Adds the fields of the specified oneof to the parent if not already done so.\n * @param {OneOf} oneof The oneof\n * @returns {undefined}\n * @inner\n * @ignore\n */\nfunction addFieldsToParent(oneof) {\n    if (oneof.parent)\n        for (var i = 0; i < oneof.fieldsArray.length; ++i)\n            if (!oneof.fieldsArray[i].parent)\n                oneof.parent.add(oneof.fieldsArray[i]);\n}\n\n/**\n * Adds a field to this oneof and removes it from its current parent, if any.\n * @param {Field} field Field to add\n * @returns {OneOf} `this`\n */\nOneOf.prototype.add = function add(field) {\n\n    /* istanbul ignore if */\n    if (!(field instanceof Field))\n        throw TypeError(\"field must be a Field\");\n\n    if (field.parent && field.parent !== this.parent)\n        field.parent.remove(field);\n    this.oneof.push(field.name);\n    this.fieldsArray.push(field);\n    field.partOf = this; // field.parent remains null\n    addFieldsToParent(this);\n    return this;\n};\n\n/**\n * Removes a field from this oneof and puts it back to the oneof's parent.\n * @param {Field} field Field to remove\n * @returns {OneOf} `this`\n */\nOneOf.prototype.remove = function remove(field) {\n\n    /* istanbul ignore if */\n    if (!(field instanceof Field))\n        throw TypeError(\"field must be a Field\");\n\n    var index = this.fieldsArray.indexOf(field);\n\n    /* istanbul ignore if */\n    if (index < 0)\n        throw Error(field + \" is not a member of \" + this);\n\n    this.fieldsArray.splice(index, 1);\n    index = this.oneof.indexOf(field.name);\n\n    /* istanbul ignore else */\n    if (index > -1) // theoretical\n        this.oneof.splice(index, 1);\n\n    field.partOf = null;\n    return this;\n};\n\n/**\n * @override\n */\nOneOf.prototype.onAdd = function onAdd(parent) {\n    ReflectionObject.prototype.onAdd.call(this, parent);\n    var self = this;\n    // Collect present fields\n    for (var i = 0; i < this.oneof.length; ++i) {\n        var field = parent.get(this.oneof[i]);\n        if (field && !field.partOf) {\n            field.partOf = self;\n            self.fieldsArray.push(field);\n        }\n    }\n    // Add not yet present fields\n    addFieldsToParent(this);\n};\n\n/**\n * @override\n */\nOneOf.prototype.onRemove = function onRemove(parent) {\n    for (var i = 0, field; i < this.fieldsArray.length; ++i)\n        if ((field = this.fieldsArray[i]).parent)\n            field.parent.remove(field);\n    ReflectionObject.prototype.onRemove.call(this, parent);\n};\n\n/**\n * Decorator function as returned by {@link OneOf.d} (TypeScript).\n * @typedef OneOfDecorator\n * @type {function}\n * @param {Object} prototype Target prototype\n * @param {string} oneofName OneOf name\n * @returns {undefined}\n */\n\n/**\n * OneOf decorator (TypeScript).\n * @function\n * @param {...string} fieldNames Field names\n * @returns {OneOfDecorator} Decorator function\n * @template T extends string\n */\nOneOf.d = function decorateOneOf() {\n    var fieldNames = new Array(arguments.length),\n        index = 0;\n    while (index < arguments.length)\n        fieldNames[index] = arguments[index++];\n    return function oneOfDecorator(prototype, oneofName) {\n        util.decorateType(prototype.constructor)\n            .add(new OneOf(oneofName, fieldNames));\n        Object.defineProperty(prototype, oneofName, {\n            get: util.oneOfGetter(fieldNames),\n            set: util.oneOfSetter(fieldNames)\n        });\n    };\n};\n", "\"use strict\";\nmodule.exports = parse;\n\nparse.filename = null;\nparse.defaults = { keepCase: false };\n\nvar tokenize  = require(34),\n    Root      = require(29),\n    Type      = require(35),\n    Field     = require(16),\n    MapField  = require(20),\n    OneOf     = require(25),\n    Enum      = require(15),\n    Service   = require(33),\n    Method    = require(22),\n    types     = require(36),\n    util      = require(37);\n\nvar base10Re    = /^[1-9][0-9]*$/,\n    base10NegRe = /^-?[1-9][0-9]*$/,\n    base16Re    = /^0[x][0-9a-fA-F]+$/,\n    base16NegRe = /^-?0[x][0-9a-fA-F]+$/,\n    base8Re     = /^0[0-7]+$/,\n    base8NegRe  = /^-?0[0-7]+$/,\n    numberRe    = /^(?![eE])[0-9]*(?:\\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/,\n    nameRe      = /^[a-zA-Z_][a-zA-Z_0-9]*$/,\n    typeRefRe   = /^(?:\\.?[a-zA-Z_][a-zA-Z_0-9]*)(?:\\.[a-zA-Z_][a-zA-Z_0-9]*)*$/,\n    fqTypeRefRe = /^(?:\\.[a-zA-Z_][a-zA-Z_0-9]*)+$/;\n\n/**\n * Result object returned from {@link parse}.\n * @interface IParserResult\n * @property {string|undefined} package Package name, if declared\n * @property {string[]|undefined} imports Imports, if any\n * @property {string[]|undefined} weakImports Weak imports, if any\n * @property {string|undefined} syntax Syntax, if specified (either `\"proto2\"` or `\"proto3\"`)\n * @property {Root} root Populated root instance\n */\n\n/**\n * Options modifying the behavior of {@link parse}.\n * @interface IParseOptions\n * @property {boolean} [keepCase=false] Keeps field casing instead of converting to camel case\n * @property {boolean} [alternateCommentMode=false] Recognize double-slash comments in addition to doc-block comments.\n * @property {boolean} [preferTrailingComment=false] Use trailing comment when both leading comment and trailing comment exist.\n */\n\n/**\n * Options modifying the behavior of JSON serialization.\n * @interface IToJSONOptions\n * @property {boolean} [keepComments=false] Serializes comments.\n */\n\n/**\n * Parses the given .proto source and returns an object with the parsed contents.\n * @param {string} source Source contents\n * @param {Root} root Root to populate\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {IParserResult} Parser result\n * @property {string} filename=null Currently processing file name for error reporting, if known\n * @property {IParseOptions} defaults Default {@link IParseOptions}\n */\nfunction parse(source, root, options) {\n    /* eslint-disable callback-return */\n    if (!(root instanceof Root)) {\n        options = root;\n        root = new Root();\n    }\n    if (!options)\n        options = parse.defaults;\n\n    var preferTrailingComment = options.preferTrailingComment || false;\n    var tn = tokenize(source, options.alternateCommentMode || false),\n        next = tn.next,\n        push = tn.push,\n        peek = tn.peek,\n        skip = tn.skip,\n        cmnt = tn.cmnt;\n\n    var head = true,\n        pkg,\n        imports,\n        weakImports,\n        syntax,\n        isProto3 = false;\n\n    var ptr = root;\n\n    var applyCase = options.keepCase ? function(name) { return name; } : util.camelCase;\n\n    /* istanbul ignore next */\n    function illegal(token, name, insideTryCatch) {\n        var filename = parse.filename;\n        if (!insideTryCatch)\n            parse.filename = null;\n        return Error(\"illegal \" + (name || \"token\") + \" '\" + token + \"' (\" + (filename ? filename + \", \" : \"\") + \"line \" + tn.line + \")\");\n    }\n\n    function readString() {\n        var values = [],\n            token;\n        do {\n            /* istanbul ignore if */\n            if ((token = next()) !== \"\\\"\" && token !== \"'\")\n                throw illegal(token);\n\n            values.push(next());\n            skip(token);\n            token = peek();\n        } while (token === \"\\\"\" || token === \"'\");\n        return values.join(\"\");\n    }\n\n    function readValue(acceptTypeRef) {\n        var token = next();\n        switch (token) {\n            case \"'\":\n            case \"\\\"\":\n                push(token);\n                return readString();\n            case \"true\": case \"TRUE\":\n                return true;\n            case \"false\": case \"FALSE\":\n                return false;\n        }\n        try {\n            return parseNumber(token, /* insideTryCatch */ true);\n        } catch (e) {\n\n            /* istanbul ignore else */\n            if (acceptTypeRef && typeRefRe.test(token))\n                return token;\n\n            /* istanbul ignore next */\n            throw illegal(token, \"value\");\n        }\n    }\n\n    function readRanges(target, acceptStrings) {\n        var token, start;\n        do {\n            if (acceptStrings && ((token = peek()) === \"\\\"\" || token === \"'\"))\n                target.push(readString());\n            else\n                target.push([ start = parseId(next()), skip(\"to\", true) ? parseId(next()) : start ]);\n        } while (skip(\",\", true));\n        skip(\";\");\n    }\n\n    function parseNumber(token, insideTryCatch) {\n        var sign = 1;\n        if (token.charAt(0) === \"-\") {\n            sign = -1;\n            token = token.substring(1);\n        }\n        switch (token) {\n            case \"inf\": case \"INF\": case \"Inf\":\n                return sign * Infinity;\n            case \"nan\": case \"NAN\": case \"Nan\": case \"NaN\":\n                return NaN;\n            case \"0\":\n                return 0;\n        }\n        if (base10Re.test(token))\n            return sign * parseInt(token, 10);\n        if (base16Re.test(token))\n            return sign * parseInt(token, 16);\n        if (base8Re.test(token))\n            return sign * parseInt(token, 8);\n\n        /* istanbul ignore else */\n        if (numberRe.test(token))\n            return sign * parseFloat(token);\n\n        /* istanbul ignore next */\n        throw illegal(token, \"number\", insideTryCatch);\n    }\n\n    function parseId(token, acceptNegative) {\n        switch (token) {\n            case \"max\": case \"MAX\": case \"Max\":\n                return 536870911;\n            case \"0\":\n                return 0;\n        }\n\n        /* istanbul ignore if */\n        if (!acceptNegative && token.charAt(0) === \"-\")\n            throw illegal(token, \"id\");\n\n        if (base10NegRe.test(token))\n            return parseInt(token, 10);\n        if (base16NegRe.test(token))\n            return parseInt(token, 16);\n\n        /* istanbul ignore else */\n        if (base8NegRe.test(token))\n            return parseInt(token, 8);\n\n        /* istanbul ignore next */\n        throw illegal(token, \"id\");\n    }\n\n    function parsePackage() {\n\n        /* istanbul ignore if */\n        if (pkg !== undefined)\n            throw illegal(\"package\");\n\n        pkg = next();\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(pkg))\n            throw illegal(pkg, \"name\");\n\n        ptr = ptr.define(pkg);\n        skip(\";\");\n    }\n\n    function parseImport() {\n        var token = peek();\n        var whichImports;\n        switch (token) {\n            case \"weak\":\n                whichImports = weakImports || (weakImports = []);\n                next();\n                break;\n            case \"public\":\n                next();\n                // eslint-disable-line no-fallthrough\n            default:\n                whichImports = imports || (imports = []);\n                break;\n        }\n        token = readString();\n        skip(\";\");\n        whichImports.push(token);\n    }\n\n    function parseSyntax() {\n        skip(\"=\");\n        syntax = readString();\n        isProto3 = syntax === \"proto3\";\n\n        /* istanbul ignore if */\n        if (!isProto3 && syntax !== \"proto2\")\n            throw illegal(syntax, \"syntax\");\n\n        skip(\";\");\n    }\n\n    function parseCommon(parent, token) {\n        switch (token) {\n\n            case \"option\":\n                parseOption(parent, token);\n                skip(\";\");\n                return true;\n\n            case \"message\":\n                parseType(parent, token);\n                return true;\n\n            case \"enum\":\n                parseEnum(parent, token);\n                return true;\n\n            case \"service\":\n                parseService(parent, token);\n                return true;\n\n            case \"extend\":\n                parseExtension(parent, token);\n                return true;\n        }\n        return false;\n    }\n\n    function ifBlock(obj, fnIf, fnElse) {\n        var trailingLine = tn.line;\n        if (obj) {\n            if(typeof obj.comment !== \"string\") {\n              obj.comment = cmnt(); // try block-type comment\n            }\n            obj.filename = parse.filename;\n        }\n        if (skip(\"{\", true)) {\n            var token;\n            while ((token = next()) !== \"}\")\n                fnIf(token);\n            skip(\";\", true);\n        } else {\n            if (fnElse)\n                fnElse();\n            skip(\";\");\n            if (obj && (typeof obj.comment !== \"string\" || preferTrailingComment))\n                obj.comment = cmnt(trailingLine) || obj.comment; // try line-type comment\n        }\n    }\n\n    function parseType(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"type name\");\n\n        var type = new Type(token);\n        ifBlock(type, function parseType_block(token) {\n            if (parseCommon(type, token))\n                return;\n\n            switch (token) {\n\n                case \"map\":\n                    parseMapField(type, token);\n                    break;\n\n                case \"required\":\n                case \"repeated\":\n                    parseField(type, token);\n                    break;\n\n                case \"optional\":\n                    /* istanbul ignore if */\n                    if (isProto3) {\n                        parseField(type, \"proto3_optional\");\n                    } else {\n                        parseField(type, \"optional\");\n                    }\n                    break;\n\n                case \"oneof\":\n                    parseOneOf(type, token);\n                    break;\n\n                case \"extensions\":\n                    readRanges(type.extensions || (type.extensions = []));\n                    break;\n\n                case \"reserved\":\n                    readRanges(type.reserved || (type.reserved = []), true);\n                    break;\n\n                default:\n                    /* istanbul ignore if */\n                    if (!isProto3 || !typeRefRe.test(token))\n                        throw illegal(token);\n\n                    push(token);\n                    parseField(type, \"optional\");\n                    break;\n            }\n        });\n        parent.add(type);\n    }\n\n    function parseField(parent, rule, extend) {\n        var type = next();\n        if (type === \"group\") {\n            parseGroup(parent, rule);\n            return;\n        }\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(type))\n            throw illegal(type, \"type\");\n\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        name = applyCase(name);\n        skip(\"=\");\n\n        var field = new Field(name, parseId(next()), type, rule, extend);\n        ifBlock(field, function parseField_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(field, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseField_line() {\n            parseInlineOptions(field);\n        });\n\n        if (rule === \"proto3_optional\") {\n            // for proto3 optional fields, we create a single-member Oneof to mimic \"optional\" behavior\n            var oneof = new OneOf(\"_\" + name);\n            field.setOption(\"proto3_optional\", true);\n            oneof.add(field);\n            parent.add(oneof);\n        } else {\n            parent.add(field);\n        }\n\n        // JSON defaults to packed=true if not set so we have to set packed=false explicity when\n        // parsing proto2 descriptors without the option, where applicable. This must be done for\n        // all known packable types and anything that could be an enum (= is not a basic type).\n        if (!isProto3 && field.repeated && (types.packed[type] !== undefined || types.basic[type] === undefined))\n            field.setOption(\"packed\", false, /* ifNotSet */ true);\n    }\n\n    function parseGroup(parent, rule) {\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        var fieldName = util.lcFirst(name);\n        if (name === fieldName)\n            name = util.ucFirst(name);\n        skip(\"=\");\n        var id = parseId(next());\n        var type = new Type(name);\n        type.group = true;\n        var field = new Field(fieldName, id, name, rule);\n        field.filename = parse.filename;\n        ifBlock(type, function parseGroup_block(token) {\n            switch (token) {\n\n                case \"option\":\n                    parseOption(type, token);\n                    skip(\";\");\n                    break;\n\n                case \"required\":\n                case \"repeated\":\n                    parseField(type, token);\n                    break;\n\n                case \"optional\":\n                    /* istanbul ignore if */\n                    if (isProto3) {\n                        parseField(type, \"proto3_optional\");\n                    } else {\n                        parseField(type, \"optional\");\n                    }\n                    break;\n\n                /* istanbul ignore next */\n                default:\n                    throw illegal(token); // there are no groups with proto3 semantics\n            }\n        });\n        parent.add(type)\n              .add(field);\n    }\n\n    function parseMapField(parent) {\n        skip(\"<\");\n        var keyType = next();\n\n        /* istanbul ignore if */\n        if (types.mapKey[keyType] === undefined)\n            throw illegal(keyType, \"type\");\n\n        skip(\",\");\n        var valueType = next();\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(valueType))\n            throw illegal(valueType, \"type\");\n\n        skip(\">\");\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        skip(\"=\");\n        var field = new MapField(applyCase(name), parseId(next()), keyType, valueType);\n        ifBlock(field, function parseMapField_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(field, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseMapField_line() {\n            parseInlineOptions(field);\n        });\n        parent.add(field);\n    }\n\n    function parseOneOf(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var oneof = new OneOf(applyCase(token));\n        ifBlock(oneof, function parseOneOf_block(token) {\n            if (token === \"option\") {\n                parseOption(oneof, token);\n                skip(\";\");\n            } else {\n                push(token);\n                parseField(oneof, \"optional\");\n            }\n        });\n        parent.add(oneof);\n    }\n\n    function parseEnum(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var enm = new Enum(token);\n        ifBlock(enm, function parseEnum_block(token) {\n          switch(token) {\n            case \"option\":\n              parseOption(enm, token);\n              skip(\";\");\n              break;\n\n            case \"reserved\":\n              readRanges(enm.reserved || (enm.reserved = []), true);\n              break;\n\n            default:\n              parseEnumValue(enm, token);\n          }\n        });\n        parent.add(enm);\n    }\n\n    function parseEnumValue(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token))\n            throw illegal(token, \"name\");\n\n        skip(\"=\");\n        var value = parseId(next(), true),\n            dummy = {};\n        ifBlock(dummy, function parseEnumValue_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(dummy, token); // skip\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseEnumValue_line() {\n            parseInlineOptions(dummy); // skip\n        });\n        parent.add(token, value, dummy.comment);\n    }\n\n    function parseOption(parent, token) {\n        var isCustom = skip(\"(\", true);\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var name = token;\n        var option = name;\n        var propName;\n\n        if (isCustom) {\n            skip(\")\");\n            name = \"(\" + name + \")\";\n            option = name;\n            token = peek();\n            if (fqTypeRefRe.test(token)) {\n                propName = token.substr(1); //remove '.' before property name\n                name += token;\n                next();\n            }\n        }\n        skip(\"=\");\n        var optionValue = parseOptionValue(parent, name);\n        setParsedOption(parent, option, optionValue, propName);\n    }\n\n    function parseOptionValue(parent, name) {\n        if (skip(\"{\", true)) { // { a: \"foo\" b { c: \"bar\" } }\n            var result = {};\n            while (!skip(\"}\", true)) {\n                /* istanbul ignore if */\n                if (!nameRe.test(token = next()))\n                    throw illegal(token, \"name\");\n\n                var value;\n                var propName = token;\n                if (peek() === \"{\")\n                    value = parseOptionValue(parent, name + \".\" + token);\n                else {\n                    skip(\":\");\n                    if (peek() === \"{\")\n                        value = parseOptionValue(parent, name + \".\" + token);\n                    else {\n                        value = readValue(true);\n                        setOption(parent, name + \".\" + token, value);\n                    }\n                }\n                var prevValue = result[propName];\n                if (prevValue)\n                    value = [].concat(prevValue).concat(value);\n                result[propName] = value;\n                skip(\",\", true);\n            }\n            return result;\n        }\n\n        var simpleValue = readValue(true);\n        setOption(parent, name, simpleValue);\n        return simpleValue;\n        // Does not enforce a delimiter to be universal\n    }\n\n    function setOption(parent, name, value) {\n        if (parent.setOption)\n            parent.setOption(name, value);\n    }\n\n    function setParsedOption(parent, name, value, propName) {\n        if (parent.setParsedOption)\n            parent.setParsedOption(name, value, propName);\n    }\n\n    function parseInlineOptions(parent) {\n        if (skip(\"[\", true)) {\n            do {\n                parseOption(parent, \"option\");\n            } while (skip(\",\", true));\n            skip(\"]\");\n        }\n        return parent;\n    }\n\n    function parseService(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"service name\");\n\n        var service = new Service(token);\n        ifBlock(service, function parseService_block(token) {\n            if (parseCommon(service, token))\n                return;\n\n            /* istanbul ignore else */\n            if (token === \"rpc\")\n                parseMethod(service, token);\n            else\n                throw illegal(token);\n        });\n        parent.add(service);\n    }\n\n    function parseMethod(parent, token) {\n        // Get the comment of the preceding line now (if one exists) in case the\n        // method is defined across multiple lines.\n        var commentText = cmnt();\n\n        var type = token;\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var name = token,\n            requestType, requestStream,\n            responseType, responseStream;\n\n        skip(\"(\");\n        if (skip(\"stream\", true))\n            requestStream = true;\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token);\n\n        requestType = token;\n        skip(\")\"); skip(\"returns\"); skip(\"(\");\n        if (skip(\"stream\", true))\n            responseStream = true;\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token);\n\n        responseType = token;\n        skip(\")\");\n\n        var method = new Method(name, type, requestType, responseType, requestStream, responseStream);\n        method.comment = commentText;\n        ifBlock(method, function parseMethod_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(method, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        });\n        parent.add(method);\n    }\n\n    function parseExtension(parent, token) {\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token, \"reference\");\n\n        var reference = token;\n        ifBlock(null, function parseExtension_block(token) {\n            switch (token) {\n\n                case \"required\":\n                case \"repeated\":\n                    parseField(parent, token, reference);\n                    break;\n\n                case \"optional\":\n                    /* istanbul ignore if */\n                    if (isProto3) {\n                        parseField(parent, \"proto3_optional\", reference);\n                    } else {\n                        parseField(parent, \"optional\", reference);\n                    }\n                    break;\n\n                default:\n                    /* istanbul ignore if */\n                    if (!isProto3 || !typeRefRe.test(token))\n                        throw illegal(token);\n                    push(token);\n                    parseField(parent, \"optional\", reference);\n                    break;\n            }\n        });\n    }\n\n    var token;\n    while ((token = next()) !== null) {\n        switch (token) {\n\n            case \"package\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parsePackage();\n                break;\n\n            case \"import\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parseImport();\n                break;\n\n            case \"syntax\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parseSyntax();\n                break;\n\n            case \"option\":\n\n                parseOption(ptr, token);\n                skip(\";\");\n                break;\n\n            default:\n\n                /* istanbul ignore else */\n                if (parseCommon(ptr, token)) {\n                    head = false;\n                    continue;\n                }\n\n                /* istanbul ignore next */\n                throw illegal(token);\n        }\n    }\n\n    parse.filename = null;\n    return {\n        \"package\"     : pkg,\n        \"imports\"     : imports,\n         weakImports  : weakImports,\n         syntax       : syntax,\n         root         : root\n    };\n}\n\n/**\n * Parses the given .proto source and returns an object with the parsed contents.\n * @name parse\n * @function\n * @param {string} source Source contents\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {IParserResult} Parser result\n * @property {string} filename=null Currently processing file name for error reporting, if known\n * @property {IParseOptions} defaults Default {@link IParseOptions}\n * @variation 2\n */\n", "\"use strict\";\nmodule.exports = Reader;\n\nvar util      = require(39);\n\nvar BufferReader; // cyclic\n\nvar LongBits  = util.LongBits,\n    utf8      = util.utf8;\n\n/* istanbul ignore next */\nfunction indexOutOfRange(reader, writeLength) {\n    return RangeError(\"index out of range: \" + reader.pos + \" + \" + (writeLength || 1) + \" > \" + reader.len);\n}\n\n/**\n * Constructs a new reader instance using the specified buffer.\n * @classdesc Wire format reader using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n * @param {Uint8Array} buffer Buffer to read from\n */\nfunction Reader(buffer) {\n\n    /**\n     * Read buffer.\n     * @type {Uint8Array}\n     */\n    this.buf = buffer;\n\n    /**\n     * Read buffer position.\n     * @type {number}\n     */\n    this.pos = 0;\n\n    /**\n     * Read buffer length.\n     * @type {number}\n     */\n    this.len = buffer.length;\n}\n\nvar create_array = typeof Uint8Array !== \"undefined\"\n    ? function create_typed_array(buffer) {\n        if (buffer instanceof Uint8Array || Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    }\n    /* istanbul ignore next */\n    : function create_array(buffer) {\n        if (Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    };\n\nvar create = function create() {\n    return util.Buffer\n        ? function create_buffer_setup(buffer) {\n            return (Reader.create = function create_buffer(buffer) {\n                return util.Buffer.isBuffer(buffer)\n                    ? new BufferReader(buffer)\n                    /* istanbul ignore next */\n                    : create_array(buffer);\n            })(buffer);\n        }\n        /* istanbul ignore next */\n        : create_array;\n};\n\n/**\n * Creates a new reader using the specified buffer.\n * @function\n * @param {Uint8Array|Buffer} buffer Buffer to read from\n * @returns {Reader|BufferReader} A {@link BufferReader} if `buffer` is a Buffer, otherwise a {@link Reader}\n * @throws {Error} If `buffer` is not a valid buffer\n */\nReader.create = create();\n\nReader.prototype._slice = util.Array.prototype.subarray || /* istanbul ignore next */ util.Array.prototype.slice;\n\n/**\n * Reads a varint as an unsigned 32 bit value.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.uint32 = (function read_uint32_setup() {\n    var value = 4294967295; // optimizer type-hint, tends to deopt otherwise (?!)\n    return function read_uint32() {\n        value = (         this.buf[this.pos] & 127       ) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) <<  7) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 14) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 21) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] &  15) << 28) >>> 0; if (this.buf[this.pos++] < 128) return value;\n\n        /* istanbul ignore if */\n        if ((this.pos += 5) > this.len) {\n            this.pos = this.len;\n            throw indexOutOfRange(this, 10);\n        }\n        return value;\n    };\n})();\n\n/**\n * Reads a varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.int32 = function read_int32() {\n    return this.uint32() | 0;\n};\n\n/**\n * Reads a zig-zag encoded varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.sint32 = function read_sint32() {\n    var value = this.uint32();\n    return value >>> 1 ^ -(value & 1) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readLongVarint() {\n    // tends to deopt with local vars for octet etc.\n    var bits = new LongBits(0, 0);\n    var i = 0;\n    if (this.len - this.pos > 4) { // fast route (lo)\n        for (; i < 4; ++i) {\n            // 1st..4th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 5th\n        bits.lo = (bits.lo | (this.buf[this.pos] & 127) << 28) >>> 0;\n        bits.hi = (bits.hi | (this.buf[this.pos] & 127) >>  4) >>> 0;\n        if (this.buf[this.pos++] < 128)\n            return bits;\n        i = 0;\n    } else {\n        for (; i < 3; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 1st..3th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 4th\n        bits.lo = (bits.lo | (this.buf[this.pos++] & 127) << i * 7) >>> 0;\n        return bits;\n    }\n    if (this.len - this.pos > 4) { // fast route (hi)\n        for (; i < 5; ++i) {\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    } else {\n        for (; i < 5; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    }\n    /* istanbul ignore next */\n    throw Error(\"invalid varint encoding\");\n}\n\n/* eslint-enable no-invalid-this */\n\n/**\n * Reads a varint as a signed 64 bit value.\n * @name Reader#int64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as an unsigned 64 bit value.\n * @name Reader#uint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a zig-zag encoded varint as a signed 64 bit value.\n * @name Reader#sint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as a boolean.\n * @returns {boolean} Value read\n */\nReader.prototype.bool = function read_bool() {\n    return this.uint32() !== 0;\n};\n\nfunction readFixed32_end(buf, end) { // note that this uses `end`, not `pos`\n    return (buf[end - 4]\n          | buf[end - 3] << 8\n          | buf[end - 2] << 16\n          | buf[end - 1] << 24) >>> 0;\n}\n\n/**\n * Reads fixed 32 bits as an unsigned 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.fixed32 = function read_fixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4);\n};\n\n/**\n * Reads fixed 32 bits as a signed 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.sfixed32 = function read_sfixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readFixed64(/* this: Reader */) {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 8);\n\n    return new LongBits(readFixed32_end(this.buf, this.pos += 4), readFixed32_end(this.buf, this.pos += 4));\n}\n\n/* eslint-enable no-invalid-this */\n\n/**\n * Reads fixed 64 bits.\n * @name Reader#fixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads zig-zag encoded fixed 64 bits.\n * @name Reader#sfixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a float (32 bit) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.float = function read_float() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readFloatLE(this.buf, this.pos);\n    this.pos += 4;\n    return value;\n};\n\n/**\n * Reads a double (64 bit float) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.double = function read_double() {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readDoubleLE(this.buf, this.pos);\n    this.pos += 8;\n    return value;\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @returns {Uint8Array} Value read\n */\nReader.prototype.bytes = function read_bytes() {\n    var length = this.uint32(),\n        start  = this.pos,\n        end    = this.pos + length;\n\n    /* istanbul ignore if */\n    if (end > this.len)\n        throw indexOutOfRange(this, length);\n\n    this.pos += length;\n    if (Array.isArray(this.buf)) // plain array\n        return this.buf.slice(start, end);\n    return start === end // fix for IE 10/Win8 and others' subarray returning array of size 1\n        ? new this.buf.constructor(0)\n        : this._slice.call(this.buf, start, end);\n};\n\n/**\n * Reads a string preceeded by its byte length as a varint.\n * @returns {string} Value read\n */\nReader.prototype.string = function read_string() {\n    var bytes = this.bytes();\n    return utf8.read(bytes, 0, bytes.length);\n};\n\n/**\n * Skips the specified number of bytes if specified, otherwise skips a varint.\n * @param {number} [length] Length if known, otherwise a varint is assumed\n * @returns {Reader} `this`\n */\nReader.prototype.skip = function skip(length) {\n    if (typeof length === \"number\") {\n        /* istanbul ignore if */\n        if (this.pos + length > this.len)\n            throw indexOutOfRange(this, length);\n        this.pos += length;\n    } else {\n        do {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n        } while (this.buf[this.pos++] & 128);\n    }\n    return this;\n};\n\n/**\n * Skips the next element of the specified wire type.\n * @param {number} wireType Wire type received\n * @returns {Reader} `this`\n */\nReader.prototype.skipType = function(wireType) {\n    switch (wireType) {\n        case 0:\n            this.skip();\n            break;\n        case 1:\n            this.skip(8);\n            break;\n        case 2:\n            this.skip(this.uint32());\n            break;\n        case 3:\n            while ((wireType = this.uint32() & 7) !== 4) {\n                this.skipType(wireType);\n            }\n            break;\n        case 5:\n            this.skip(4);\n            break;\n\n        /* istanbul ignore next */\n        default:\n            throw Error(\"invalid wire type \" + wireType + \" at offset \" + this.pos);\n    }\n    return this;\n};\n\nReader._configure = function(BufferReader_) {\n    BufferReader = BufferReader_;\n    Reader.create = create();\n    BufferReader._configure();\n\n    var fn = util.Long ? \"toLong\" : /* istanbul ignore next */ \"toNumber\";\n    util.merge(Reader.prototype, {\n\n        int64: function read_int64() {\n            return readLongVarint.call(this)[fn](false);\n        },\n\n        uint64: function read_uint64() {\n            return readLongVarint.call(this)[fn](true);\n        },\n\n        sint64: function read_sint64() {\n            return readLongVarint.call(this).zzDecode()[fn](false);\n        },\n\n        fixed64: function read_fixed64() {\n            return readFixed64.call(this)[fn](true);\n        },\n\n        sfixed64: function read_sfixed64() {\n            return readFixed64.call(this)[fn](false);\n        }\n\n    });\n};\n", "\"use strict\";\nmodule.exports = B<PERSON>erReader;\n\n// extends Reader\nvar Reader = require(27);\n(BufferReader.prototype = Object.create(Reader.prototype)).constructor = BufferReader;\n\nvar util = require(39);\n\n/**\n * Constructs a new buffer reader instance.\n * @classdesc Wire format reader using node buffers.\n * @extends Reader\n * @constructor\n * @param {Buffer} buffer Buffer to read from\n */\nfunction BufferReader(buffer) {\n    Reader.call(this, buffer);\n\n    /**\n     * Read buffer.\n     * @name BufferReader#buf\n     * @type {Buffer}\n     */\n}\n\nBufferReader._configure = function () {\n    /* istanbul ignore else */\n    if (util.Buffer)\n        BufferReader.prototype._slice = util.Buffer.prototype.slice;\n};\n\n\n/**\n * @override\n */\nBufferReader.prototype.string = function read_string_buffer() {\n    var len = this.uint32(); // modifies pos\n    return this.buf.utf8Slice\n        ? this.buf.utf8Slice(this.pos, this.pos = Math.min(this.pos + len, this.len))\n        : this.buf.toString(\"utf-8\", this.pos, this.pos = Math.min(this.pos + len, this.len));\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @name BufferReader#bytes\n * @function\n * @returns {Buffer} Value read\n */\n\nBufferReader._configure();\n", "\"use strict\";\nmodule.exports = Root;\n\n// extends Namespace\nvar Namespace = require(23);\n((Root.prototype = Object.create(Namespace.prototype)).constructor = Root).className = \"Root\";\n\nvar Field   = require(16),\n    Enum    = require(15),\n    OneOf   = require(25),\n    util    = require(37);\n\nvar Type,   // cyclic\n    parse,  // might be excluded\n    common; // \"\n\n/**\n * Constructs a new root namespace instance.\n * @classdesc Root namespace wrapping all types, enums, services, sub-namespaces etc. that belong together.\n * @extends NamespaceBase\n * @constructor\n * @param {Object.<string,*>} [options] Top level options\n */\nfunction Root(options) {\n    Namespace.call(this, \"\", options);\n\n    /**\n     * Deferred extension fields.\n     * @type {Field[]}\n     */\n    this.deferred = [];\n\n    /**\n     * Resolved file names of loaded files.\n     * @type {string[]}\n     */\n    this.files = [];\n}\n\n/**\n * Loads a namespace descriptor into a root namespace.\n * @param {INamespace} json Nameespace descriptor\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted\n * @returns {Root} Root namespace\n */\nRoot.fromJSON = function fromJSON(json, root) {\n    if (!root)\n        root = new Root();\n    if (json.options)\n        root.setOptions(json.options);\n    return root.addJSON(json.nested);\n};\n\n/**\n * Resolves the path of an imported file, relative to the importing origin.\n * This method exists so you can override it with your own logic in case your imports are scattered over multiple directories.\n * @function\n * @param {string} origin The file name of the importing file\n * @param {string} target The file name being imported\n * @returns {string|null} Resolved path to `target` or `null` to skip the file\n */\nRoot.prototype.resolvePath = util.path.resolve;\n\n/**\n * Fetch content from file path or url\n * This method exists so you can override it with your own logic.\n * @function\n * @param {string} path File path or url\n * @param {FetchCallback} callback Callback function\n * @returns {undefined}\n */\nRoot.prototype.fetch = util.fetch;\n\n// A symbol-like function to safely signal synchronous loading\n/* istanbul ignore next */\nfunction SYNC() {} // eslint-disable-line no-empty-function\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} options Parse options\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n */\nRoot.prototype.load = function load(filename, options, callback) {\n    if (typeof options === \"function\") {\n        callback = options;\n        options = undefined;\n    }\n    var self = this;\n    if (!callback)\n        return util.asPromise(load, self, filename, options);\n\n    var sync = callback === SYNC; // undocumented\n\n    // Finishes loading by calling the callback (exactly once)\n    function finish(err, root) {\n        /* istanbul ignore if */\n        if (!callback)\n            return;\n        var cb = callback;\n        callback = null;\n        if (sync)\n            throw err;\n        cb(err, root);\n    }\n\n    // Bundled definition existence checking\n    function getBundledFileName(filename) {\n        var idx = filename.lastIndexOf(\"google/protobuf/\");\n        if (idx > -1) {\n            var altname = filename.substring(idx);\n            if (altname in common) return altname;\n        }\n        return null;\n    }\n\n    // Processes a single file\n    function process(filename, source) {\n        try {\n            if (util.isString(source) && source.charAt(0) === \"{\")\n                source = JSON.parse(source);\n            if (!util.isString(source))\n                self.setOptions(source.options).addJSON(source.nested);\n            else {\n                parse.filename = filename;\n                var parsed = parse(source, self, options),\n                    resolved,\n                    i = 0;\n                if (parsed.imports)\n                    for (; i < parsed.imports.length; ++i)\n                        if (resolved = getBundledFileName(parsed.imports[i]) || self.resolvePath(filename, parsed.imports[i]))\n                            fetch(resolved);\n                if (parsed.weakImports)\n                    for (i = 0; i < parsed.weakImports.length; ++i)\n                        if (resolved = getBundledFileName(parsed.weakImports[i]) || self.resolvePath(filename, parsed.weakImports[i]))\n                            fetch(resolved, true);\n            }\n        } catch (err) {\n            finish(err);\n        }\n        if (!sync && !queued)\n            finish(null, self); // only once anyway\n    }\n\n    // Fetches a single file\n    function fetch(filename, weak) {\n\n        // Skip if already loaded / attempted\n        if (self.files.indexOf(filename) > -1)\n            return;\n        self.files.push(filename);\n\n        // Shortcut bundled definitions\n        if (filename in common) {\n            if (sync)\n                process(filename, common[filename]);\n            else {\n                ++queued;\n                setTimeout(function() {\n                    --queued;\n                    process(filename, common[filename]);\n                });\n            }\n            return;\n        }\n\n        // Otherwise fetch from disk or network\n        if (sync) {\n            var source;\n            try {\n                source = util.fs.readFileSync(filename).toString(\"utf8\");\n            } catch (err) {\n                if (!weak)\n                    finish(err);\n                return;\n            }\n            process(filename, source);\n        } else {\n            ++queued;\n            self.fetch(filename, function(err, source) {\n                --queued;\n                /* istanbul ignore if */\n                if (!callback)\n                    return; // terminated meanwhile\n                if (err) {\n                    /* istanbul ignore else */\n                    if (!weak)\n                        finish(err);\n                    else if (!queued) // can't be covered reliably\n                        finish(null, self);\n                    return;\n                }\n                process(filename, source);\n            });\n        }\n    }\n    var queued = 0;\n\n    // Assembling the root namespace doesn't require working type\n    // references anymore, so we can load everything in parallel\n    if (util.isString(filename))\n        filename = [ filename ];\n    for (var i = 0, resolved; i < filename.length; ++i)\n        if (resolved = self.resolvePath(\"\", filename[i]))\n            fetch(resolved);\n\n    if (sync)\n        return self;\n    if (!queued)\n        finish(null, self);\n    return undefined;\n};\n// function load(filename:string, options:IParseOptions, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\n * @function Root#load\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @variation 2\n */\n// function load(filename:string, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and returns a promise.\n * @function Root#load\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {Promise<Root>} Promise\n * @variation 3\n */\n// function load(filename:string, [options:IParseOptions]):Promise<Root>\n\n/**\n * Synchronously loads one or multiple .proto or preprocessed .json files into this root namespace (node only).\n * @function Root#loadSync\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {Root} Root namespace\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\n */\nRoot.prototype.loadSync = function loadSync(filename, options) {\n    if (!util.isNode)\n        throw Error(\"not supported\");\n    return this.load(filename, options, SYNC);\n};\n\n/**\n * @override\n */\nRoot.prototype.resolveAll = function resolveAll() {\n    if (this.deferred.length)\n        throw Error(\"unresolvable extensions: \" + this.deferred.map(function(field) {\n            return \"'extend \" + field.extend + \"' in \" + field.parent.fullName;\n        }).join(\", \"));\n    return Namespace.prototype.resolveAll.call(this);\n};\n\n// only uppercased (and thus conflict-free) children are exposed, see below\nvar exposeRe = /^[A-Z]/;\n\n/**\n * Handles a deferred declaring extension field by creating a sister field to represent it within its extended type.\n * @param {Root} root Root instance\n * @param {Field} field Declaring extension field witin the declaring type\n * @returns {boolean} `true` if successfully added to the extended type, `false` otherwise\n * @inner\n * @ignore\n */\nfunction tryHandleExtension(root, field) {\n    var extendedType = field.parent.lookup(field.extend);\n    if (extendedType) {\n        var sisterField = new Field(field.fullName, field.id, field.type, field.rule, undefined, field.options);\n        sisterField.declaringField = field;\n        field.extensionField = sisterField;\n        extendedType.add(sisterField);\n        return true;\n    }\n    return false;\n}\n\n/**\n * Called when any object is added to this root or its sub-namespaces.\n * @param {ReflectionObject} object Object added\n * @returns {undefined}\n * @private\n */\nRoot.prototype._handleAdd = function _handleAdd(object) {\n    if (object instanceof Field) {\n\n        if (/* an extension field (implies not part of a oneof) */ object.extend !== undefined && /* not already handled */ !object.extensionField)\n            if (!tryHandleExtension(this, object))\n                this.deferred.push(object);\n\n    } else if (object instanceof Enum) {\n\n        if (exposeRe.test(object.name))\n            object.parent[object.name] = object.values; // expose enum values as property of its parent\n\n    } else if (!(object instanceof OneOf)) /* everything else is a namespace */ {\n\n        if (object instanceof Type) // Try to handle any deferred extensions\n            for (var i = 0; i < this.deferred.length;)\n                if (tryHandleExtension(this, this.deferred[i]))\n                    this.deferred.splice(i, 1);\n                else\n                    ++i;\n        for (var j = 0; j < /* initializes */ object.nestedArray.length; ++j) // recurse into the namespace\n            this._handleAdd(object._nestedArray[j]);\n        if (exposeRe.test(object.name))\n            object.parent[object.name] = object; // expose namespace as property of its parent\n    }\n\n    // The above also adds uppercased (and thus conflict-free) nested types, services and enums as\n    // properties of namespaces just like static code does. This allows using a .d.ts generated for\n    // a static module with reflection-based solutions where the condition is met.\n};\n\n/**\n * Called when any object is removed from this root or its sub-namespaces.\n * @param {ReflectionObject} object Object removed\n * @returns {undefined}\n * @private\n */\nRoot.prototype._handleRemove = function _handleRemove(object) {\n    if (object instanceof Field) {\n\n        if (/* an extension field */ object.extend !== undefined) {\n            if (/* already handled */ object.extensionField) { // remove its sister field\n                object.extensionField.parent.remove(object.extensionField);\n                object.extensionField = null;\n            } else { // cancel the extension\n                var index = this.deferred.indexOf(object);\n                /* istanbul ignore else */\n                if (index > -1)\n                    this.deferred.splice(index, 1);\n            }\n        }\n\n    } else if (object instanceof Enum) {\n\n        if (exposeRe.test(object.name))\n            delete object.parent[object.name]; // unexpose enum values\n\n    } else if (object instanceof Namespace) {\n\n        for (var i = 0; i < /* initializes */ object.nestedArray.length; ++i) // recurse into the namespace\n            this._handleRemove(object._nestedArray[i]);\n\n        if (exposeRe.test(object.name))\n            delete object.parent[object.name]; // unexpose namespaces\n\n    }\n};\n\n// Sets up cyclic dependencies (called in index-light)\nRoot._configure = function(Type_, parse_, common_) {\n    Type   = Type_;\n    parse  = parse_;\n    common = common_;\n};\n", "\"use strict\";\nmodule.exports = {};\n\n/**\n * Named roots.\n * This is where pbjs stores generated structures (the option `-r, --root` specifies a name).\n * Can also be used manually to make roots available accross modules.\n * @name roots\n * @type {Object.<string,Root>}\n * @example\n * // pbjs -r myroot -o compiled.js ...\n *\n * // in another module:\n * require(\"./compiled.js\");\n *\n * // in any subsequent module:\n * var root = protobuf.roots[\"myroot\"];\n */\n", "\"use strict\";\n\n/**\n * Streaming RPC helpers.\n * @namespace\n */\nvar rpc = exports;\n\n/**\n * RPC implementation passed to {@link Service#create} performing a service request on network level, i.e. by utilizing http requests or websockets.\n * @typedef RPCImpl\n * @type {function}\n * @param {Method|rpc.ServiceMethod<Message<{}>,Message<{}>>} method Reflected or static method being called\n * @param {Uint8Array} requestData Request data\n * @param {RPCImplCallback} callback Callback function\n * @returns {undefined}\n * @example\n * function rpcImpl(method, requestData, callback) {\n *     if (protobuf.util.lcFirst(method.name) !== \"myMethod\") // compatible with static code\n *         throw Error(\"no such method\");\n *     asynchronouslyObtainAResponse(requestData, function(err, responseData) {\n *         callback(err, responseData);\n *     });\n * }\n */\n\n/**\n * Node-style callback as used by {@link RPCImpl}.\n * @typedef RPCImplCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Uint8Array|null} [response] Response data or `null` to signal end of stream, if there hasn't been an error\n * @returns {undefined}\n */\n\nrpc.Service = require(32);\n", "\"use strict\";\nmodule.exports = Service;\n\nvar util = require(39);\n\n// Extends EventEmitter\n(Service.prototype = Object.create(util.EventEmitter.prototype)).constructor = Service;\n\n/**\n * A service method callback as used by {@link rpc.ServiceMethod|ServiceMethod}.\n *\n * Differs from {@link RPCImplCallback} in that it is an actual callback of a service method which may not return `response = null`.\n * @typedef rpc.ServiceMethodCallback\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {Error|null} error Error, if any\n * @param {TRes} [response] Response message\n * @returns {undefined}\n */\n\n/**\n * A service method part of a {@link rpc.Service} as created by {@link Service.create}.\n * @typedef rpc.ServiceMethod\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} [callback] Node-style callback called with the error, if any, and the response message\n * @returns {Promise<Message<TRes>>} Promise if `callback` has been omitted, otherwise `undefined`\n */\n\n/**\n * Constructs a new RPC service instance.\n * @classdesc An RPC service as returned by {@link Service#create}.\n * @exports rpc.Service\n * @extends util.EventEmitter\n * @constructor\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n */\nfunction Service(rpcImpl, requestDelimited, responseDelimited) {\n\n    if (typeof rpcImpl !== \"function\")\n        throw TypeError(\"rpcImpl must be a function\");\n\n    util.EventEmitter.call(this);\n\n    /**\n     * RPC implementation. Becomes `null` once the service is ended.\n     * @type {RPCImpl|null}\n     */\n    this.rpcImpl = rpcImpl;\n\n    /**\n     * Whether requests are length-delimited.\n     * @type {boolean}\n     */\n    this.requestDelimited = Boolean(requestDelimited);\n\n    /**\n     * Whether responses are length-delimited.\n     * @type {boolean}\n     */\n    this.responseDelimited = Boolean(responseDelimited);\n}\n\n/**\n * Calls a service method through {@link rpc.Service#rpcImpl|rpcImpl}.\n * @param {Method|rpc.ServiceMethod<TReq,TRes>} method Reflected or static method\n * @param {Constructor<TReq>} requestCtor Request constructor\n * @param {Constructor<TRes>} responseCtor Response constructor\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} callback Service callback\n * @returns {undefined}\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n */\nService.prototype.rpcCall = function rpcCall(method, requestCtor, responseCtor, request, callback) {\n\n    if (!request)\n        throw TypeError(\"request must be specified\");\n\n    var self = this;\n    if (!callback)\n        return util.asPromise(rpcCall, self, method, requestCtor, responseCtor, request);\n\n    if (!self.rpcImpl) {\n        setTimeout(function() { callback(Error(\"already ended\")); }, 0);\n        return undefined;\n    }\n\n    try {\n        return self.rpcImpl(\n            method,\n            requestCtor[self.requestDelimited ? \"encodeDelimited\" : \"encode\"](request).finish(),\n            function rpcCallback(err, response) {\n\n                if (err) {\n                    self.emit(\"error\", err, method);\n                    return callback(err);\n                }\n\n                if (response === null) {\n                    self.end(/* endedByRPC */ true);\n                    return undefined;\n                }\n\n                if (!(response instanceof responseCtor)) {\n                    try {\n                        response = responseCtor[self.responseDelimited ? \"decodeDelimited\" : \"decode\"](response);\n                    } catch (err) {\n                        self.emit(\"error\", err, method);\n                        return callback(err);\n                    }\n                }\n\n                self.emit(\"data\", response, method);\n                return callback(null, response);\n            }\n        );\n    } catch (err) {\n        self.emit(\"error\", err, method);\n        setTimeout(function() { callback(err); }, 0);\n        return undefined;\n    }\n};\n\n/**\n * Ends this service and emits the `end` event.\n * @param {boolean} [endedByRPC=false] Whether the service has been ended by the RPC implementation.\n * @returns {rpc.Service} `this`\n */\nService.prototype.end = function end(endedByRPC) {\n    if (this.rpcImpl) {\n        if (!endedByRPC) // signal end to rpcImpl\n            this.rpcImpl(null, null, null);\n        this.rpcImpl = null;\n        this.emit(\"end\").off();\n    }\n    return this;\n};\n", "\"use strict\";\nmodule.exports = Service;\n\n// extends Namespace\nvar Namespace = require(23);\n((Service.prototype = Object.create(Namespace.prototype)).constructor = Service).className = \"Service\";\n\nvar Method = require(22),\n    util   = require(37),\n    rpc    = require(31);\n\n/**\n * Constructs a new service instance.\n * @classdesc Reflected service.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Service name\n * @param {Object.<string,*>} [options] Service options\n * @throws {TypeError} If arguments are invalid\n */\nfunction Service(name, options) {\n    Namespace.call(this, name, options);\n\n    /**\n     * Service methods.\n     * @type {Object.<string,Method>}\n     */\n    this.methods = {}; // toJSON, marker\n\n    /**\n     * Cached methods as an array.\n     * @type {Method[]|null}\n     * @private\n     */\n    this._methodsArray = null;\n}\n\n/**\n * Service descriptor.\n * @interface IService\n * @extends INamespace\n * @property {Object.<string,IMethod>} methods Method descriptors\n */\n\n/**\n * Constructs a service from a service descriptor.\n * @param {string} name Service name\n * @param {IService} json Service descriptor\n * @returns {Service} Created service\n * @throws {TypeError} If arguments are invalid\n */\nService.fromJSON = function fromJSON(name, json) {\n    var service = new Service(name, json.options);\n    /* istanbul ignore else */\n    if (json.methods)\n        for (var names = Object.keys(json.methods), i = 0; i < names.length; ++i)\n            service.add(Method.fromJSON(names[i], json.methods[names[i]]));\n    if (json.nested)\n        service.addJSON(json.nested);\n    service.comment = json.comment;\n    return service;\n};\n\n/**\n * Converts this service to a service descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IService} Service descriptor\n */\nService.prototype.toJSON = function toJSON(toJSONOptions) {\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\" , inherited && inherited.options || undefined,\n        \"methods\" , Namespace.arrayToJSON(this.methodsArray, toJSONOptions) || /* istanbul ignore next */ {},\n        \"nested\"  , inherited && inherited.nested || undefined,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Methods of this service as an array for iteration.\n * @name Service#methodsArray\n * @type {Method[]}\n * @readonly\n */\nObject.defineProperty(Service.prototype, \"methodsArray\", {\n    get: function() {\n        return this._methodsArray || (this._methodsArray = util.toArray(this.methods));\n    }\n});\n\nfunction clearCache(service) {\n    service._methodsArray = null;\n    return service;\n}\n\n/**\n * @override\n */\nService.prototype.get = function get(name) {\n    return this.methods[name]\n        || Namespace.prototype.get.call(this, name);\n};\n\n/**\n * @override\n */\nService.prototype.resolveAll = function resolveAll() {\n    var methods = this.methodsArray;\n    for (var i = 0; i < methods.length; ++i)\n        methods[i].resolve();\n    return Namespace.prototype.resolve.call(this);\n};\n\n/**\n * @override\n */\nService.prototype.add = function add(object) {\n\n    /* istanbul ignore if */\n    if (this.get(object.name))\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n\n    if (object instanceof Method) {\n        this.methods[object.name] = object;\n        object.parent = this;\n        return clearCache(this);\n    }\n    return Namespace.prototype.add.call(this, object);\n};\n\n/**\n * @override\n */\nService.prototype.remove = function remove(object) {\n    if (object instanceof Method) {\n\n        /* istanbul ignore if */\n        if (this.methods[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.methods[object.name];\n        object.parent = null;\n        return clearCache(this);\n    }\n    return Namespace.prototype.remove.call(this, object);\n};\n\n/**\n * Creates a runtime service using the specified rpc implementation.\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n * @returns {rpc.Service} RPC service. Useful where requests and/or responses are streamed.\n */\nService.prototype.create = function create(rpcImpl, requestDelimited, responseDelimited) {\n    var rpcService = new rpc.Service(rpcImpl, requestDelimited, responseDelimited);\n    for (var i = 0, method; i < /* initializes */ this.methodsArray.length; ++i) {\n        var methodName = util.lcFirst((method = this._methodsArray[i]).resolve().name).replace(/[^$\\w_]/g, \"\");\n        rpcService[methodName] = util.codegen([\"r\",\"c\"], util.isReserved(methodName) ? methodName + \"_\" : methodName)(\"return this.rpcCall(m,q,s,r,c)\")({\n            m: method,\n            q: method.resolvedRequestType.ctor,\n            s: method.resolvedResponseType.ctor\n        });\n    }\n    return rpcService;\n};\n", "\"use strict\";\nmodule.exports = tokenize;\n\nvar delimRe        = /[\\s{}=;:[\\],'\"()<>]/g,\n    stringDoubleRe = /(?:\"([^\"\\\\]*(?:\\\\.[^\"\\\\]*)*)\")/g,\n    stringSingleRe = /(?:'([^'\\\\]*(?:\\\\.[^'\\\\]*)*)')/g;\n\nvar setCommentRe = /^ *[*/]+ */,\n    setCommentAltRe = /^\\s*\\*?\\/*/,\n    setCommentSplitRe = /\\n/g,\n    whitespaceRe = /\\s/,\n    unescapeRe = /\\\\(.?)/g;\n\nvar unescapeMap = {\n    \"0\": \"\\0\",\n    \"r\": \"\\r\",\n    \"n\": \"\\n\",\n    \"t\": \"\\t\"\n};\n\n/**\n * Unescapes a string.\n * @param {string} str String to unescape\n * @returns {string} Unescaped string\n * @property {Object.<string,string>} map Special characters map\n * @memberof tokenize\n */\nfunction unescape(str) {\n    return str.replace(unescapeRe, function($0, $1) {\n        switch ($1) {\n            case \"\\\\\":\n            case \"\":\n                return $1;\n            default:\n                return unescapeMap[$1] || \"\";\n        }\n    });\n}\n\ntokenize.unescape = unescape;\n\n/**\n * Gets the next token and advances.\n * @typedef TokenizerHandleNext\n * @type {function}\n * @returns {string|null} Next token or `null` on eof\n */\n\n/**\n * Peeks for the next token.\n * @typedef TokenizerHandlePeek\n * @type {function}\n * @returns {string|null} Next token or `null` on eof\n */\n\n/**\n * Pushes a token back to the stack.\n * @typedef TokenizerHandlePush\n * @type {function}\n * @param {string} token Token\n * @returns {undefined}\n */\n\n/**\n * Skips the next token.\n * @typedef TokenizerHandleSkip\n * @type {function}\n * @param {string} expected Expected token\n * @param {boolean} [optional=false] If optional\n * @returns {boolean} Whether the token matched\n * @throws {Error} If the token didn't match and is not optional\n */\n\n/**\n * Gets the comment on the previous line or, alternatively, the line comment on the specified line.\n * @typedef TokenizerHandleCmnt\n * @type {function}\n * @param {number} [line] Line number\n * @returns {string|null} Comment text or `null` if none\n */\n\n/**\n * Handle object returned from {@link tokenize}.\n * @interface ITokenizerHandle\n * @property {TokenizerHandleNext} next Gets the next token and advances (`null` on eof)\n * @property {TokenizerHandlePeek} peek Peeks for the next token (`null` on eof)\n * @property {TokenizerHandlePush} push Pushes a token back to the stack\n * @property {TokenizerHandleSkip} skip Skips a token, returns its presence and advances or, if non-optional and not present, throws\n * @property {TokenizerHandleCmnt} cmnt Gets the comment on the previous line or the line comment on the specified line, if any\n * @property {number} line Current line number\n */\n\n/**\n * Tokenizes the given .proto source and returns an object with useful utility functions.\n * @param {string} source Source contents\n * @param {boolean} alternateCommentMode Whether we should activate alternate comment parsing mode.\n * @returns {ITokenizerHandle} Tokenizer handle\n */\nfunction tokenize(source, alternateCommentMode) {\n    /* eslint-disable callback-return */\n    source = source.toString();\n\n    var offset = 0,\n        length = source.length,\n        line = 1,\n        commentType = null,\n        commentText = null,\n        commentLine = 0,\n        commentLineEmpty = false,\n        commentIsLeading = false;\n\n    var stack = [];\n\n    var stringDelim = null;\n\n    /* istanbul ignore next */\n    /**\n     * Creates an error for illegal syntax.\n     * @param {string} subject Subject\n     * @returns {Error} Error created\n     * @inner\n     */\n    function illegal(subject) {\n        return Error(\"illegal \" + subject + \" (line \" + line + \")\");\n    }\n\n    /**\n     * Reads a string till its end.\n     * @returns {string} String read\n     * @inner\n     */\n    function readString() {\n        var re = stringDelim === \"'\" ? stringSingleRe : stringDoubleRe;\n        re.lastIndex = offset - 1;\n        var match = re.exec(source);\n        if (!match)\n            throw illegal(\"string\");\n        offset = re.lastIndex;\n        push(stringDelim);\n        stringDelim = null;\n        return unescape(match[1]);\n    }\n\n    /**\n     * Gets the character at `pos` within the source.\n     * @param {number} pos Position\n     * @returns {string} Character\n     * @inner\n     */\n    function charAt(pos) {\n        return source.charAt(pos);\n    }\n\n    /**\n     * Sets the current comment text.\n     * @param {number} start Start offset\n     * @param {number} end End offset\n     * @param {boolean} isLeading set if a leading comment\n     * @returns {undefined}\n     * @inner\n     */\n    function setComment(start, end, isLeading) {\n        commentType = source.charAt(start++);\n        commentLine = line;\n        commentLineEmpty = false;\n        commentIsLeading = isLeading;\n        var lookback;\n        if (alternateCommentMode) {\n            lookback = 2;  // alternate comment parsing: \"//\" or \"/*\"\n        } else {\n            lookback = 3;  // \"///\" or \"/**\"\n        }\n        var commentOffset = start - lookback,\n            c;\n        do {\n            if (--commentOffset < 0 ||\n                    (c = source.charAt(commentOffset)) === \"\\n\") {\n                commentLineEmpty = true;\n                break;\n            }\n        } while (c === \" \" || c === \"\\t\");\n        var lines = source\n            .substring(start, end)\n            .split(setCommentSplitRe);\n        for (var i = 0; i < lines.length; ++i)\n            lines[i] = lines[i]\n                .replace(alternateCommentMode ? setCommentAltRe : setCommentRe, \"\")\n                .trim();\n        commentText = lines\n            .join(\"\\n\")\n            .trim();\n    }\n\n    function isDoubleSlashCommentLine(startOffset) {\n        var endOffset = findEndOfLine(startOffset);\n\n        // see if remaining line matches comment pattern\n        var lineText = source.substring(startOffset, endOffset);\n        // look for 1 or 2 slashes since startOffset would already point past\n        // the first slash that started the comment.\n        var isComment = /^\\s*\\/{1,2}/.test(lineText);\n        return isComment;\n    }\n\n    function findEndOfLine(cursor) {\n        // find end of cursor's line\n        var endOffset = cursor;\n        while (endOffset < length && charAt(endOffset) !== \"\\n\") {\n            endOffset++;\n        }\n        return endOffset;\n    }\n\n    /**\n     * Obtains the next token.\n     * @returns {string|null} Next token or `null` on eof\n     * @inner\n     */\n    function next() {\n        if (stack.length > 0)\n            return stack.shift();\n        if (stringDelim)\n            return readString();\n        var repeat,\n            prev,\n            curr,\n            start,\n            isDoc,\n            isLeadingComment = offset === 0;\n        do {\n            if (offset === length)\n                return null;\n            repeat = false;\n            while (whitespaceRe.test(curr = charAt(offset))) {\n                if (curr === \"\\n\") {\n                    isLeadingComment = true;\n                    ++line;\n                }\n                if (++offset === length)\n                    return null;\n            }\n\n            if (charAt(offset) === \"/\") {\n                if (++offset === length) {\n                    throw illegal(\"comment\");\n                }\n                if (charAt(offset) === \"/\") { // Line\n                    if (!alternateCommentMode) {\n                        // check for triple-slash comment\n                        isDoc = charAt(start = offset + 1) === \"/\";\n\n                        while (charAt(++offset) !== \"\\n\") {\n                            if (offset === length) {\n                                return null;\n                            }\n                        }\n                        ++offset;\n                        if (isDoc) {\n                            setComment(start, offset - 1, isLeadingComment);\n                        }\n                        ++line;\n                        repeat = true;\n                    } else {\n                        // check for double-slash comments, consolidating consecutive lines\n                        start = offset;\n                        isDoc = false;\n                        if (isDoubleSlashCommentLine(offset)) {\n                            isDoc = true;\n                            do {\n                                offset = findEndOfLine(offset);\n                                if (offset === length) {\n                                    break;\n                                }\n                                offset++;\n                            } while (isDoubleSlashCommentLine(offset));\n                        } else {\n                            offset = Math.min(length, findEndOfLine(offset) + 1);\n                        }\n                        if (isDoc) {\n                            setComment(start, offset, isLeadingComment);\n                        }\n                        line++;\n                        repeat = true;\n                    }\n                } else if ((curr = charAt(offset)) === \"*\") { /* Block */\n                    // check for /** (regular comment mode) or /* (alternate comment mode)\n                    start = offset + 1;\n                    isDoc = alternateCommentMode || charAt(start) === \"*\";\n                    do {\n                        if (curr === \"\\n\") {\n                            ++line;\n                        }\n                        if (++offset === length) {\n                            throw illegal(\"comment\");\n                        }\n                        prev = curr;\n                        curr = charAt(offset);\n                    } while (prev !== \"*\" || curr !== \"/\");\n                    ++offset;\n                    if (isDoc) {\n                        setComment(start, offset - 2, isLeadingComment);\n                    }\n                    repeat = true;\n                } else {\n                    return \"/\";\n                }\n            }\n        } while (repeat);\n\n        // offset !== length if we got here\n\n        var end = offset;\n        delimRe.lastIndex = 0;\n        var delim = delimRe.test(charAt(end++));\n        if (!delim)\n            while (end < length && !delimRe.test(charAt(end)))\n                ++end;\n        var token = source.substring(offset, offset = end);\n        if (token === \"\\\"\" || token === \"'\")\n            stringDelim = token;\n        return token;\n    }\n\n    /**\n     * Pushes a token back to the stack.\n     * @param {string} token Token\n     * @returns {undefined}\n     * @inner\n     */\n    function push(token) {\n        stack.push(token);\n    }\n\n    /**\n     * Peeks for the next token.\n     * @returns {string|null} Token or `null` on eof\n     * @inner\n     */\n    function peek() {\n        if (!stack.length) {\n            var token = next();\n            if (token === null)\n                return null;\n            push(token);\n        }\n        return stack[0];\n    }\n\n    /**\n     * Skips a token.\n     * @param {string} expected Expected token\n     * @param {boolean} [optional=false] Whether the token is optional\n     * @returns {boolean} `true` when skipped, `false` if not\n     * @throws {Error} When a required token is not present\n     * @inner\n     */\n    function skip(expected, optional) {\n        var actual = peek(),\n            equals = actual === expected;\n        if (equals) {\n            next();\n            return true;\n        }\n        if (!optional)\n            throw illegal(\"token '\" + actual + \"', '\" + expected + \"' expected\");\n        return false;\n    }\n\n    /**\n     * Gets a comment.\n     * @param {number} [trailingLine] Line number if looking for a trailing comment\n     * @returns {string|null} Comment text\n     * @inner\n     */\n    function cmnt(trailingLine) {\n        var ret = null;\n        if (trailingLine === undefined) {\n            if (commentLine === line - 1 && (alternateCommentMode || commentType === \"*\" || commentLineEmpty)) {\n                ret = commentIsLeading ? commentText : null;\n            }\n        } else {\n            /* istanbul ignore else */\n            if (commentLine < trailingLine) {\n                peek();\n            }\n            if (commentLine === trailingLine && !commentLineEmpty && (alternateCommentMode || commentType === \"/\")) {\n                ret = commentIsLeading ? null : commentText;\n            }\n        }\n        return ret;\n    }\n\n    return Object.defineProperty({\n        next: next,\n        peek: peek,\n        push: push,\n        skip: skip,\n        cmnt: cmnt\n    }, \"line\", {\n        get: function() { return line; }\n    });\n    /* eslint-enable callback-return */\n}\n", "\"use strict\";\nmodule.exports = Type;\n\n// extends Namespace\nvar Namespace = require(23);\n((Type.prototype = Object.create(Namespace.prototype)).constructor = Type).className = \"Type\";\n\nvar Enum      = require(15),\n    OneOf     = require(25),\n    Field     = require(16),\n    MapField  = require(20),\n    Service   = require(33),\n    Message   = require(21),\n    Reader    = require(27),\n    Writer    = require(42),\n    util      = require(37),\n    encoder   = require(14),\n    decoder   = require(13),\n    verifier  = require(40),\n    converter = require(12),\n    wrappers  = require(41);\n\n/**\n * Constructs a new reflected message type instance.\n * @classdesc Reflected message type.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Message name\n * @param {Object.<string,*>} [options] Declared options\n */\nfunction Type(name, options) {\n    Namespace.call(this, name, options);\n\n    /**\n     * Message fields.\n     * @type {Object.<string,Field>}\n     */\n    this.fields = {};  // toJSON, marker\n\n    /**\n     * Oneofs declared within this namespace, if any.\n     * @type {Object.<string,OneOf>}\n     */\n    this.oneofs = undefined; // toJSON\n\n    /**\n     * Extension ranges, if any.\n     * @type {number[][]}\n     */\n    this.extensions = undefined; // toJSON\n\n    /**\n     * Reserved ranges, if any.\n     * @type {Array.<number[]|string>}\n     */\n    this.reserved = undefined; // toJSON\n\n    /*?\n     * Whether this type is a legacy group.\n     * @type {boolean|undefined}\n     */\n    this.group = undefined; // toJSON\n\n    /**\n     * Cached fields by id.\n     * @type {Object.<number,Field>|null}\n     * @private\n     */\n    this._fieldsById = null;\n\n    /**\n     * Cached fields as an array.\n     * @type {Field[]|null}\n     * @private\n     */\n    this._fieldsArray = null;\n\n    /**\n     * Cached oneofs as an array.\n     * @type {OneOf[]|null}\n     * @private\n     */\n    this._oneofsArray = null;\n\n    /**\n     * Cached constructor.\n     * @type {Constructor<{}>}\n     * @private\n     */\n    this._ctor = null;\n}\n\nObject.defineProperties(Type.prototype, {\n\n    /**\n     * Message fields by id.\n     * @name Type#fieldsById\n     * @type {Object.<number,Field>}\n     * @readonly\n     */\n    fieldsById: {\n        get: function() {\n\n            /* istanbul ignore if */\n            if (this._fieldsById)\n                return this._fieldsById;\n\n            this._fieldsById = {};\n            for (var names = Object.keys(this.fields), i = 0; i < names.length; ++i) {\n                var field = this.fields[names[i]],\n                    id = field.id;\n\n                /* istanbul ignore if */\n                if (this._fieldsById[id])\n                    throw Error(\"duplicate id \" + id + \" in \" + this);\n\n                this._fieldsById[id] = field;\n            }\n            return this._fieldsById;\n        }\n    },\n\n    /**\n     * Fields of this message as an array for iteration.\n     * @name Type#fieldsArray\n     * @type {Field[]}\n     * @readonly\n     */\n    fieldsArray: {\n        get: function() {\n            return this._fieldsArray || (this._fieldsArray = util.toArray(this.fields));\n        }\n    },\n\n    /**\n     * Oneofs of this message as an array for iteration.\n     * @name Type#oneofsArray\n     * @type {OneOf[]}\n     * @readonly\n     */\n    oneofsArray: {\n        get: function() {\n            return this._oneofsArray || (this._oneofsArray = util.toArray(this.oneofs));\n        }\n    },\n\n    /**\n     * The registered constructor, if any registered, otherwise a generic constructor.\n     * Assigning a function replaces the internal constructor. If the function does not extend {@link Message} yet, its prototype will be setup accordingly and static methods will be populated. If it already extends {@link Message}, it will just replace the internal constructor.\n     * @name Type#ctor\n     * @type {Constructor<{}>}\n     */\n    ctor: {\n        get: function() {\n            return this._ctor || (this.ctor = Type.generateConstructor(this)());\n        },\n        set: function(ctor) {\n\n            // Ensure proper prototype\n            var prototype = ctor.prototype;\n            if (!(prototype instanceof Message)) {\n                (ctor.prototype = new Message()).constructor = ctor;\n                util.merge(ctor.prototype, prototype);\n            }\n\n            // Classes and messages reference their reflected type\n            ctor.$type = ctor.prototype.$type = this;\n\n            // Mix in static methods\n            util.merge(ctor, Message, true);\n\n            this._ctor = ctor;\n\n            // Messages have non-enumerable default values on their prototype\n            var i = 0;\n            for (; i < /* initializes */ this.fieldsArray.length; ++i)\n                this._fieldsArray[i].resolve(); // ensures a proper value\n\n            // Messages have non-enumerable getters and setters for each virtual oneof field\n            var ctorProperties = {};\n            for (i = 0; i < /* initializes */ this.oneofsArray.length; ++i)\n                ctorProperties[this._oneofsArray[i].resolve().name] = {\n                    get: util.oneOfGetter(this._oneofsArray[i].oneof),\n                    set: util.oneOfSetter(this._oneofsArray[i].oneof)\n                };\n            if (i)\n                Object.defineProperties(ctor.prototype, ctorProperties);\n        }\n    }\n});\n\n/**\n * Generates a constructor function for the specified type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nType.generateConstructor = function generateConstructor(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n    var gen = util.codegen([\"p\"], mtype.name);\n    // explicitly initialize mutable object/array fields so that these aren't just inherited from the prototype\n    for (var i = 0, field; i < mtype.fieldsArray.length; ++i)\n        if ((field = mtype._fieldsArray[i]).map) gen\n            (\"this%s={}\", util.safeProp(field.name));\n        else if (field.repeated) gen\n            (\"this%s=[]\", util.safeProp(field.name));\n    return gen\n    (\"if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)\") // omit undefined or null\n        (\"this[ks[i]]=p[ks[i]]\");\n    /* eslint-enable no-unexpected-multiline */\n};\n\nfunction clearCache(type) {\n    type._fieldsById = type._fieldsArray = type._oneofsArray = null;\n    delete type.encode;\n    delete type.decode;\n    delete type.verify;\n    return type;\n}\n\n/**\n * Message type descriptor.\n * @interface IType\n * @extends INamespace\n * @property {Object.<string,IOneOf>} [oneofs] Oneof descriptors\n * @property {Object.<string,IField>} fields Field descriptors\n * @property {number[][]} [extensions] Extension ranges\n * @property {number[][]} [reserved] Reserved ranges\n * @property {boolean} [group=false] Whether a legacy group or not\n */\n\n/**\n * Creates a message type from a message type descriptor.\n * @param {string} name Message name\n * @param {IType} json Message type descriptor\n * @returns {Type} Created message type\n */\nType.fromJSON = function fromJSON(name, json) {\n    var type = new Type(name, json.options);\n    type.extensions = json.extensions;\n    type.reserved = json.reserved;\n    var names = Object.keys(json.fields),\n        i = 0;\n    for (; i < names.length; ++i)\n        type.add(\n            ( typeof json.fields[names[i]].keyType !== \"undefined\"\n            ? MapField.fromJSON\n            : Field.fromJSON )(names[i], json.fields[names[i]])\n        );\n    if (json.oneofs)\n        for (names = Object.keys(json.oneofs), i = 0; i < names.length; ++i)\n            type.add(OneOf.fromJSON(names[i], json.oneofs[names[i]]));\n    if (json.nested)\n        for (names = Object.keys(json.nested), i = 0; i < names.length; ++i) {\n            var nested = json.nested[names[i]];\n            type.add( // most to least likely\n                ( nested.id !== undefined\n                ? Field.fromJSON\n                : nested.fields !== undefined\n                ? Type.fromJSON\n                : nested.values !== undefined\n                ? Enum.fromJSON\n                : nested.methods !== undefined\n                ? Service.fromJSON\n                : Namespace.fromJSON )(names[i], nested)\n            );\n        }\n    if (json.extensions && json.extensions.length)\n        type.extensions = json.extensions;\n    if (json.reserved && json.reserved.length)\n        type.reserved = json.reserved;\n    if (json.group)\n        type.group = true;\n    if (json.comment)\n        type.comment = json.comment;\n    return type;\n};\n\n/**\n * Converts this message type to a message type descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IType} Message type descriptor\n */\nType.prototype.toJSON = function toJSON(toJSONOptions) {\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\"    , inherited && inherited.options || undefined,\n        \"oneofs\"     , Namespace.arrayToJSON(this.oneofsArray, toJSONOptions),\n        \"fields\"     , Namespace.arrayToJSON(this.fieldsArray.filter(function(obj) { return !obj.declaringField; }), toJSONOptions) || {},\n        \"extensions\" , this.extensions && this.extensions.length ? this.extensions : undefined,\n        \"reserved\"   , this.reserved && this.reserved.length ? this.reserved : undefined,\n        \"group\"      , this.group || undefined,\n        \"nested\"     , inherited && inherited.nested || undefined,\n        \"comment\"    , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nType.prototype.resolveAll = function resolveAll() {\n    var fields = this.fieldsArray, i = 0;\n    while (i < fields.length)\n        fields[i++].resolve();\n    var oneofs = this.oneofsArray; i = 0;\n    while (i < oneofs.length)\n        oneofs[i++].resolve();\n    return Namespace.prototype.resolveAll.call(this);\n};\n\n/**\n * @override\n */\nType.prototype.get = function get(name) {\n    return this.fields[name]\n        || this.oneofs && this.oneofs[name]\n        || this.nested && this.nested[name]\n        || null;\n};\n\n/**\n * Adds a nested object to this type.\n * @param {ReflectionObject} object Nested object to add\n * @returns {Type} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a nested object with this name or, if a field, when there is already a field with this id\n */\nType.prototype.add = function add(object) {\n\n    if (this.get(object.name))\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n\n    if (object instanceof Field && object.extend === undefined) {\n        // NOTE: Extension fields aren't actual fields on the declaring type, but nested objects.\n        // The root object takes care of adding distinct sister-fields to the respective extended\n        // type instead.\n\n        // avoids calling the getter if not absolutely necessary because it's called quite frequently\n        if (this._fieldsById ? /* istanbul ignore next */ this._fieldsById[object.id] : this.fieldsById[object.id])\n            throw Error(\"duplicate id \" + object.id + \" in \" + this);\n        if (this.isReservedId(object.id))\n            throw Error(\"id \" + object.id + \" is reserved in \" + this);\n        if (this.isReservedName(object.name))\n            throw Error(\"name '\" + object.name + \"' is reserved in \" + this);\n\n        if (object.parent)\n            object.parent.remove(object);\n        this.fields[object.name] = object;\n        object.message = this;\n        object.onAdd(this);\n        return clearCache(this);\n    }\n    if (object instanceof OneOf) {\n        if (!this.oneofs)\n            this.oneofs = {};\n        this.oneofs[object.name] = object;\n        object.onAdd(this);\n        return clearCache(this);\n    }\n    return Namespace.prototype.add.call(this, object);\n};\n\n/**\n * Removes a nested object from this type.\n * @param {ReflectionObject} object Nested object to remove\n * @returns {Type} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `object` is not a member of this type\n */\nType.prototype.remove = function remove(object) {\n    if (object instanceof Field && object.extend === undefined) {\n        // See Type#add for the reason why extension fields are excluded here.\n\n        /* istanbul ignore if */\n        if (!this.fields || this.fields[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.fields[object.name];\n        object.parent = null;\n        object.onRemove(this);\n        return clearCache(this);\n    }\n    if (object instanceof OneOf) {\n\n        /* istanbul ignore if */\n        if (!this.oneofs || this.oneofs[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.oneofs[object.name];\n        object.parent = null;\n        object.onRemove(this);\n        return clearCache(this);\n    }\n    return Namespace.prototype.remove.call(this, object);\n};\n\n/**\n * Tests if the specified id is reserved.\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nType.prototype.isReservedId = function isReservedId(id) {\n    return Namespace.isReservedId(this.reserved, id);\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nType.prototype.isReservedName = function isReservedName(name) {\n    return Namespace.isReservedName(this.reserved, name);\n};\n\n/**\n * Creates a new message of this type using the specified properties.\n * @param {Object.<string,*>} [properties] Properties to set\n * @returns {Message<{}>} Message instance\n */\nType.prototype.create = function create(properties) {\n    return new this.ctor(properties);\n};\n\n/**\n * Sets up {@link Type#encode|encode}, {@link Type#decode|decode} and {@link Type#verify|verify}.\n * @returns {Type} `this`\n */\nType.prototype.setup = function setup() {\n    // Sets up everything at once so that the prototype chain does not have to be re-evaluated\n    // multiple times (V8, soft-deopt prototype-check).\n\n    var fullName = this.fullName,\n        types    = [];\n    for (var i = 0; i < /* initializes */ this.fieldsArray.length; ++i)\n        types.push(this._fieldsArray[i].resolve().resolvedType);\n\n    // Replace setup methods with type-specific generated functions\n    this.encode = encoder(this)({\n        Writer : Writer,\n        types  : types,\n        util   : util\n    });\n    this.decode = decoder(this)({\n        Reader : Reader,\n        types  : types,\n        util   : util\n    });\n    this.verify = verifier(this)({\n        types : types,\n        util  : util\n    });\n    this.fromObject = converter.fromObject(this)({\n        types : types,\n        util  : util\n    });\n    this.toObject = converter.toObject(this)({\n        types : types,\n        util  : util\n    });\n\n    // Inject custom wrappers for common types\n    var wrapper = wrappers[fullName];\n    if (wrapper) {\n        var originalThis = Object.create(this);\n        // if (wrapper.fromObject) {\n            originalThis.fromObject = this.fromObject;\n            this.fromObject = wrapper.fromObject.bind(originalThis);\n        // }\n        // if (wrapper.toObject) {\n            originalThis.toObject = this.toObject;\n            this.toObject = wrapper.toObject.bind(originalThis);\n        // }\n    }\n\n    return this;\n};\n\n/**\n * Encodes a message of this type. Does not implicitly {@link Type#verify|verify} messages.\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\n * @param {Writer} [writer] Writer to encode to\n * @returns {Writer} writer\n */\nType.prototype.encode = function encode_setup(message, writer) {\n    return this.setup().encode(message, writer); // overrides this method\n};\n\n/**\n * Encodes a message of this type preceeded by its byte length as a varint. Does not implicitly {@link Type#verify|verify} messages.\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\n * @param {Writer} [writer] Writer to encode to\n * @returns {Writer} writer\n */\nType.prototype.encodeDelimited = function encodeDelimited(message, writer) {\n    return this.encode(message, writer && writer.len ? writer.fork() : writer).ldelim();\n};\n\n/**\n * Decodes a message of this type.\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\n * @param {number} [length] Length of the message, if known beforehand\n * @returns {Message<{}>} Decoded message\n * @throws {Error} If the payload is not a reader or valid buffer\n * @throws {util.ProtocolError<{}>} If required fields are missing\n */\nType.prototype.decode = function decode_setup(reader, length) {\n    return this.setup().decode(reader, length); // overrides this method\n};\n\n/**\n * Decodes a message of this type preceeded by its byte length as a varint.\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\n * @returns {Message<{}>} Decoded message\n * @throws {Error} If the payload is not a reader or valid buffer\n * @throws {util.ProtocolError} If required fields are missing\n */\nType.prototype.decodeDelimited = function decodeDelimited(reader) {\n    if (!(reader instanceof Reader))\n        reader = Reader.create(reader);\n    return this.decode(reader, reader.uint32());\n};\n\n/**\n * Verifies that field values are valid and that required fields are present.\n * @param {Object.<string,*>} message Plain object to verify\n * @returns {null|string} `null` if valid, otherwise the reason why it is not\n */\nType.prototype.verify = function verify_setup(message) {\n    return this.setup().verify(message); // overrides this method\n};\n\n/**\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\n * @param {Object.<string,*>} object Plain object to convert\n * @returns {Message<{}>} Message instance\n */\nType.prototype.fromObject = function fromObject(object) {\n    return this.setup().fromObject(object);\n};\n\n/**\n * Conversion options as used by {@link Type#toObject} and {@link Message.toObject}.\n * @interface IConversionOptions\n * @property {Function} [longs] Long conversion type.\n * Valid values are `String` and `Number` (the global types).\n * Defaults to copy the present value, which is a possibly unsafe number without and a {@link Long} with a long library.\n * @property {Function} [enums] Enum value conversion type.\n * Only valid value is `String` (the global type).\n * Defaults to copy the present value, which is the numeric id.\n * @property {Function} [bytes] Bytes value conversion type.\n * Valid values are `Array` and (a base64 encoded) `String` (the global types).\n * Defaults to copy the present value, which usually is a Buffer under node and an Uint8Array in the browser.\n * @property {boolean} [defaults=false] Also sets default values on the resulting object\n * @property {boolean} [arrays=false] Sets empty arrays for missing repeated fields even if `defaults=false`\n * @property {boolean} [objects=false] Sets empty objects for missing map fields even if `defaults=false`\n * @property {boolean} [oneofs=false] Includes virtual oneof properties set to the present field's name, if any\n * @property {boolean} [json=false] Performs additional JSON compatibility conversions, i.e. NaN and Infinity to strings\n */\n\n/**\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\n * @param {Message<{}>} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n */\nType.prototype.toObject = function toObject(message, options) {\n    return this.setup().toObject(message, options);\n};\n\n/**\n * Decorator function as returned by {@link Type.d} (TypeScript).\n * @typedef TypeDecorator\n * @type {function}\n * @param {Constructor<T>} target Target constructor\n * @returns {undefined}\n * @template T extends Message<T>\n */\n\n/**\n * Type decorator (TypeScript).\n * @param {string} [typeName] Type name, defaults to the constructor's name\n * @returns {TypeDecorator<T>} Decorator function\n * @template T extends Message<T>\n */\nType.d = function decorateType(typeName) {\n    return function typeDecorator(target) {\n        util.decorateType(target, typeName);\n    };\n};\n", "\"use strict\";\n\n/**\n * Common type constants.\n * @namespace\n */\nvar types = exports;\n\nvar util = require(37);\n\nvar s = [\n    \"double\",   // 0\n    \"float\",    // 1\n    \"int32\",    // 2\n    \"uint32\",   // 3\n    \"sint32\",   // 4\n    \"fixed32\",  // 5\n    \"sfixed32\", // 6\n    \"int64\",    // 7\n    \"uint64\",   // 8\n    \"sint64\",   // 9\n    \"fixed64\",  // 10\n    \"sfixed64\", // 11\n    \"bool\",     // 12\n    \"string\",   // 13\n    \"bytes\"     // 14\n];\n\nfunction bake(values, offset) {\n    var i = 0, o = {};\n    offset |= 0;\n    while (i < values.length) o[s[i + offset]] = values[i++];\n    return o;\n}\n\n/**\n * Basic type wire types.\n * @type {Object.<string,number>}\n * @const\n * @property {number} double=1 Fixed64 wire type\n * @property {number} float=5 Fixed32 wire type\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n * @property {number} string=2 Ldelim wire type\n * @property {number} bytes=2 Ldelim wire type\n */\ntypes.basic = bake([\n    /* double   */ 1,\n    /* float    */ 5,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0,\n    /* string   */ 2,\n    /* bytes    */ 2\n]);\n\n/**\n * Basic type defaults.\n * @type {Object.<string,*>}\n * @const\n * @property {number} double=0 Double default\n * @property {number} float=0 Float default\n * @property {number} int32=0 Int32 default\n * @property {number} uint32=0 Uint32 default\n * @property {number} sint32=0 Sint32 default\n * @property {number} fixed32=0 Fixed32 default\n * @property {number} sfixed32=0 Sfixed32 default\n * @property {number} int64=0 Int64 default\n * @property {number} uint64=0 Uint64 default\n * @property {number} sint64=0 Sint32 default\n * @property {number} fixed64=0 Fixed64 default\n * @property {number} sfixed64=0 Sfixed64 default\n * @property {boolean} bool=false Bool default\n * @property {string} string=\"\" String default\n * @property {Array.<number>} bytes=Array(0) Bytes default\n * @property {null} message=null Message default\n */\ntypes.defaults = bake([\n    /* double   */ 0,\n    /* float    */ 0,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 0,\n    /* sfixed32 */ 0,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 0,\n    /* sfixed64 */ 0,\n    /* bool     */ false,\n    /* string   */ \"\",\n    /* bytes    */ util.emptyArray,\n    /* message  */ null\n]);\n\n/**\n * Basic long type wire types.\n * @type {Object.<string,number>}\n * @const\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n */\ntypes.long = bake([\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1\n], 7);\n\n/**\n * Allowed types for map keys with their associated wire type.\n * @type {Object.<string,number>}\n * @const\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n * @property {number} string=2 Ldelim wire type\n */\ntypes.mapKey = bake([\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0,\n    /* string   */ 2\n], 2);\n\n/**\n * Allowed types for packed repeated fields with their associated wire type.\n * @type {Object.<string,number>}\n * @const\n * @property {number} double=1 Fixed64 wire type\n * @property {number} float=5 Fixed32 wire type\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n */\ntypes.packed = bake([\n    /* double   */ 1,\n    /* float    */ 5,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0\n]);\n", "\"use strict\";\n\n/**\n * Various utility functions.\n * @namespace\n */\nvar util = module.exports = require(39);\n\nvar roots = require(30);\n\nvar Type, // cyclic\n    Enum;\n\nutil.codegen = require(3);\nutil.fetch   = require(5);\nutil.path    = require(8);\n\n/**\n * Node's fs module if available.\n * @type {Object.<string,*>}\n */\nutil.fs = util.inquire(\"fs\");\n\n/**\n * Converts an object's values to an array.\n * @param {Object.<string,*>} object Object to convert\n * @returns {Array.<*>} Converted array\n */\nutil.toArray = function toArray(object) {\n    if (object) {\n        var keys  = Object.keys(object),\n            array = new Array(keys.length),\n            index = 0;\n        while (index < keys.length)\n            array[index] = object[keys[index++]];\n        return array;\n    }\n    return [];\n};\n\n/**\n * Converts an array of keys immediately followed by their respective value to an object, omitting undefined values.\n * @param {Array.<*>} array Array to convert\n * @returns {Object.<string,*>} Converted object\n */\nutil.toObject = function toObject(array) {\n    var object = {},\n        index  = 0;\n    while (index < array.length) {\n        var key = array[index++],\n            val = array[index++];\n        if (val !== undefined)\n            object[key] = val;\n    }\n    return object;\n};\n\nvar safePropBackslashRe = /\\\\/g,\n    safePropQuoteRe     = /\"/g;\n\n/**\n * Tests whether the specified name is a reserved word in JS.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nutil.isReserved = function isReserved(name) {\n    return /^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(name);\n};\n\n/**\n * Returns a safe property accessor for the specified property name.\n * @param {string} prop Property name\n * @returns {string} Safe accessor\n */\nutil.safeProp = function safeProp(prop) {\n    if (!/^[$\\w_]+$/.test(prop) || util.isReserved(prop))\n        return \"[\\\"\" + prop.replace(safePropBackslashRe, \"\\\\\\\\\").replace(safePropQuoteRe, \"\\\\\\\"\") + \"\\\"]\";\n    return \".\" + prop;\n};\n\n/**\n * Converts the first character of a string to upper case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.ucFirst = function ucFirst(str) {\n    return str.charAt(0).toUpperCase() + str.substring(1);\n};\n\nvar camelCaseRe = /_([a-z])/g;\n\n/**\n * Converts a string to camel case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.camelCase = function camelCase(str) {\n    return str.substring(0, 1)\n         + str.substring(1)\n               .replace(camelCaseRe, function($0, $1) { return $1.toUpperCase(); });\n};\n\n/**\n * Compares reflected fields by id.\n * @param {Field} a First field\n * @param {Field} b Second field\n * @returns {number} Comparison value\n */\nutil.compareFieldsById = function compareFieldsById(a, b) {\n    return a.id - b.id;\n};\n\n/**\n * Decorator helper for types (TypeScript).\n * @param {Constructor<T>} ctor Constructor function\n * @param {string} [typeName] Type name, defaults to the constructor's name\n * @returns {Type} Reflected type\n * @template T extends Message<T>\n * @property {Root} root Decorators root\n */\nutil.decorateType = function decorateType(ctor, typeName) {\n\n    /* istanbul ignore if */\n    if (ctor.$type) {\n        if (typeName && ctor.$type.name !== typeName) {\n            util.decorateRoot.remove(ctor.$type);\n            ctor.$type.name = typeName;\n            util.decorateRoot.add(ctor.$type);\n        }\n        return ctor.$type;\n    }\n\n    /* istanbul ignore next */\n    if (!Type)\n        Type = require(35);\n\n    var type = new Type(typeName || ctor.name);\n    util.decorateRoot.add(type);\n    type.ctor = ctor; // sets up .encode, .decode etc.\n    Object.defineProperty(ctor, \"$type\", { value: type, enumerable: false });\n    Object.defineProperty(ctor.prototype, \"$type\", { value: type, enumerable: false });\n    return type;\n};\n\nvar decorateEnumIndex = 0;\n\n/**\n * Decorator helper for enums (TypeScript).\n * @param {Object} object Enum object\n * @returns {Enum} Reflected enum\n */\nutil.decorateEnum = function decorateEnum(object) {\n\n    /* istanbul ignore if */\n    if (object.$type)\n        return object.$type;\n\n    /* istanbul ignore next */\n    if (!Enum)\n        Enum = require(15);\n\n    var enm = new Enum(\"Enum\" + decorateEnumIndex++, object);\n    util.decorateRoot.add(enm);\n    Object.defineProperty(object, \"$type\", { value: enm, enumerable: false });\n    return enm;\n};\n\n\n/**\n * Sets the value of a property by property path. If a value already exists, it is turned to an array\n * @param {Object.<string,*>} dst Destination object\n * @param {string} path dot '.' delimited path of the property to set\n * @param {Object} value the value to set\n * @returns {Object.<string,*>} Destination object\n */\nutil.setProperty = function setProperty(dst, path, value) {\n    function setProp(dst, path, value) {\n        var part = path.shift();\n        if (part === \"__proto__\" || part === \"prototype\") {\n          return dst;\n        }\n        if (path.length > 0) {\n            dst[part] = setProp(dst[part] || {}, path, value);\n        } else {\n            var prevValue = dst[part];\n            if (prevValue)\n                value = [].concat(prevValue).concat(value);\n            dst[part] = value;\n        }\n        return dst;\n    }\n\n    if (typeof dst !== \"object\")\n        throw TypeError(\"dst must be an object\");\n    if (!path)\n        throw TypeError(\"path must be specified\");\n\n    path = path.split(\".\");\n    return setProp(dst, path, value);\n};\n\n/**\n * Decorator root (TypeScript).\n * @name util.decorateRoot\n * @type {Root}\n * @readonly\n */\nObject.defineProperty(util, \"decorateRoot\", {\n    get: function() {\n        return roots[\"decorated\"] || (roots[\"decorated\"] = new (require(29))());\n    }\n});\n", "\"use strict\";\nmodule.exports = LongBits;\n\nvar util = require(39);\n\n/**\n * Constructs new long bits.\n * @classdesc Helper class for working with the low and high bits of a 64 bit value.\n * @memberof util\n * @constructor\n * @param {number} lo Low 32 bits, unsigned\n * @param {number} hi High 32 bits, unsigned\n */\nfunction LongBits(lo, hi) {\n\n    // note that the casts below are theoretically unnecessary as of today, but older statically\n    // generated converter code might still call the ctor with signed 32bits. kept for compat.\n\n    /**\n     * Low bits.\n     * @type {number}\n     */\n    this.lo = lo >>> 0;\n\n    /**\n     * High bits.\n     * @type {number}\n     */\n    this.hi = hi >>> 0;\n}\n\n/**\n * Zero bits.\n * @memberof util.LongBits\n * @type {util.LongBits}\n */\nvar zero = LongBits.zero = new LongBits(0, 0);\n\nzero.toNumber = function() { return 0; };\nzero.zzEncode = zero.zzDecode = function() { return this; };\nzero.length = function() { return 1; };\n\n/**\n * Zero hash.\n * @memberof util.LongBits\n * @type {string}\n */\nvar zeroHash = LongBits.zeroHash = \"\\0\\0\\0\\0\\0\\0\\0\\0\";\n\n/**\n * Constructs new long bits from the specified number.\n * @param {number} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.fromNumber = function fromNumber(value) {\n    if (value === 0)\n        return zero;\n    var sign = value < 0;\n    if (sign)\n        value = -value;\n    var lo = value >>> 0,\n        hi = (value - lo) / 4294967296 >>> 0;\n    if (sign) {\n        hi = ~hi >>> 0;\n        lo = ~lo >>> 0;\n        if (++lo > 4294967295) {\n            lo = 0;\n            if (++hi > 4294967295)\n                hi = 0;\n        }\n    }\n    return new LongBits(lo, hi);\n};\n\n/**\n * Constructs new long bits from a number, long or string.\n * @param {Long|number|string} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.from = function from(value) {\n    if (typeof value === \"number\")\n        return LongBits.fromNumber(value);\n    if (util.isString(value)) {\n        /* istanbul ignore else */\n        if (util.Long)\n            value = util.Long.fromString(value);\n        else\n            return LongBits.fromNumber(parseInt(value, 10));\n    }\n    return value.low || value.high ? new LongBits(value.low >>> 0, value.high >>> 0) : zero;\n};\n\n/**\n * Converts this long bits to a possibly unsafe JavaScript number.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {number} Possibly unsafe number\n */\nLongBits.prototype.toNumber = function toNumber(unsigned) {\n    if (!unsigned && this.hi >>> 31) {\n        var lo = ~this.lo + 1 >>> 0,\n            hi = ~this.hi     >>> 0;\n        if (!lo)\n            hi = hi + 1 >>> 0;\n        return -(lo + hi * 4294967296);\n    }\n    return this.lo + this.hi * 4294967296;\n};\n\n/**\n * Converts this long bits to a long.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long} Long\n */\nLongBits.prototype.toLong = function toLong(unsigned) {\n    return util.Long\n        ? new util.Long(this.lo | 0, this.hi | 0, Boolean(unsigned))\n        /* istanbul ignore next */\n        : { low: this.lo | 0, high: this.hi | 0, unsigned: Boolean(unsigned) };\n};\n\nvar charCodeAt = String.prototype.charCodeAt;\n\n/**\n * Constructs new long bits from the specified 8 characters long hash.\n * @param {string} hash Hash\n * @returns {util.LongBits} Bits\n */\nLongBits.fromHash = function fromHash(hash) {\n    if (hash === zeroHash)\n        return zero;\n    return new LongBits(\n        ( charCodeAt.call(hash, 0)\n        | charCodeAt.call(hash, 1) << 8\n        | charCodeAt.call(hash, 2) << 16\n        | charCodeAt.call(hash, 3) << 24) >>> 0\n    ,\n        ( charCodeAt.call(hash, 4)\n        | charCodeAt.call(hash, 5) << 8\n        | charCodeAt.call(hash, 6) << 16\n        | charCodeAt.call(hash, 7) << 24) >>> 0\n    );\n};\n\n/**\n * Converts this long bits to a 8 characters long hash.\n * @returns {string} Hash\n */\nLongBits.prototype.toHash = function toHash() {\n    return String.fromCharCode(\n        this.lo        & 255,\n        this.lo >>> 8  & 255,\n        this.lo >>> 16 & 255,\n        this.lo >>> 24      ,\n        this.hi        & 255,\n        this.hi >>> 8  & 255,\n        this.hi >>> 16 & 255,\n        this.hi >>> 24\n    );\n};\n\n/**\n * Zig-zag encodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzEncode = function zzEncode() {\n    var mask =   this.hi >> 31;\n    this.hi  = ((this.hi << 1 | this.lo >>> 31) ^ mask) >>> 0;\n    this.lo  = ( this.lo << 1                   ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Zig-zag decodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzDecode = function zzDecode() {\n    var mask = -(this.lo & 1);\n    this.lo  = ((this.lo >>> 1 | this.hi << 31) ^ mask) >>> 0;\n    this.hi  = ( this.hi >>> 1                  ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Calculates the length of this longbits when encoded as a varint.\n * @returns {number} Length\n */\nLongBits.prototype.length = function length() {\n    var part0 =  this.lo,\n        part1 = (this.lo >>> 28 | this.hi << 4) >>> 0,\n        part2 =  this.hi >>> 24;\n    return part2 === 0\n         ? part1 === 0\n           ? part0 < 16384\n             ? part0 < 128 ? 1 : 2\n             : part0 < 2097152 ? 3 : 4\n           : part1 < 16384\n             ? part1 < 128 ? 5 : 6\n             : part1 < 2097152 ? 7 : 8\n         : part2 < 128 ? 9 : 10;\n};\n", "\"use strict\";\nvar util = exports;\n\n// used to return a Promise where callback is omitted\nutil.asPromise = require(1);\n\n// converts to / from base64 encoded strings\nutil.base64 = require(2);\n\n// base class of rpc.Service\nutil.EventEmitter = require(4);\n\n// float handling accross browsers\nutil.float = require(6);\n\n// requires modules optionally and hides the call from bundlers\nutil.inquire = require(7);\n\n// converts to / from utf8 encoded strings\nutil.utf8 = require(10);\n\n// provides a node-like buffer pool in the browser\nutil.pool = require(9);\n\n// utility to work with the low and high bits of a 64 bit value\nutil.LongBits = require(38);\n\n/**\n * Whether running within node or not.\n * @memberof util\n * @type {boolean}\n */\nutil.isNode = Boolean(typeof global !== \"undefined\"\n                   && global\n                   && global.process\n                   && global.process.versions\n                   && global.process.versions.node);\n\n/**\n * Global object reference.\n * @memberof util\n * @type {Object}\n */\nutil.global = util.isNode && global\n           || typeof window !== \"undefined\" && window\n           || typeof self   !== \"undefined\" && self\n           || this; // eslint-disable-line no-invalid-this\n\n/**\n * An immuable empty array.\n * @memberof util\n * @type {Array.<*>}\n * @const\n */\nutil.emptyArray = Object.freeze ? Object.freeze([]) : /* istanbul ignore next */ []; // used on prototypes\n\n/**\n * An immutable empty object.\n * @type {Object}\n * @const\n */\nutil.emptyObject = Object.freeze ? Object.freeze({}) : /* istanbul ignore next */ {}; // used on prototypes\n\n/**\n * Tests if the specified value is an integer.\n * @function\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is an integer\n */\nutil.isInteger = Number.isInteger || /* istanbul ignore next */ function isInteger(value) {\n    return typeof value === \"number\" && isFinite(value) && Math.floor(value) === value;\n};\n\n/**\n * Tests if the specified value is a string.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a string\n */\nutil.isString = function isString(value) {\n    return typeof value === \"string\" || value instanceof String;\n};\n\n/**\n * Tests if the specified value is a non-null object.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a non-null object\n */\nutil.isObject = function isObject(value) {\n    return value && typeof value === \"object\";\n};\n\n/**\n * Checks if a property on a message is considered to be present.\n * This is an alias of {@link util.isSet}.\n * @function\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isset =\n\n/**\n * Checks if a property on a message is considered to be present.\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isSet = function isSet(obj, prop) {\n    var value = obj[prop];\n    if (value != null && obj.hasOwnProperty(prop)) // eslint-disable-line eqeqeq, no-prototype-builtins\n        return typeof value !== \"object\" || (Array.isArray(value) ? value.length : Object.keys(value).length) > 0;\n    return false;\n};\n\n/**\n * Any compatible Buffer instance.\n * This is a minimal stand-alone definition of a Buffer instance. The actual type is that exported by node's typings.\n * @interface Buffer\n * @extends Uint8Array\n */\n\n/**\n * Node's Buffer class if available.\n * @type {Constructor<Buffer>}\n */\nutil.Buffer = (function() {\n    try {\n        var Buffer = util.inquire(\"buffer\").Buffer;\n        // refuse to use non-node buffers if not explicitly assigned (perf reasons):\n        return Buffer.prototype.utf8Write ? Buffer : /* istanbul ignore next */ null;\n    } catch (e) {\n        /* istanbul ignore next */\n        return null;\n    }\n})();\n\n// Internal alias of or polyfull for Buffer.from.\nutil._Buffer_from = null;\n\n// Internal alias of or polyfill for Buffer.allocUnsafe.\nutil._Buffer_allocUnsafe = null;\n\n/**\n * Creates a new buffer of whatever type supported by the environment.\n * @param {number|number[]} [sizeOrArray=0] Buffer size or number array\n * @returns {Uint8Array|Buffer} Buffer\n */\nutil.newBuffer = function newBuffer(sizeOrArray) {\n    /* istanbul ignore next */\n    return typeof sizeOrArray === \"number\"\n        ? util.Buffer\n            ? util._Buffer_allocUnsafe(sizeOrArray)\n            : new util.Array(sizeOrArray)\n        : util.Buffer\n            ? util._Buffer_from(sizeOrArray)\n            : typeof Uint8Array === \"undefined\"\n                ? sizeOrArray\n                : new Uint8Array(sizeOrArray);\n};\n\n/**\n * Array implementation used in the browser. `Uint8Array` if supported, otherwise `Array`.\n * @type {Constructor<Uint8Array>}\n */\nutil.Array = typeof Uint8Array !== \"undefined\" ? Uint8Array /* istanbul ignore next */ : Array;\n\n/**\n * Any compatible Long instance.\n * This is a minimal stand-alone definition of a Long instance. The actual type is that exported by long.js.\n * @interface Long\n * @property {number} low Low bits\n * @property {number} high High bits\n * @property {boolean} unsigned Whether unsigned or not\n */\n\n/**\n * Long.js's Long class if available.\n * @type {Constructor<Long>}\n */\nutil.Long = /* istanbul ignore next */ util.global.dcodeIO && /* istanbul ignore next */ util.global.dcodeIO.Long\n         || /* istanbul ignore next */ util.global.Long\n         || util.inquire(\"long\");\n\n/**\n * Regular expression used to verify 2 bit (`bool`) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key2Re = /^true|false|0|1$/;\n\n/**\n * Regular expression used to verify 32 bit (`int32` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key32Re = /^-?(?:0|[1-9][0-9]*)$/;\n\n/**\n * Regular expression used to verify 64 bit (`int64` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key64Re = /^(?:[\\\\x00-\\\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;\n\n/**\n * Converts a number or long to an 8 characters long hash string.\n * @param {Long|number} value Value to convert\n * @returns {string} Hash\n */\nutil.longToHash = function longToHash(value) {\n    return value\n        ? util.LongBits.from(value).toHash()\n        : util.LongBits.zeroHash;\n};\n\n/**\n * Converts an 8 characters long hash string to a long or number.\n * @param {string} hash Hash\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long|number} Original value\n */\nutil.longFromHash = function longFromHash(hash, unsigned) {\n    var bits = util.LongBits.fromHash(hash);\n    if (util.Long)\n        return util.Long.fromBits(bits.lo, bits.hi, unsigned);\n    return bits.toNumber(Boolean(unsigned));\n};\n\n/**\n * Merges the properties of the source object into the destination object.\n * @memberof util\n * @param {Object.<string,*>} dst Destination object\n * @param {Object.<string,*>} src Source object\n * @param {boolean} [ifNotSet=false] Merges only if the key is not already set\n * @returns {Object.<string,*>} Destination object\n */\nfunction merge(dst, src, ifNotSet) { // used by converters\n    for (var keys = Object.keys(src), i = 0; i < keys.length; ++i)\n        if (dst[keys[i]] === undefined || !ifNotSet)\n            dst[keys[i]] = src[keys[i]];\n    return dst;\n}\n\nutil.merge = merge;\n\n/**\n * Converts the first character of a string to lower case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.lcFirst = function lcFirst(str) {\n    return str.charAt(0).toLowerCase() + str.substring(1);\n};\n\n/**\n * Creates a custom error constructor.\n * @memberof util\n * @param {string} name Error name\n * @returns {Constructor<Error>} Custom error constructor\n */\nfunction newError(name) {\n\n    function CustomError(message, properties) {\n\n        if (!(this instanceof CustomError))\n            return new CustomError(message, properties);\n\n        // Error.call(this, message);\n        // ^ just returns a new error instance because the ctor can be called as a function\n\n        Object.defineProperty(this, \"message\", { get: function() { return message; } });\n\n        /* istanbul ignore next */\n        if (Error.captureStackTrace) // node\n            Error.captureStackTrace(this, CustomError);\n        else\n            Object.defineProperty(this, \"stack\", { value: new Error().stack || \"\" });\n\n        if (properties)\n            merge(this, properties);\n    }\n\n    (CustomError.prototype = Object.create(Error.prototype)).constructor = CustomError;\n\n    Object.defineProperty(CustomError.prototype, \"name\", { get: function() { return name; } });\n\n    CustomError.prototype.toString = function toString() {\n        return this.name + \": \" + this.message;\n    };\n\n    return CustomError;\n}\n\nutil.newError = newError;\n\n/**\n * Constructs a new protocol error.\n * @classdesc Error subclass indicating a protocol specifc error.\n * @memberof util\n * @extends Error\n * @template T extends Message<T>\n * @constructor\n * @param {string} message Error message\n * @param {Object.<string,*>} [properties] Additional properties\n * @example\n * try {\n *     MyMessage.decode(someBuffer); // throws if required fields are missing\n * } catch (e) {\n *     if (e instanceof ProtocolError && e.instance)\n *         console.log(\"decoded so far: \" + JSON.stringify(e.instance));\n * }\n */\nutil.ProtocolError = newError(\"ProtocolError\");\n\n/**\n * So far decoded message instance.\n * @name util.ProtocolError#instance\n * @type {Message<T>}\n */\n\n/**\n * A OneOf getter as returned by {@link util.oneOfGetter}.\n * @typedef OneOfGetter\n * @type {function}\n * @returns {string|undefined} Set field name, if any\n */\n\n/**\n * Builds a getter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfGetter} Unbound getter\n */\nutil.oneOfGetter = function getOneOf(fieldNames) {\n    var fieldMap = {};\n    for (var i = 0; i < fieldNames.length; ++i)\n        fieldMap[fieldNames[i]] = 1;\n\n    /**\n     * @returns {string|undefined} Set field name, if any\n     * @this Object\n     * @ignore\n     */\n    return function() { // eslint-disable-line consistent-return\n        for (var keys = Object.keys(this), i = keys.length - 1; i > -1; --i)\n            if (fieldMap[keys[i]] === 1 && this[keys[i]] !== undefined && this[keys[i]] !== null)\n                return keys[i];\n    };\n};\n\n/**\n * A OneOf setter as returned by {@link util.oneOfSetter}.\n * @typedef OneOfSetter\n * @type {function}\n * @param {string|undefined} value Field name\n * @returns {undefined}\n */\n\n/**\n * Builds a setter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfSetter} Unbound setter\n */\nutil.oneOfSetter = function setOneOf(fieldNames) {\n\n    /**\n     * @param {string} name Field name\n     * @returns {undefined}\n     * @this Object\n     * @ignore\n     */\n    return function(name) {\n        for (var i = 0; i < fieldNames.length; ++i)\n            if (fieldNames[i] !== name)\n                delete this[fieldNames[i]];\n    };\n};\n\n/**\n * Default conversion options used for {@link Message#toJSON} implementations.\n *\n * These options are close to proto3's JSON mapping with the exception that internal types like Any are handled just like messages. More precisely:\n *\n * - Longs become strings\n * - Enums become string keys\n * - Bytes become base64 encoded strings\n * - (Sub-)Messages become plain objects\n * - Maps become plain objects with all string keys\n * - Repeated fields become arrays\n * - NaN and Infinity for float and double fields become strings\n *\n * @type {IConversionOptions}\n * @see https://developers.google.com/protocol-buffers/docs/proto3?hl=en#json\n */\nutil.toJSONOptions = {\n    longs: String,\n    enums: String,\n    bytes: String,\n    json: true\n};\n\n// Sets up buffer utility according to the environment (called in index-minimal)\nutil._configure = function() {\n    var Buffer = util.Buffer;\n    /* istanbul ignore if */\n    if (!Buffer) {\n        util._Buffer_from = util._Buffer_allocUnsafe = null;\n        return;\n    }\n    // because node 4.x buffers are incompatible & immutable\n    // see: https://github.com/dcodeIO/protobuf.js/pull/665\n    util._Buffer_from = Buffer.from !== Uint8Array.from && Buffer.from ||\n        /* istanbul ignore next */\n        function Buffer_from(value, encoding) {\n            return new Buffer(value, encoding);\n        };\n    util._Buffer_allocUnsafe = Buffer.allocUnsafe ||\n        /* istanbul ignore next */\n        function Buffer_allocUnsafe(size) {\n            return new Buffer(size);\n        };\n};\n", "\"use strict\";\nmodule.exports = verifier;\n\nvar Enum      = require(15),\n    util      = require(37);\n\nfunction invalid(field, expected) {\n    return field.name + \": \" + expected + (field.repeated && expected !== \"array\" ? \"[]\" : field.map && expected !== \"object\" ? \"{k:\"+field.keyType+\"}\" : \"\") + \" expected\";\n}\n\n/**\n * Generates a partial value verifier.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genVerifyValue(gen, field, fieldIndex, ref) {\n    /* eslint-disable no-unexpected-multiline */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) { gen\n            (\"switch(%s){\", ref)\n                (\"default:\")\n                    (\"return%j\", invalid(field, \"enum value\"));\n            for (var keys = Object.keys(field.resolvedType.values), j = 0; j < keys.length; ++j) gen\n                (\"case %i:\", field.resolvedType.values[keys[j]]);\n            gen\n                    (\"break\")\n            (\"}\");\n        } else {\n            gen\n            (\"{\")\n                (\"var e=types[%i].verify(%s);\", fieldIndex, ref)\n                (\"if(e)\")\n                    (\"return%j+e\", field.name + \".\")\n            (\"}\");\n        }\n    } else {\n        switch (field.type) {\n            case \"int32\":\n            case \"uint32\":\n            case \"sint32\":\n            case \"fixed32\":\n            case \"sfixed32\": gen\n                (\"if(!util.isInteger(%s))\", ref)\n                    (\"return%j\", invalid(field, \"integer\"));\n                break;\n            case \"int64\":\n            case \"uint64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n                (\"if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))\", ref, ref, ref, ref)\n                    (\"return%j\", invalid(field, \"integer|Long\"));\n                break;\n            case \"float\":\n            case \"double\": gen\n                (\"if(typeof %s!==\\\"number\\\")\", ref)\n                    (\"return%j\", invalid(field, \"number\"));\n                break;\n            case \"bool\": gen\n                (\"if(typeof %s!==\\\"boolean\\\")\", ref)\n                    (\"return%j\", invalid(field, \"boolean\"));\n                break;\n            case \"string\": gen\n                (\"if(!util.isString(%s))\", ref)\n                    (\"return%j\", invalid(field, \"string\"));\n                break;\n            case \"bytes\": gen\n                (\"if(!(%s&&typeof %s.length===\\\"number\\\"||util.isString(%s)))\", ref, ref, ref)\n                    (\"return%j\", invalid(field, \"buffer\"));\n                break;\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline */\n}\n\n/**\n * Generates a partial key verifier.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genVerifyKey(gen, field, ref) {\n    /* eslint-disable no-unexpected-multiline */\n    switch (field.keyType) {\n        case \"int32\":\n        case \"uint32\":\n        case \"sint32\":\n        case \"fixed32\":\n        case \"sfixed32\": gen\n            (\"if(!util.key32Re.test(%s))\", ref)\n                (\"return%j\", invalid(field, \"integer key\"));\n            break;\n        case \"int64\":\n        case \"uint64\":\n        case \"sint64\":\n        case \"fixed64\":\n        case \"sfixed64\": gen\n            (\"if(!util.key64Re.test(%s))\", ref) // see comment above: x is ok, d is not\n                (\"return%j\", invalid(field, \"integer|Long key\"));\n            break;\n        case \"bool\": gen\n            (\"if(!util.key2Re.test(%s))\", ref)\n                (\"return%j\", invalid(field, \"boolean key\"));\n            break;\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline */\n}\n\n/**\n * Generates a verifier specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction verifier(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n\n    var gen = util.codegen([\"m\"], mtype.name + \"$verify\")\n    (\"if(typeof m!==\\\"object\\\"||m===null)\")\n        (\"return%j\", \"object expected\");\n    var oneofs = mtype.oneofsArray,\n        seenFirstField = {};\n    if (oneofs.length) gen\n    (\"var p={}\");\n\n    for (var i = 0; i < /* initializes */ mtype.fieldsArray.length; ++i) {\n        var field = mtype._fieldsArray[i].resolve(),\n            ref   = \"m\" + util.safeProp(field.name);\n\n        if (field.optional) gen\n        (\"if(%s!=null&&m.hasOwnProperty(%j)){\", ref, field.name); // !== undefined && !== null\n\n        // map fields\n        if (field.map) { gen\n            (\"if(!util.isObject(%s))\", ref)\n                (\"return%j\", invalid(field, \"object\"))\n            (\"var k=Object.keys(%s)\", ref)\n            (\"for(var i=0;i<k.length;++i){\");\n                genVerifyKey(gen, field, \"k[i]\");\n                genVerifyValue(gen, field, i, ref + \"[k[i]]\")\n            (\"}\");\n\n        // repeated fields\n        } else if (field.repeated) { gen\n            (\"if(!Array.isArray(%s))\", ref)\n                (\"return%j\", invalid(field, \"array\"))\n            (\"for(var i=0;i<%s.length;++i){\", ref);\n                genVerifyValue(gen, field, i, ref + \"[i]\")\n            (\"}\");\n\n        // required or present fields\n        } else {\n            if (field.partOf) {\n                var oneofProp = util.safeProp(field.partOf.name);\n                if (seenFirstField[field.partOf.name] === 1) gen\n            (\"if(p%s===1)\", oneofProp)\n                (\"return%j\", field.partOf.name + \": multiple values\");\n                seenFirstField[field.partOf.name] = 1;\n                gen\n            (\"p%s=1\", oneofProp);\n            }\n            genVerifyValue(gen, field, i, ref);\n        }\n        if (field.optional) gen\n        (\"}\");\n    }\n    return gen\n    (\"return null\");\n    /* eslint-enable no-unexpected-multiline */\n}", "\"use strict\";\n\n/**\n * Wrappers for common types.\n * @type {Object.<string,IWrapper>}\n * @const\n */\nvar wrappers = exports;\n\nvar Message = require(21);\n\n/**\n * From object converter part of an {@link IWrapper}.\n * @typedef WrapperFromObjectConverter\n * @type {function}\n * @param {Object.<string,*>} object Plain object\n * @returns {Message<{}>} Message instance\n * @this Type\n */\n\n/**\n * To object converter part of an {@link IWrapper}.\n * @typedef WrapperToObjectConverter\n * @type {function}\n * @param {Message<{}>} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n * @this Type\n */\n\n/**\n * Common type wrapper part of {@link wrappers}.\n * @interface IWrapper\n * @property {WrapperFromObjectConverter} [fromObject] From object converter\n * @property {WrapperToObjectConverter} [toObject] To object converter\n */\n\n// Custom wrapper for Any\nwrappers[\".google.protobuf.Any\"] = {\n\n    fromObject: function(object) {\n\n        // unwrap value type if mapped\n        if (object && object[\"@type\"]) {\n             // Only use fully qualified type name after the last '/'\n            var name = object[\"@type\"].substring(object[\"@type\"].lastIndexOf(\"/\") + 1);\n            var type = this.lookup(name);\n            /* istanbul ignore else */\n            if (type) {\n                // type_url does not accept leading \".\"\n                var type_url = object[\"@type\"].charAt(0) === \".\" ?\n                    object[\"@type\"].substr(1) : object[\"@type\"];\n                // type_url prefix is optional, but path seperator is required\n                if (type_url.indexOf(\"/\") === -1) {\n                    type_url = \"/\" + type_url;\n                }\n                return this.create({\n                    type_url: type_url,\n                    value: type.encode(type.fromObject(object)).finish()\n                });\n            }\n        }\n\n        return this.fromObject(object);\n    },\n\n    toObject: function(message, options) {\n\n        // Default prefix\n        var googleApi = \"type.googleapis.com/\";\n        var prefix = \"\";\n        var name = \"\";\n\n        // decode value if requested and unmapped\n        if (options && options.json && message.type_url && message.value) {\n            // Only use fully qualified type name after the last '/'\n            name = message.type_url.substring(message.type_url.lastIndexOf(\"/\") + 1);\n            // Separate the prefix used\n            prefix = message.type_url.substring(0, message.type_url.lastIndexOf(\"/\") + 1);\n            var type = this.lookup(name);\n            /* istanbul ignore else */\n            if (type)\n                message = type.decode(message.value);\n        }\n\n        // wrap value if unmapped\n        if (!(message instanceof this.ctor) && message instanceof Message) {\n            var object = message.$type.toObject(message, options);\n            var messageName = message.$type.fullName[0] === \".\" ?\n                message.$type.fullName.substr(1) : message.$type.fullName;\n            // Default to type.googleapis.com prefix if no prefix is used\n            if (prefix === \"\") {\n                prefix = googleApi;\n            }\n            name = prefix + messageName;\n            object[\"@type\"] = name;\n            return object;\n        }\n\n        return this.toObject(message, options);\n    }\n};\n", "\"use strict\";\nmodule.exports = Writer;\n\nvar util      = require(39);\n\nvar BufferWriter; // cyclic\n\nvar LongBits  = util.LongBits,\n    base64    = util.base64,\n    utf8      = util.utf8;\n\n/**\n * Constructs a new writer operation instance.\n * @classdesc Scheduled writer operation.\n * @constructor\n * @param {function(*, Uint8Array, number)} fn Function to call\n * @param {number} len Value byte length\n * @param {*} val Value to write\n * @ignore\n */\nfunction Op(fn, len, val) {\n\n    /**\n     * Function to call.\n     * @type {function(Uint8Array, number, *)}\n     */\n    this.fn = fn;\n\n    /**\n     * Value byte length.\n     * @type {number}\n     */\n    this.len = len;\n\n    /**\n     * Next operation.\n     * @type {Writer.Op|undefined}\n     */\n    this.next = undefined;\n\n    /**\n     * Value to write.\n     * @type {*}\n     */\n    this.val = val; // type varies\n}\n\n/* istanbul ignore next */\nfunction noop() {} // eslint-disable-line no-empty-function\n\n/**\n * Constructs a new writer state instance.\n * @classdesc Copied writer state.\n * @memberof Writer\n * @constructor\n * @param {Writer} writer Writer to copy state from\n * @ignore\n */\nfunction State(writer) {\n\n    /**\n     * Current head.\n     * @type {Writer.Op}\n     */\n    this.head = writer.head;\n\n    /**\n     * Current tail.\n     * @type {Writer.Op}\n     */\n    this.tail = writer.tail;\n\n    /**\n     * Current buffer length.\n     * @type {number}\n     */\n    this.len = writer.len;\n\n    /**\n     * Next state.\n     * @type {State|null}\n     */\n    this.next = writer.states;\n}\n\n/**\n * Constructs a new writer instance.\n * @classdesc Wire format writer using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n */\nfunction Writer() {\n\n    /**\n     * Current length.\n     * @type {number}\n     */\n    this.len = 0;\n\n    /**\n     * Operations head.\n     * @type {Object}\n     */\n    this.head = new Op(noop, 0, 0);\n\n    /**\n     * Operations tail\n     * @type {Object}\n     */\n    this.tail = this.head;\n\n    /**\n     * Linked forked states.\n     * @type {Object|null}\n     */\n    this.states = null;\n\n    // When a value is written, the writer calculates its byte length and puts it into a linked\n    // list of operations to perform when finish() is called. This both allows us to allocate\n    // buffers of the exact required size and reduces the amount of work we have to do compared\n    // to first calculating over objects and then encoding over objects. In our case, the encoding\n    // part is just a linked list walk calling operations with already prepared values.\n}\n\nvar create = function create() {\n    return util.Buffer\n        ? function create_buffer_setup() {\n            return (Writer.create = function create_buffer() {\n                return new BufferWriter();\n            })();\n        }\n        /* istanbul ignore next */\n        : function create_array() {\n            return new Writer();\n        };\n};\n\n/**\n * Creates a new writer.\n * @function\n * @returns {BufferWriter|Writer} A {@link BufferWriter} when Buffers are supported, otherwise a {@link Writer}\n */\nWriter.create = create();\n\n/**\n * Allocates a buffer of the specified size.\n * @param {number} size Buffer size\n * @returns {Uint8Array} Buffer\n */\nWriter.alloc = function alloc(size) {\n    return new util.Array(size);\n};\n\n// Use Uint8Array buffer pool in the browser, just like node does with buffers\n/* istanbul ignore else */\nif (util.Array !== Array)\n    Writer.alloc = util.pool(Writer.alloc, util.Array.prototype.subarray);\n\n/**\n * Pushes a new operation to the queue.\n * @param {function(Uint8Array, number, *)} fn Function to call\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @returns {Writer} `this`\n * @private\n */\nWriter.prototype._push = function push(fn, len, val) {\n    this.tail = this.tail.next = new Op(fn, len, val);\n    this.len += len;\n    return this;\n};\n\nfunction writeByte(val, buf, pos) {\n    buf[pos] = val & 255;\n}\n\nfunction writeVarint32(val, buf, pos) {\n    while (val > 127) {\n        buf[pos++] = val & 127 | 128;\n        val >>>= 7;\n    }\n    buf[pos] = val;\n}\n\n/**\n * Constructs a new varint writer operation instance.\n * @classdesc Scheduled varint writer operation.\n * @extends Op\n * @constructor\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @ignore\n */\nfunction VarintOp(len, val) {\n    this.len = len;\n    this.next = undefined;\n    this.val = val;\n}\n\nVarintOp.prototype = Object.create(Op.prototype);\nVarintOp.prototype.fn = writeVarint32;\n\n/**\n * Writes an unsigned 32 bit value as a varint.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.uint32 = function write_uint32(value) {\n    // here, the call to this.push has been inlined and a varint specific Op subclass is used.\n    // uint32 is by far the most frequently used operation and benefits significantly from this.\n    this.len += (this.tail = this.tail.next = new VarintOp(\n        (value = value >>> 0)\n                < 128       ? 1\n        : value < 16384     ? 2\n        : value < 2097152   ? 3\n        : value < 268435456 ? 4\n        :                     5,\n    value)).len;\n    return this;\n};\n\n/**\n * Writes a signed 32 bit value as a varint.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.int32 = function write_int32(value) {\n    return value < 0\n        ? this._push(writeVarint64, 10, LongBits.fromNumber(value)) // 10 bytes per spec\n        : this.uint32(value);\n};\n\n/**\n * Writes a 32 bit value as a varint, zig-zag encoded.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sint32 = function write_sint32(value) {\n    return this.uint32((value << 1 ^ value >> 31) >>> 0);\n};\n\nfunction writeVarint64(val, buf, pos) {\n    while (val.hi) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = (val.lo >>> 7 | val.hi << 25) >>> 0;\n        val.hi >>>= 7;\n    }\n    while (val.lo > 127) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = val.lo >>> 7;\n    }\n    buf[pos++] = val.lo;\n}\n\n/**\n * Writes an unsigned 64 bit value as a varint.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.uint64 = function write_uint64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a signed 64 bit value as a varint.\n * @function\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.int64 = Writer.prototype.uint64;\n\n/**\n * Writes a signed 64 bit value as a varint, zig-zag encoded.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sint64 = function write_sint64(value) {\n    var bits = LongBits.from(value).zzEncode();\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a boolish value as a varint.\n * @param {boolean} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bool = function write_bool(value) {\n    return this._push(writeByte, 1, value ? 1 : 0);\n};\n\nfunction writeFixed32(val, buf, pos) {\n    buf[pos    ] =  val         & 255;\n    buf[pos + 1] =  val >>> 8   & 255;\n    buf[pos + 2] =  val >>> 16  & 255;\n    buf[pos + 3] =  val >>> 24;\n}\n\n/**\n * Writes an unsigned 32 bit value as fixed 32 bits.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.fixed32 = function write_fixed32(value) {\n    return this._push(writeFixed32, 4, value >>> 0);\n};\n\n/**\n * Writes a signed 32 bit value as fixed 32 bits.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sfixed32 = Writer.prototype.fixed32;\n\n/**\n * Writes an unsigned 64 bit value as fixed 64 bits.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.fixed64 = function write_fixed64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeFixed32, 4, bits.lo)._push(writeFixed32, 4, bits.hi);\n};\n\n/**\n * Writes a signed 64 bit value as fixed 64 bits.\n * @function\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sfixed64 = Writer.prototype.fixed64;\n\n/**\n * Writes a float (32 bit).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.float = function write_float(value) {\n    return this._push(util.float.writeFloatLE, 4, value);\n};\n\n/**\n * Writes a double (64 bit float).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.double = function write_double(value) {\n    return this._push(util.float.writeDoubleLE, 8, value);\n};\n\nvar writeBytes = util.Array.prototype.set\n    ? function writeBytes_set(val, buf, pos) {\n        buf.set(val, pos); // also works for plain array values\n    }\n    /* istanbul ignore next */\n    : function writeBytes_for(val, buf, pos) {\n        for (var i = 0; i < val.length; ++i)\n            buf[pos + i] = val[i];\n    };\n\n/**\n * Writes a sequence of bytes.\n * @param {Uint8Array|string} value Buffer or base64 encoded string to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bytes = function write_bytes(value) {\n    var len = value.length >>> 0;\n    if (!len)\n        return this._push(writeByte, 1, 0);\n    if (util.isString(value)) {\n        var buf = Writer.alloc(len = base64.length(value));\n        base64.decode(value, buf, 0);\n        value = buf;\n    }\n    return this.uint32(len)._push(writeBytes, len, value);\n};\n\n/**\n * Writes a string.\n * @param {string} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.string = function write_string(value) {\n    var len = utf8.length(value);\n    return len\n        ? this.uint32(len)._push(utf8.write, len, value)\n        : this._push(writeByte, 1, 0);\n};\n\n/**\n * Forks this writer's state by pushing it to a stack.\n * Calling {@link Writer#reset|reset} or {@link Writer#ldelim|ldelim} resets the writer to the previous state.\n * @returns {Writer} `this`\n */\nWriter.prototype.fork = function fork() {\n    this.states = new State(this);\n    this.head = this.tail = new Op(noop, 0, 0);\n    this.len = 0;\n    return this;\n};\n\n/**\n * Resets this instance to the last state.\n * @returns {Writer} `this`\n */\nWriter.prototype.reset = function reset() {\n    if (this.states) {\n        this.head   = this.states.head;\n        this.tail   = this.states.tail;\n        this.len    = this.states.len;\n        this.states = this.states.next;\n    } else {\n        this.head = this.tail = new Op(noop, 0, 0);\n        this.len  = 0;\n    }\n    return this;\n};\n\n/**\n * Resets to the last state and appends the fork state's current write length as a varint followed by its operations.\n * @returns {Writer} `this`\n */\nWriter.prototype.ldelim = function ldelim() {\n    var head = this.head,\n        tail = this.tail,\n        len  = this.len;\n    this.reset().uint32(len);\n    if (len) {\n        this.tail.next = head.next; // skip noop\n        this.tail = tail;\n        this.len += len;\n    }\n    return this;\n};\n\n/**\n * Finishes the write operation.\n * @returns {Uint8Array} Finished buffer\n */\nWriter.prototype.finish = function finish() {\n    var head = this.head.next, // skip noop\n        buf  = this.constructor.alloc(this.len),\n        pos  = 0;\n    while (head) {\n        head.fn(head.val, buf, pos);\n        pos += head.len;\n        head = head.next;\n    }\n    // this.head = this.tail = null;\n    return buf;\n};\n\nWriter._configure = function(BufferWriter_) {\n    BufferWriter = BufferWriter_;\n    Writer.create = create();\n    BufferWriter._configure();\n};\n", "\"use strict\";\nmodule.exports = <PERSON><PERSON>erWriter;\n\n// extends Writer\nvar Writer = require(42);\n(BufferWriter.prototype = Object.create(Writer.prototype)).constructor = BufferWriter;\n\nvar util = require(39);\n\n/**\n * Constructs a new buffer writer instance.\n * @classdesc Wire format writer using node buffers.\n * @extends Writer\n * @constructor\n */\nfunction BufferWriter() {\n    Writer.call(this);\n}\n\nBufferWriter._configure = function () {\n    /**\n     * Allocates a buffer of the specified size.\n     * @function\n     * @param {number} size Buffer size\n     * @returns {Buffer} Buffer\n     */\n    BufferWriter.alloc = util._Buffer_allocUnsafe;\n\n    BufferWriter.writeBytesBuffer = util.Buffer && util.Buffer.prototype instanceof Uint8Array && util.Buffer.prototype.set.name === \"set\"\n        ? function writeBytesBuffer_set(val, buf, pos) {\n          buf.set(val, pos); // faster than copy (requires node >= 4 where Buffers extend Uint8Array and set is properly inherited)\n          // also works for plain array values\n        }\n        /* istanbul ignore next */\n        : function writeBytesBuffer_copy(val, buf, pos) {\n          if (val.copy) // Buffer values\n            val.copy(buf, pos, 0, val.length);\n          else for (var i = 0; i < val.length;) // plain array values\n            buf[pos++] = val[i++];\n        };\n};\n\n\n/**\n * @override\n */\nBufferWriter.prototype.bytes = function write_bytes_buffer(value) {\n    if (util.isString(value))\n        value = util._Buffer_from(value, \"base64\");\n    var len = value.length >>> 0;\n    this.uint32(len);\n    if (len)\n        this._push(BufferWriter.writeBytesBuffer, len, value);\n    return this;\n};\n\nfunction writeStringBuffer(val, buf, pos) {\n    if (val.length < 40) // plain js is faster for short strings (probably due to redundant assertions)\n        util.utf8.write(val, buf, pos);\n    else if (buf.utf8Write)\n        buf.utf8Write(val, pos);\n    else\n        buf.write(val, pos);\n}\n\n/**\n * @override\n */\nBufferWriter.prototype.string = function write_string_buffer(value) {\n    var len = util.Buffer.byteLength(value);\n    this.uint32(len);\n    if (len)\n        this._push(writeStringBuffer, len, value);\n    return this;\n};\n\n\n/**\n * Finishes the write operation.\n * @name BufferWriter#finish\n * @function\n * @returns {Buffer} Finished buffer\n */\n\nBufferWriter._configure();\n"], "sourceRoot": "."}