// 云函数：knowledge-analyzer
// 专门用于分析知识库结构和内容特征

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { action = 'analyze_structure' } = event;
  
  console.log(`知识库分析器被调用: ${action}`);
  
  try {
    switch (action) {
      case 'analyze_structure':
        return await analyzeKnowledgeStructure();
      case 'extract_keywords':
        return await extractKeywords();
      case 'categorize_content':
        return await categorizeContent();
      case 'analyze_terminology':
        return await analyzeTerminology();
      case 'generate_summary':
        return await generateSummary();
      default:
        return { success: false, error: '未知的分析操作' };
    }
  } catch (error) {
    console.error('知识库分析失败:', error);
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * 分析知识库整体结构
 */
async function analyzeKnowledgeStructure() {
  console.log('开始分析知识库结构...');
  
  // 获取总体统计
  const totalCount = await db.collection('knowledge_base').count();
  
  // 获取所有记录进行统计分析
  const allRecords = await db.collection('knowledge_base')
    .field({
      title: true,
      category: true,
      author: true,
      dynasty: true,
      content: true
    })
    .get();

  // 手动统计分类分布
  const categoryStats = {};
  const dynastyStats = {};
  const authorStats = {};
  let totalSize = 0;
  let minSize = Infinity;
  let maxSize = 0;

  allRecords.data.forEach(record => {
    const category = record.category || '未分类';
    const dynasty = record.dynasty || '未知朝代';
    const author = record.author || '未知作者';
    const size = (record.content || '').length;

    // 分类统计
    if (!categoryStats[category]) {
      categoryStats[category] = { count: 0, titles: [], totalSize: 0 };
    }
    categoryStats[category].count++;
    categoryStats[category].titles.push(record.title);
    categoryStats[category].totalSize += size;

    // 朝代统计
    if (!dynastyStats[dynasty]) {
      dynastyStats[dynasty] = { count: 0, categories: new Set() };
    }
    dynastyStats[dynasty].count++;
    dynastyStats[dynasty].categories.add(category);

    // 作者统计
    if (!authorStats[author]) {
      authorStats[author] = { count: 0, works: [] };
    }
    authorStats[author].count++;
    authorStats[author].works.push(record.title);

    // 大小统计
    totalSize += size;
    minSize = Math.min(minSize, size);
    maxSize = Math.max(maxSize, size);
  });

  // 转换Set为Array
  Object.keys(dynastyStats).forEach(dynasty => {
    dynastyStats[dynasty].categories = Array.from(dynastyStats[dynasty].categories);
  });

  const sizeDistribution = {
    minSize: minSize === Infinity ? 0 : minSize,
    maxSize,
    avgSize: Math.round(totalSize / allRecords.data.length),
    totalSize
  };
  
  return {
    success: true,
    analysis: {
      total_records: totalCount.total,
      category_distribution: Object.keys(categoryStats).map(key => ({
        _id: key,
        count: categoryStats[key].count,
        avgSize: Math.round(categoryStats[key].totalSize / categoryStats[key].count),
        titles: categoryStats[key].titles.slice(0, 5) // 限制返回的标题数量
      })),
      dynasty_distribution: Object.keys(dynastyStats).map(key => ({
        _id: key,
        count: dynastyStats[key].count,
        categories: dynastyStats[key].categories
      })),
      author_distribution: Object.keys(authorStats).map(key => ({
        _id: key,
        count: authorStats[key].count,
        works: authorStats[key].works.slice(0, 5) // 限制返回的作品数量
      })),
      size_statistics: sizeDistribution,
      analysis_timestamp: new Date().toISOString()
    }
  };
}

/**
 * 提取关键词和术语
 */
async function extractKeywords() {
  console.log('开始提取关键词...');
  
  // 获取所有记录的标题和关键词字段
  const records = await db.collection('knowledge_base')
    .field({
      title: true,
      keywords: true,
      category: true,
      author: true
    })
    .get();
  
  const keywordAnalysis = {
    by_category: {},
    by_author: {},
    common_terms: {},
    title_patterns: []
  };
  
  // 分析每个分类的关键词
  records.data.forEach(record => {
    const category = record.category || '未分类';
    const author = record.author || '未知作者';
    
    if (!keywordAnalysis.by_category[category]) {
      keywordAnalysis.by_category[category] = {
        titles: [],
        keywords: [],
        count: 0
      };
    }
    
    if (!keywordAnalysis.by_author[author]) {
      keywordAnalysis.by_author[author] = {
        titles: [],
        works_count: 0
      };
    }
    
    keywordAnalysis.by_category[category].titles.push(record.title);
    keywordAnalysis.by_category[category].count++;
    
    keywordAnalysis.by_author[author].titles.push(record.title);
    keywordAnalysis.by_author[author].works_count++;
    
    if (record.keywords && Array.isArray(record.keywords)) {
      keywordAnalysis.by_category[category].keywords.push(...record.keywords);
    }
    
    // 分析标题模式
    if (record.title) {
      const titleWords = record.title.match(/[\u4e00-\u9fa5]+/g) || [];
      titleWords.forEach(word => {
        if (word.length >= 2) {
          keywordAnalysis.common_terms[word] = (keywordAnalysis.common_terms[word] || 0) + 1;
        }
      });
    }
  });
  
  // 排序常见术语
  const sortedTerms = Object.entries(keywordAnalysis.common_terms)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 50)
    .map(([term, count]) => ({ term, count }));
  
  return {
    success: true,
    keywords: {
      ...keywordAnalysis,
      common_terms: sortedTerms,
      total_analyzed: records.data.length
    }
  };
}

/**
 * 内容分类分析
 */
async function categorizeContent() {
  console.log('开始内容分类分析...');
  
  // 获取部分内容进行分析
  const sampleRecords = await db.collection('knowledge_base')
    .field({
      title: true,
      category: true,
      content: true,
      file_size: true
    })
    .limit(50)
    .get();
  
  const contentAnalysis = {
    categories: {},
    content_patterns: {},
    length_distribution: {
      short: 0,    // < 10KB
      medium: 0,   // 10KB - 100KB
      long: 0      // > 100KB
    }
  };
  
  sampleRecords.data.forEach(record => {
    const category = record.category || '未分类';
    const content = record.content || '';
    const size = record.file_size || 0;
    
    if (!contentAnalysis.categories[category]) {
      contentAnalysis.categories[category] = {
        count: 0,
        avg_length: 0,
        total_length: 0,
        sample_titles: []
      };
    }
    
    contentAnalysis.categories[category].count++;
    contentAnalysis.categories[category].total_length += content.length;
    contentAnalysis.categories[category].sample_titles.push(record.title);
    
    // 长度分布
    if (size < 10000) {
      contentAnalysis.length_distribution.short++;
    } else if (size < 100000) {
      contentAnalysis.length_distribution.medium++;
    } else {
      contentAnalysis.length_distribution.long++;
    }
    
    // 内容模式分析（查找常见的结构性词汇）
    const structuralWords = ['卷', '章', '篇', '节', '条', '则', '法', '诀', '歌', '赋'];
    structuralWords.forEach(word => {
      if (content.includes(word)) {
        contentAnalysis.content_patterns[word] = (contentAnalysis.content_patterns[word] || 0) + 1;
      }
    });
  });
  
  // 计算平均长度
  Object.keys(contentAnalysis.categories).forEach(category => {
    const cat = contentAnalysis.categories[category];
    cat.avg_length = Math.round(cat.total_length / cat.count);
  });
  
  return {
    success: true,
    content_analysis: {
      ...contentAnalysis,
      sample_size: sampleRecords.data.length
    }
  };
}

/**
 * 专业术语分析
 */
async function analyzeTerminology() {
  console.log('开始专业术语分析...');
  
  // 预定义的专业术语词典
  const terminologyDict = {
    '梅花易数': ['体卦', '用卦', '互卦', '变卦', '先天数', '后天数', '八卦万物'],
    '六爻': ['世爻', '应爻', '用神', '原神', '忌神', '仇神', '六亲', '六神', '动爻', '静爻'],
    '八字': ['日主', '用神', '喜神', '忌神', '十神', '格局', '大运', '流年', '身强', '身弱'],
    '紫微': ['命宫', '身宫', '十四主星', '四化', '三方四正', '大限', '流年', '庙旺利陷']
  };
  
  // 获取样本内容进行术语频率分析
  const sampleRecords = await db.collection('knowledge_base')
    .field({
      title: true,
      category: true,
      content: true
    })
    .limit(100)
    .get();
  
  const terminologyAnalysis = {
    by_category: {},
    term_frequency: {},
    coverage_analysis: {}
  };
  
  // 分析每个分类中术语的出现频率
  Object.keys(terminologyDict).forEach(category => {
    terminologyAnalysis.by_category[category] = {};
    terminologyAnalysis.coverage_analysis[category] = {
      total_terms: terminologyDict[category].length,
      found_terms: 0,
      coverage_rate: 0
    };
    
    terminologyDict[category].forEach(term => {
      terminologyAnalysis.by_category[category][term] = 0;
      terminologyAnalysis.term_frequency[term] = 0;
    });
  });
  
  // 统计术语出现频率
  sampleRecords.data.forEach(record => {
    const content = record.content || '';
    const title = record.title || '';
    const fullText = title + content;
    
    Object.keys(terminologyDict).forEach(category => {
      terminologyDict[category].forEach(term => {
        const matches = (fullText.match(new RegExp(term, 'g')) || []).length;
        if (matches > 0) {
          terminologyAnalysis.by_category[category][term] += matches;
          terminologyAnalysis.term_frequency[term] += matches;
          
          if (terminologyAnalysis.coverage_analysis[category]) {
            terminologyAnalysis.coverage_analysis[category].found_terms++;
          }
        }
      });
    });
  });
  
  // 计算覆盖率
  Object.keys(terminologyAnalysis.coverage_analysis).forEach(category => {
    const analysis = terminologyAnalysis.coverage_analysis[category];
    analysis.coverage_rate = Math.round((analysis.found_terms / analysis.total_terms) * 100);
  });
  
  return {
    success: true,
    terminology: {
      ...terminologyAnalysis,
      sample_size: sampleRecords.data.length,
      dictionary_size: Object.values(terminologyDict).flat().length
    }
  };
}

/**
 * 生成知识库总结报告
 */
async function generateSummary() {
  console.log('生成知识库总结报告...');
  
  // 调用其他分析函数获取数据
  const [structure, keywords, content, terminology] = await Promise.all([
    analyzeKnowledgeStructure(),
    extractKeywords(),
    categorizeContent(),
    analyzeTerminology()
  ]);
  
  const summary = {
    overview: {
      total_records: structure.analysis.total_records,
      categories: Object.keys(structure.analysis.category_distribution).length,
      authors: Object.keys(structure.analysis.author_distribution).length,
      total_size_mb: Math.round(structure.analysis.size_statistics.totalSize / (1024 * 1024))
    },
    content_quality: {
      avg_file_size_kb: Math.round(structure.analysis.size_statistics.avgSize / 1024),
      terminology_coverage: terminology.terminology.coverage_analysis,
      common_terms_count: keywords.keywords.common_terms.length
    },
    recommendations: [
      '建议对内容进行进一步的结构化处理',
      '可以建立更完善的术语索引系统',
      '需要统一不同分类的内容格式',
      '建议增加内容的语义标注'
    ],
    generated_at: new Date().toISOString()
  };
  
  return {
    success: true,
    summary,
    detailed_analysis: {
      structure: structure.analysis,
      keywords: keywords.keywords,
      content: content.content_analysis,
      terminology: terminology.terminology
    }
  };
}
