<?js
    var self = this;
    var isGlobalPage;

    docs.forEach(function(doc, i) {
?>

<?js
    // we only need to check this once
    if (typeof isGlobalPage === 'undefined') {
        isGlobalPage = (doc.kind === 'globalobj');
    }
?>
<?js if (doc.kind === 'mainpage' || (doc.kind === 'package')) { ?>
    <?js= self.partial('mainpage.tmpl', doc) ?>
<?js } else if (doc.kind === 'source') { ?>
    <?js= self.partial('source.tmpl', doc) ?>
<?js } else { ?>

<section>

<header>
    <?js if (!doc.longname || doc.kind !== 'module') { ?>
        <h2><?js if (doc.attribs) { ?><span class="attribs"><?js= doc.attribs ?></span><?js }
            if (doc.ancestors && doc.ancestors.length) { ?>
            <span class="ancestors"><?js= doc.ancestors.join('') ?></span><?js
         }
        ?><?js= doc.name ?><?js
         if (doc.variation) { ?>
            <sup class="variation"><?js= doc.variation ?></sup><?js }
         if (doc.signature && !doc.hideconstructor) { ?><?js= doc.signature ?><?js } ?></h2>
        <?js if (doc.classdesc) { ?>
            <div class="class-description"><?js= doc.classdesc ?></div>
        <?js } ?>
    <?js } else if (doc.kind === 'module' && doc.modules) { ?>
        <?js doc.modules.forEach(function(module) { ?>
            <?js if (module.classdesc) { ?>
                <div class="class-description"><?js= module.classdesc ?></div>
            <?js } ?>
        <?js }) ?>
    <?js } ?>
</header>

<article>
    <div class="container-overview">
    <?js if (doc.kind === 'module' && doc.modules) { ?>
        <?js if (doc.description) { ?>
            <div class="description"><?js= doc.description ?></div>
        <?js } ?>

        <?js doc.modules.forEach(function(module) { ?>
            <?js= self.partial('method.tmpl', module) ?>
        <?js }) ?>
    <?js } else if (doc.kind === 'class' || (doc.kind === 'namespace' && doc.signature)) { ?>
        <?js= self.partial('method.tmpl', doc) ?>
    <?js } else { ?>
        <?js if (doc.description) { ?>
            <div class="description"><?js= doc.description ?></div>
        <?js } ?>

        <?js= self.partial('details.tmpl', doc) ?>

        <?js if (doc.examples && doc.examples.length) { ?>
            <h3>Example<?js= doc.examples.length > 1? 's':'' ?></h3>
            <?js= self.partial('examples.tmpl', doc.examples) ?>
        <?js } ?>
    <?js } ?>
    </div>

    <?js if (doc.augments && doc.augments.length) { ?>
        <h3 class="subsection-title">Extends</h3>

        <?js= self.partial('augments.tmpl', doc) ?>
    <?js } ?>

    <?js if (doc.requires && doc.requires.length) { ?>
        <h3 class="subsection-title">Requires</h3>

        <ul><?js doc.requires.forEach(function(r) { ?>
            <li><?js= self.linkto(r, r) ?></li>
        <?js }); ?></ul>
    <?js } ?>

    <?js
        var classes = self.find({kind: 'class', memberof: doc.longname});
        if (!isGlobalPage && classes && classes.length) {
    ?>
        <h3 class="subsection-title">Classes</h3>

        <dl><?js classes.forEach(function(c) { ?>
            <dt><?js= self.linkto(c.longname, c.name) ?></dt>
            <dd><?js if (c.summary) { ?><?js= c.summary ?><?js } ?></dd>
        <?js }); ?></dl>
    <?js } ?>

    <?js
        var interfaces = self.find({kind: 'interface', memberof: doc.longname});
        if (!isGlobalPage && interfaces && interfaces.length) {
    ?>
        <h3 class="subsection-title">Interfaces</h3>

        <dl><?js interfaces.forEach(function(i) { ?>
            <dt><?js= self.linkto(i.longname, i.name) ?></dt>
            <dd><?js if (i.summary) { ?><?js= i.summary ?><?js } ?></dd>
        <?js }); ?></dl>
    <?js } ?>

    <?js
        var mixins = self.find({kind: 'mixin', memberof: doc.longname});
        if (!isGlobalPage && mixins && mixins.length) {
    ?>
        <h3 class="subsection-title">Mixins</h3>

        <dl><?js mixins.forEach(function(m) { ?>
            <dt><?js= self.linkto(m.longname, m.name) ?></dt>
            <dd><?js if (m.summary) { ?><?js= m.summary ?><?js } ?></dd>
        <?js }); ?></dl>
    <?js } ?>

    <?js
        var namespaces = self.find({kind: 'namespace', memberof: doc.longname});
        if (!isGlobalPage && namespaces && namespaces.length) {
    ?>
        <h3 class="subsection-title">Namespaces</h3>

        <dl><?js namespaces.forEach(function(n) { ?>
            <dt><?js= self.linkto(n.longname, n.name) ?></dt>
            <dd><?js if (n.summary) { ?><?js= n.summary ?><?js } ?></dd>
        <?js }); ?></dl>
    <?js } ?>

    <?js
        var members = self.find({kind: 'member', memberof: isGlobalPage ? {isUndefined: true} : doc.longname});

        // symbols that are assigned to module.exports are not globals, even though they're not a memberof anything
        if (isGlobalPage && members && members.length && members.forEach) {
            members = members.filter(function(m) {
                return m.longname && m.longname.indexOf('module:') !== 0;
            });
        }
        if (members && members.length && members.forEach) {
    ?>
        <h3 class="subsection-title">Members</h3>

        <?js members.forEach(function(p) { ?>
            <?js= self.partial('members.tmpl', p) ?>
        <?js }); ?>
    <?js } ?>

    <?js
        var methods = self.find({kind: 'function', memberof: isGlobalPage ? {isUndefined: true} : doc.longname});
        if (methods && methods.length && methods.forEach) {
    ?>
        <h3 class="subsection-title">Methods</h3>

        <?js methods.forEach(function(m) { ?>
            <?js= self.partial('method.tmpl', m) ?>
        <?js }); ?>
    <?js } ?>

    <?js
        var typedefs = self.find({kind: 'typedef', memberof: isGlobalPage ? {isUndefined: true} : doc.longname});
        if (typedefs && typedefs.length && typedefs.forEach) {
    ?>
        <h3 class="subsection-title">Type Definitions</h3>

        <?js typedefs.forEach(function(e) {
                if (e.signature) {
            ?>
                <?js= self.partial('method.tmpl', e) ?>
            <?js
                }
                else {
            ?>
                <?js= self.partial('members.tmpl', e) ?>
            <?js
                }
            }); ?>
    <?js } ?>

    <?js
        var events = self.find({kind: 'event', memberof: isGlobalPage ? {isUndefined: true} : doc.longname});
        if (events && events.length && events.forEach) {
    ?>
        <h3 class="subsection-title">Events</h3>

        <?js events.forEach(function(e) { ?>
            <?js= self.partial('method.tmpl', e) ?>
        <?js }); ?>
    <?js } ?>
</article>

</section>
<?js } ?>

<?js }); ?>
