// 快速修复AI验证系统问题
// 解决API连接和术语词典问题

const quickFix = {
  
  // 一键修复所有问题
  async fixAllIssues() {
    console.log('🔧 开始一键修复AI验证系统问题...');
    
    const results = {
      terminology_fix: await this.fixTerminologyDict(),
      api_test: await this.testAPIViaCloudFunction(),
      knowledge_test: await this.testKnowledgeBase()
    };
    
    console.log('🎯 修复结果汇总:', results);
    
    const allSuccess = Object.values(results).every(result => result.success);
    
    return {
      success: allSuccess,
      results: results,
      summary: {
        terminology_status: results.terminology_fix.success ? '已修复' : '修复失败',
        api_status: results.api_test.success ? '连接正常' : '连接失败',
        knowledge_status: results.knowledge_test.success ? `${results.knowledge_test.count}条记录` : '查询失败',
        overall_status: allSuccess ? '系统就绪' : '存在问题'
      }
    };
  },

  // 修复术语词典问题
  async fixTerminologyDict() {
    console.log('📖 修复术语词典...');
    
    try {
      // 直接调用创建术语词典云函数
      const result = await wx.cloud.callFunction({
        name: 'create-terminology'
      });
      
      if (result.result.success) {
        console.log('✅ 术语词典修复成功:', result.result.total_terms, '个术语');
        return {
          success: true,
          total_terms: result.result.total_terms,
          categories: result.result.categories
        };
      } else {
        console.log('❌ 术语词典修复失败:', result.result.error);
        return { success: false, error: result.result.error };
      }
      
    } catch (error) {
      console.error('❌ 术语词典修复异常:', error);
      return { success: false, error: error.message };
    }
  },

  // 通过云函数测试API
  async testAPIViaCloudFunction() {
    console.log('🔍 通过云函数测试API连接...');
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'ai-verifier',
        data: {
          action: 'test_api'
        }
      });
      
      if (result.result.success) {
        console.log('✅ API连接成功:', result.result.reply);
        return {
          success: true,
          reply: result.result.reply,
          usage: result.result.usage
        };
      } else {
        console.log('❌ API连接失败:', result.result.error);
        return { success: false, error: result.result.error };
      }
      
    } catch (error) {
      console.error('❌ API测试异常:', error);
      return { success: false, error: error.message };
    }
  },

  // 测试知识库
  async testKnowledgeBase() {
    console.log('📚 测试知识库...');
    
    try {
      const db = wx.cloud.database();
      
      const knowledgeCount = await db.collection('knowledge_base').count();
      console.log(`📊 知识库记录数: ${knowledgeCount.total}`);
      
      if (knowledgeCount.total > 0) {
        const sampleRecords = await db.collection('knowledge_base')
          .field({ title: true, content: true, category: true })
          .limit(2)
          .get();
        
        console.log('📖 样本记录:', sampleRecords.data);
        
        return {
          success: true,
          count: knowledgeCount.total,
          samples: sampleRecords.data
        };
      } else {
        return { success: false, error: '知识库为空' };
      }
      
    } catch (error) {
      console.error('❌ 知识库测试失败:', error);
      return { success: false, error: error.message };
    }
  },

  // 完整的AI分析验证测试
  async testCompleteAIVerification() {
    console.log('🤖 测试完整AI分析验证...');
    
    try {
      // 先确保基础组件正常
      const baseTest = await this.fixAllIssues();
      if (!baseTest.success) {
        throw new Error('基础组件测试失败');
      }
      
      // 测试AI分析验证
      const testData = {
        action: 'verify_analysis',
        analysis_type: 'liuyao',
        user_question: '我的投资什么时候能盈利？',
        divination_result: {
          hexagram: '天雷无妄',
          changing_lines: [3],
          analysis: '妻财爻持世，投资有利'
        },
        analysis_content: '根据卦象显示，您的投资在3个月内会有收益，建议在春季行动。'
      };
      
      const verifyResult = await wx.cloud.callFunction({
        name: 'ai-verifier',
        data: testData
      });
      
      if (verifyResult.result.success) {
        console.log('✅ AI分析验证成功:', verifyResult.result);
        return {
          success: true,
          verification: verifyResult.result,
          base_test: baseTest
        };
      } else {
        console.log('❌ AI分析验证失败:', verifyResult.result.error);
        return { 
          success: false, 
          error: verifyResult.result.error,
          base_test: baseTest
        };
      }
      
    } catch (error) {
      console.error('❌ 完整AI验证测试失败:', error);
      return { success: false, error: error.message };
    }
  },

  // 显示修复状态
  showFixStatus(results) {
    const status = results.summary;
    
    let message = `AI验证系统修复状态：\n\n`;
    message += `📖 术语词典: ${status.terminology_status}\n`;
    message += `🔗 API连接: ${status.api_status}\n`;
    message += `📚 知识库: ${status.knowledge_status}\n`;
    message += `🎯 整体状态: ${status.overall_status}\n\n`;
    
    if (results.success) {
      message += `✅ 系统已就绪，可以正常使用！`;
    } else {
      message += `⚠️ 系统存在问题，请检查日志。`;
    }
    
    return message;
  }
};

// 导出快速修复对象
module.exports = quickFix;
