{"version": 3, "sources": ["lib/prelude.js", "../node_modules/@protobufjs/aspromise/index.js", "../node_modules/@protobufjs/base64/index.js", "../node_modules/@protobufjs/codegen/index.js", "../node_modules/@protobufjs/eventemitter/index.js", "../node_modules/@protobufjs/fetch/index.js", "../node_modules/@protobufjs/float/index.js", "../node_modules/@protobufjs/inquire/index.js", "../node_modules/@protobufjs/path/index.js", "../node_modules/@protobufjs/pool/index.js", "../node_modules/@protobufjs/utf8/index.js", "../src/converter.js", "../src/decoder.js", "../src/encoder.js", "../src/enum.js", "../src/field.js", "../src/index-light", "../src/index-minimal.js", "../src/mapfield.js", "../src/message.js", "../src/method.js", "../src/namespace.js", "../src/object.js", "../src/oneof.js", "../src/reader.js", "../src/reader_buffer.js", "../src/root.js", "../src/roots.js", "../src/rpc.js", "../src/rpc/service.js", "../src/service.js", "../src/type.js", "../src/types.js", "../src/util.js", "../src/util/longbits.js", "../src/util/minimal.js", "../src/verifier.js", "../src/wrappers.js", "../src/writer.js", "../src/writer_buffer.js"], "names": ["undefined", "modules", "cache", "entries", "protobuf", "$require", "name", "$module", "call", "exports", "util", "global", "define", "amd", "<PERSON>", "isLong", "configure", "module", "1", "require", "fn", "ctx", "params", "Array", "arguments", "length", "offset", "index", "pending", "Promise", "resolve", "reject", "err", "apply", "base64", "string", "p", "n", "Math", "ceil", "b64", "s64", "i", "encode", "buffer", "start", "end", "t", "parts", "chunk", "j", "b", "push", "String", "fromCharCode", "slice", "join", "invalidEncoding", "decode", "c", "charCodeAt", "Error", "test", "codegen", "functionParams", "functionName", "body", "Codegen", "formatStringOrScope", "source", "toString", "verbose", "console", "log", "scopeKeys", "Object", "keys", "scopeParams", "scopeValues", "scopeOffset", "Function", "formatParams", "formatOffset", "replace", "$0", "$1", "value", "Number", "floor", "JSON", "stringify", "functionNameOverride", "EventEmitter", "this", "_listeners", "prototype", "on", "evt", "off", "listeners", "splice", "emit", "args", "fetch", "<PERSON><PERSON><PERSON><PERSON>", "fs", "inquire", "filename", "options", "callback", "xhr", "readFile", "contents", "XMLHttpRequest", "binary", "onreadystatechange", "readyState", "status", "response", "responseText", "Uint8Array", "overrideMimeType", "responseType", "open", "send", "factory", "writeFloat_ieee754", "writeUint", "val", "buf", "pos", "sign", "isNaN", "round", "exponent", "LN2", "pow", "readFloat_ieee754", "readUint", "uint", "mantissa", "NaN", "Infinity", "writeFloat_f32_cpy", "f32", "f8b", "writeFloat_f32_rev", "readFloat_f32_cpy", "readFloat_f32_rev", "f64", "le", "writeDouble_ieee754", "off0", "off1", "readDouble_ieee754", "lo", "hi", "writeDouble_f64_cpy", "writeDouble_f64_rev", "readDouble_f64_cpy", "readDouble_f64_rev", "Float32Array", "writeFloatLE", "writeFloatBE", "readFloatLE", "readFloatBE", "bind", "writeUintLE", "writeUintBE", "readUintLE", "readUintBE", "Float64Array", "writeDoubleLE", "writeDoubleBE", "readDoubleLE", "readDoubleBE", "moduleName", "mod", "eval", "e", "isAbsolute", "path", "normalize", "split", "absolute", "prefix", "shift", "originPath", "include<PERSON>ath", "alreadyNormalized", "alloc", "size", "SIZE", "MAX", "slab", "utf8", "len", "read", "write", "c1", "c2", "Enum", "genValuePartial_fromObject", "gen", "field", "fieldIndex", "prop", "resolvedType", "values", "repeated", "typeDefault", "fullName", "isUnsigned", "type", "genValuePartial_toObject", "converter", "fromObject", "mtype", "fields", "fieldsArray", "safeProp", "map", "toObject", "sort", "compareFieldsById", "repeatedFields", "mapFields", "normalFields", "partOf", "arrayDefault", "valuesById", "long", "low", "high", "unsigned", "toNumber", "bytes", "hasKs2", "_fieldsArray", "indexOf", "filter", "group", "ref", "id", "types", "defaults", "keyType", "basic", "packed", "rfield", "required", "wireType", "mapKey", "genTypePartial", "optional", "ReflectionObject", "Namespace", "create", "constructor", "className", "comment", "comments", "TypeError", "reserved", "fromJSON", "json", "enm", "toJSON", "toJSONOptions", "keepComments", "add", "isString", "isInteger", "isReservedId", "isReservedName", "allow_alias", "remove", "Field", "Type", "ruleRe", "rule", "extend", "isObject", "toLowerCase", "message", "defaultValue", "extensionField", "declaringField", "_packed", "defineProperty", "get", "getOption", "setOption", "ifNotSet", "resolved", "parent", "lookupTypeOrEnum", "fromNumber", "freeze", "new<PERSON>uffer", "emptyObject", "emptyArray", "ctor", "d", "fieldId", "fieldType", "fieldRule", "decorateType", "decorateEnum", "fieldName", "default", "_configure", "Type_", "build", "load", "root", "Root", "loadSync", "encoder", "decoder", "verifier", "OneOf", "MapField", "Service", "Method", "Message", "wrappers", "Writer", "BufferWriter", "Reader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rpc", "roots", "resolvedKeyType", "fieldKeyType", "fieldValueType", "properties", "$type", "writer", "encodeDelimited", "reader", "decodeDelimited", "verify", "object", "requestType", "requestStream", "responseStream", "parsedOptions", "resolvedRequestType", "resolvedResponseType", "lookupType", "arrayToJSON", "array", "obj", "nested", "_nested<PERSON><PERSON>y", "clearCache", "namespace", "addJSON", "toArray", "nested<PERSON><PERSON><PERSON>", "nested<PERSON><PERSON>", "names", "methods", "getEnum", "prev", "setOptions", "onAdd", "onRemove", "isArray", "ptr", "part", "resolveAll", "lookup", "filterTypes", "parentAlreadyChecked", "found", "lookupEnum", "lookupService", "Service_", "Enum_", "defineProperties", "unshift", "_handleAdd", "_handleRemove", "setParsedOption", "propName", "opt", "newOpt", "find", "hasOwnProperty", "newValue", "setProperty", "Root_", "fieldNames", "oneof", "addFieldsToParent", "oneofName", "oneOfGetter", "set", "oneOfSetter", "LongBits", "indexOutOfRange", "write<PERSON><PERSON>th", "RangeError", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "create_array", "readLongVarint", "bits", "readFixed32_end", "readFixed64", "_slice", "subarray", "uint32", "int32", "sint32", "bool", "fixed32", "sfixed32", "float", "double", "skip", "skipType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_", "merge", "int64", "uint64", "sint64", "zzDecode", "fixed64", "sfixed64", "utf8Slice", "min", "parse", "common", "deferred", "files", "SYNC", "<PERSON><PERSON><PERSON>", "self", "sync", "finish", "cb", "getBundledFileName", "idx", "lastIndexOf", "altname", "substring", "process", "parsed", "imports", "weakImports", "queued", "weak", "setTimeout", "readFileSync", "isNode", "exposeRe", "tryHandleExtension", "sisterField", "extendedType", "parse_", "common_", "rpcImpl", "requestDelimited", "responseDelimited", "rpcCall", "method", "requestCtor", "responseCtor", "request", "endedByRPC", "_methodsArray", "service", "inherited", "methodsArray", "rpcService", "methodName", "lcFirst", "isReserved", "m", "q", "s", "oneofs", "extensions", "_fieldsById", "_oneofsArray", "_ctor", "fieldsById", "oneofsArray", "generateConstructor", "ctorProperties", "setup", "originalThis", "wrapper", "fork", "l<PERSON>im", "typeName", "target", "bake", "o", "safePropBackslashRe", "key", "safePropQuoteRe", "camelCaseRe", "ucFirst", "str", "toUpperCase", "decorateEnumIndex", "camelCase", "a", "decorateRoot", "enumerable", "dst", "setProp", "prevValue", "concat", "zero", "zzEncode", "zeroHash", "from", "parseInt", "fromString", "toLong", "fromHash", "hash", "toHash", "mask", "part0", "part1", "part2", "src", "newError", "CustomError", "captureStackTrace", "stack", "pool", "versions", "node", "window", "isFinite", "isset", "isSet", "utf8Write", "_B<PERSON>er_from", "_Buffer_allocUnsafe", "sizeOrArray", "dcodeIO", "key2Re", "key32Re", "key64Re", "longToHash", "longFromHash", "fromBits", "ProtocolError", "fieldMap", "longs", "enums", "encoding", "allocUnsafe", "seenFirstField", "oneofProp", "invalid", "genVerifyKey", "genVerifyValue", "expected", "type_url", "substr", "messageName", "Op", "next", "noop", "State", "head", "tail", "states", "writeByte", "VarintOp", "writeVarint64", "writeFixed32", "_push", "writeBytes", "reset", "BufferWriter_", "writeStringBuffer", "writeBytesBuffer", "copy", "byteLength"], "mappings": ";;;;;;CAAA,SAAAA,iBAAA,SAAAC,EAAAC,EAAAC,GAcA,IAAAC,EAPA,SAAAC,EAAAC,GACA,IAAAC,EAAAL,EAAAI,GAGA,OAFAC,GACAN,EAAAK,GAAA,GAAAE,KAAAD,EAAAL,EAAAI,GAAA,CAAAG,QAAA,IAAAJ,EAAAE,EAAAA,EAAAE,SACAF,EAAAE,QAGAJ,CAAAF,EAAA,IAGAC,EAAAM,KAAAC,OAAAP,SAAAA,EAGA,mBAAAQ,QAAAA,OAAAC,KACAD,OAAA,CAAA,QAAA,SAAAE,GAKA,OAJAA,GAAAA,EAAAC,SACAX,EAAAM,KAAAI,KAAAA,EACAV,EAAAY,aAEAZ,IAIA,iBAAAa,QAAAA,QAAAA,OAAAR,UACAQ,OAAAR,QAAAL,GA/BA,CAiCA,CAAAc,EAAA,CAAA,SAAAC,EAAAF,EAAAR,GChCAQ,EAAAR,QAmBA,SAAAW,EAAAC,GACA,IAAAC,EAAAC,MAAAC,UAAAC,OAAA,GACAC,EAAA,EACAC,EAAA,EACAC,GAAA,EACA,KAAAD,EAAAH,UAAAC,QACAH,EAAAI,KAAAF,UAAAG,KACA,OAAA,IAAAE,QAAA,SAAAC,EAAAC,GACAT,EAAAI,GAAA,SAAAM,GACA,GAAAJ,EAEA,GADAA,GAAA,EACAI,EACAD,EAAAC,OACA,CAGA,IAFA,IAAAV,EAAAC,MAAAC,UAAAC,OAAA,GACAC,EAAA,EACAA,EAAAJ,EAAAG,QACAH,EAAAI,KAAAF,UAAAE,GACAI,EAAAG,MAAA,KAAAX,KAIA,IACAF,EAAAa,MAAAZ,GAAA,KAAAC,GACA,MAAAU,GACAJ,IACAA,GAAA,EACAG,EAAAC,S,uBCjCAE,EAAAT,OAAA,SAAAU,GACA,IAAAC,EAAAD,EAAAV,OACA,IAAAW,EACA,OAAA,EAEA,IADA,IAAAC,EAAA,EACA,IAAAD,EAAA,GAAA,MAAAD,EAAAA,EAAAC,IAAAD,OACAE,EACA,OAAAC,KAAAC,KAAA,EAAAJ,EAAAV,QAAA,EAAAY,GAUA,IAxBA,IAkBAG,EAAAjB,MAAA,IAGAkB,EAAAlB,MAAA,KAGAmB,EAAA,EAAAA,EAAA,IACAD,EAAAD,EAAAE,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,EAAAA,EAAA,GAAA,IAAAA,IASAR,EAAAS,OAAA,SAAAC,EAAAC,EAAAC,GAMA,IALA,IAIAC,EAJAC,EAAA,KACAC,EAAA,GACAP,EAAA,EACAQ,EAAA,EAEAL,EAAAC,GAAA,CACA,IAAAK,EAAAP,EAAAC,KACA,OAAAK,GACA,KAAA,EACAD,EAAAP,KAAAF,EAAAW,GAAA,GACAJ,GAAA,EAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,KAAAF,EAAAO,EAAAI,GAAA,GACAJ,GAAA,GAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,KAAAF,EAAAO,EAAAI,GAAA,GACAF,EAAAP,KAAAF,EAAA,GAAAW,GACAD,EAAA,EAGA,KAAAR,KACAM,EAAAA,GAAA,IAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,IACAP,EAAA,GASA,OANAQ,IACAD,EAAAP,KAAAF,EAAAO,GACAE,EAAAP,KAAA,GACA,IAAAQ,IACAD,EAAAP,KAAA,KAEAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,KACAM,EAAAQ,KAAA,KAEAH,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,KAGA,IAAAe,EAAA,mBAUAvB,EAAAwB,OAAA,SAAAvB,EAAAS,EAAAlB,GAIA,IAHA,IAEAqB,EAFAF,EAAAnB,EACAwB,EAAA,EAEAR,EAAA,EAAAA,EAAAP,EAAAV,QAAA,CACA,IAAAkC,EAAAxB,EAAAyB,WAAAlB,KACA,GAAA,IAAAiB,GAAA,EAAAT,EACA,MACA,IAAAS,EAAAlB,EAAAkB,MAAA3D,EACA,MAAA6D,MAAAJ,GACA,OAAAP,GACA,KAAA,EACAH,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,KAAAqB,GAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,MAAA,GAAAqB,IAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,MAAA,EAAAqB,IAAA,EAAAY,EACAT,EAAA,GAIA,GAAA,IAAAA,EACA,MAAAW,MAAAJ,GACA,OAAA/B,EAAAmB,GAQAX,EAAA4B,KAAA,SAAA3B,GACA,MAAA,mEAAA2B,KAAA3B,K,uBC/HA,SAAA4B,EAAAC,EAAAC,GAGA,iBAAAD,IACAC,EAAAD,EACAA,EAAAhE,GAGA,IAAAkE,EAAA,GAYA,SAAAC,EAAAC,GAIA,GAAA,iBAAAA,EAAA,CACA,IAAAC,EAAAC,IAIA,GAHAP,EAAAQ,SACAC,QAAAC,IAAA,YAAAJ,GACAA,EAAA,UAAAA,EACAD,EAAA,CAKA,IAJA,IAAAM,EAAAC,OAAAC,KAAAR,GACAS,EAAAtD,MAAAmD,EAAAjD,OAAA,GACAqD,EAAAvD,MAAAmD,EAAAjD,QACAsD,EAAA,EACAA,EAAAL,EAAAjD,QACAoD,EAAAE,GAAAL,EAAAK,GACAD,EAAAC,GAAAX,EAAAM,EAAAK,MAGA,OADAF,EAAAE,GAAAV,EACAW,SAAA/C,MAAA,KAAA4C,GAAA5C,MAAA,KAAA6C,GAEA,OAAAE,SAAAX,EAAAW,GAMA,IAFA,IAAAC,EAAA1D,MAAAC,UAAAC,OAAA,GACAyD,EAAA,EACAA,EAAAD,EAAAxD,QACAwD,EAAAC,GAAA1D,YAAA0D,GAYA,GAXAA,EAAA,EACAd,EAAAA,EAAAe,QAAA,eAAA,SAAAC,EAAAC,GACA,IAAAC,EAAAL,EAAAC,KACA,OAAAG,GACA,IAAA,IAAA,IAAA,IAAA,MAAAhC,MAAAkC,GAAAD,GACA,IAAA,IAAA,MAAAjC,GAAAf,KAAAkD,MAAAF,GACA,IAAA,IAAA,OAAAG,KAAAC,UAAAJ,GACA,IAAA,IAAA,MAAAjC,GAAAiC,EAEA,MAAA,MAEAJ,IAAAD,EAAAxD,OACA,MAAAoC,MAAA,4BAEA,OADAK,EAAAd,KAAAgB,GACAD,EAGA,SAAAG,EAAAqB,GACA,MAAA,aAAAA,GAAA1B,GAAA,IAAA,KAAAD,GAAAA,EAAAR,KAAA,MAAA,IAAA,SAAAU,EAAAV,KAAA,QAAA,MAIA,OADAW,EAAAG,SAAAA,EACAH,GAhFAlD,EAAAR,QAAAsD,GAiGAQ,SAAA,G,uBCzFA,SAAAqB,IAOAC,KAAAC,EAAA,IAfA7E,EAAAR,QAAAmF,GAyBAG,UAAAC,GAAA,SAAAC,EAAA7E,EAAAC,GAKA,OAJAwE,KAAAC,EAAAG,KAAAJ,KAAAC,EAAAG,GAAA,KAAA7C,KAAA,CACAhC,GAAAA,EACAC,IAAAA,GAAAwE,OAEAA,MASAD,EAAAG,UAAAG,IAAA,SAAAD,EAAA7E,GACA,GAAA6E,IAAAjG,EACA6F,KAAAC,EAAA,QAEA,GAAA1E,IAAApB,EACA6F,KAAAC,EAAAG,GAAA,QAGA,IADA,IAAAE,EAAAN,KAAAC,EAAAG,GACAvD,EAAA,EAAAA,EAAAyD,EAAA1E,QACA0E,EAAAzD,GAAAtB,KAAAA,EACA+E,EAAAC,OAAA1D,EAAA,KAEAA,EAGA,OAAAmD,MASAD,EAAAG,UAAAM,KAAA,SAAAJ,GACA,IAAAE,EAAAN,KAAAC,EAAAG,GACA,GAAAE,EAAA,CAGA,IAFA,IAAAG,EAAA,GACA5D,EAAA,EACAA,EAAAlB,UAAAC,QACA6E,EAAAlD,KAAA5B,UAAAkB,MACA,IAAAA,EAAA,EAAAA,EAAAyD,EAAA1E,QACA0E,EAAAzD,GAAAtB,GAAAa,MAAAkE,EAAAzD,KAAArB,IAAAiF,GAEA,OAAAT,O,uBCzEA5E,EAAAR,QAAA8F,EAEA,IAAAC,EAAArF,EAAA,GAGAsF,EAFAtF,EAAA,EAEAuF,CAAA,MA2BA,SAAAH,EAAAI,EAAAC,EAAAC,GAOA,OAJAD,EAFA,mBAAAA,GACAC,EAAAD,EACA,IACAA,GACA,GAEAC,GAIAD,EAAAE,KAAAL,GAAAA,EAAAM,SACAN,EAAAM,SAAAJ,EAAA,SAAA3E,EAAAgF,GACA,OAAAhF,GAAA,oBAAAiF,eACAV,EAAAO,IAAAH,EAAAC,EAAAC,GACA7E,EACA6E,EAAA7E,GACA6E,EAAA,KAAAD,EAAAM,OAAAF,EAAAA,EAAA1C,SAAA,WAIAiC,EAAAO,IAAAH,EAAAC,EAAAC,GAbAL,EAAAD,EAAAV,KAAAc,EAAAC,GAqCAL,EAAAO,IAAA,SAAAH,EAAAC,EAAAC,GACA,IAAAC,EAAA,IAAAG,eACAH,EAAAK,mBAAA,WAEA,GAAA,IAAAL,EAAAM,WACA,OAAApH,EAKA,GAAA,IAAA8G,EAAAO,QAAA,MAAAP,EAAAO,OACA,OAAAR,EAAAhD,MAAA,UAAAiD,EAAAO,SAIA,GAAAT,EAAAM,OAAA,CAEA,KAAAtE,EADAkE,EAAAQ,UAGA,IAAA,IADA1E,EAAA,GACAF,EAAA,EAAAA,EAAAoE,EAAAS,aAAA9F,SAAAiB,EACAE,EAAAQ,KAAA,IAAA0D,EAAAS,aAAA3D,WAAAlB,IAEA,OAAAmE,EAAA,KAAA,oBAAAW,WAAA,IAAAA,WAAA5E,GAAAA,GAEA,OAAAiE,EAAA,KAAAC,EAAAS,eAGAX,EAAAM,SAEA,qBAAAJ,GACAA,EAAAW,iBAAA,sCACAX,EAAAY,aAAA,eAGAZ,EAAAa,KAAA,MAAAhB,GACAG,EAAAc,S,8BC1BA,SAAAC,EAAApH,GAsDA,SAAAqH,EAAAC,EAAAC,EAAAC,EAAAC,GACA,IAAAC,EAAAH,EAAA,EAAA,EAAA,EAIAD,EADA,KADAC,EADAG,GACAH,EACAA,GACA,EAAA,EAAAA,EAAA,EAAA,WACAI,MAAAJ,GACA,WACA,qBAAAA,GACAG,GAAA,GAAA,cAAA,EACAH,EAAA,uBACAG,GAAA,GAAA7F,KAAA+F,MAAAL,EAAA,yBAAA,GAIAG,GAAA,GAAA,KAFAG,EAAAhG,KAAAkD,MAAAlD,KAAAmC,IAAAuD,GAAA1F,KAAAiG,OAEA,GADA,QAAAjG,KAAA+F,MAAAL,EAAA1F,KAAAkG,IAAA,GAAAF,GAAA,YACA,EAVAL,EAAAC,GAiBA,SAAAO,EAAAC,EAAAT,EAAAC,GACAS,EAAAD,EAAAT,EAAAC,GACAC,EAAA,GAAAQ,GAAA,IAAA,EACAL,EAAAK,IAAA,GAAA,IACAC,GAAA,QACA,OAAA,KAAAN,EACAM,EACAC,IACAC,EAAAA,EAAAX,EACA,GAAAG,EACA,qBAAAH,EAAAS,EACAT,EAAA7F,KAAAkG,IAAA,EAAAF,EAAA,MAAA,QAAAM,GA9EA,SAAAG,EAAAf,EAAAC,EAAAC,GACAc,EAAA,GAAAhB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GAGA,SAAAC,EAAAlB,EAAAC,EAAAC,GACAc,EAAA,GAAAhB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GAQA,SAAAE,EAAAlB,EAAAC,GAKA,OAJAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAc,EAAA,GAGA,SAAAI,EAAAnB,EAAAC,GAKA,OAJAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAc,EAAA,GAxCA,IAEAA,EACAC,EA4FAI,EACAJ,EACAK,EA+DA,SAAAC,EAAAxB,EAAAyB,EAAAC,EAAAzB,EAAAC,EAAAC,GACA,IAaAU,EAbAT,EAAAH,EAAA,EAAA,EAAA,EAGA,KADAA,EADAG,GACAH,EACAA,IACAD,EAAA,EAAAE,EAAAC,EAAAsB,GACAzB,EAAA,EAAA,EAAAC,EAAA,EAAA,WAAAC,EAAAC,EAAAuB,IACArB,MAAAJ,IACAD,EAAA,EAAAE,EAAAC,EAAAsB,GACAzB,EAAA,WAAAE,EAAAC,EAAAuB,IACA,sBAAAzB,GACAD,EAAA,EAAAE,EAAAC,EAAAsB,GACAzB,GAAAI,GAAA,GAAA,cAAA,EAAAF,EAAAC,EAAAuB,IAGAzB,EAAA,wBAEAD,GADAa,EAAAZ,EAAA,UACA,EAAAC,EAAAC,EAAAsB,GACAzB,GAAAI,GAAA,GAAAS,EAAA,cAAA,EAAAX,EAAAC,EAAAuB,KAMA1B,EAAA,kBADAa,EAAAZ,EAAA1F,KAAAkG,IAAA,IADAF,EADA,QADAA,EAAAhG,KAAAkD,MAAAlD,KAAAmC,IAAAuD,GAAA1F,KAAAiG,MAEA,KACAD,OACA,EAAAL,EAAAC,EAAAsB,GACAzB,GAAAI,GAAA,GAAAG,EAAA,MAAA,GAAA,QAAAM,EAAA,WAAA,EAAAX,EAAAC,EAAAuB,IAQA,SAAAC,EAAAhB,EAAAc,EAAAC,EAAAxB,EAAAC,GACAyB,EAAAjB,EAAAT,EAAAC,EAAAsB,GACAI,EAAAlB,EAAAT,EAAAC,EAAAuB,GACAtB,EAAA,GAAAyB,GAAA,IAAA,EACAtB,EAAAsB,IAAA,GAAA,KACAhB,EAAA,YAAA,QAAAgB,GAAAD,EACA,OAAA,MAAArB,EACAM,EACAC,IACAC,EAAAA,EAAAX,EACA,GAAAG,EACA,OAAAH,EAAAS,EACAT,EAAA7F,KAAAkG,IAAA,EAAAF,EAAA,OAAAM,EAAA,kBA1GA,SAAAiB,EAAA7B,EAAAC,EAAAC,GACAmB,EAAA,GAAArB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GAGA,SAAAa,EAAA9B,EAAAC,EAAAC,GACAmB,EAAA,GAAArB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GAQA,SAAAc,EAAA9B,EAAAC,GASA,OARAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAmB,EAAA,GAGA,SAAAW,EAAA/B,EAAAC,GASA,OARAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAmB,EAAA,GAgEA,MArNA,oBAAAY,cAEAjB,EAAA,IAAAiB,aAAA,EAAA,IACAhB,EAAA,IAAAzB,WAAAwB,EAAApG,QACA0G,EAAA,MAAAL,EAAA,GAmBAxI,EAAAyJ,aAAAZ,EAAAP,EAAAG,EAEAzI,EAAA0J,aAAAb,EAAAJ,EAAAH,EAmBAtI,EAAA2J,YAAAd,EAAAH,EAAAC,EAEA3I,EAAA4J,YAAAf,EAAAF,EAAAD,IAwBA1I,EAAAyJ,aAAApC,EAAAwC,KAAA,KAAAC,GACA9J,EAAA0J,aAAArC,EAAAwC,KAAA,KAAAE,GAgBA/J,EAAA2J,YAAA3B,EAAA6B,KAAA,KAAAG,GACAhK,EAAA4J,YAAA5B,EAAA6B,KAAA,KAAAI,IAKA,oBAAAC,cAEAtB,EAAA,IAAAsB,aAAA,EAAA,IACA1B,EAAA,IAAAzB,WAAA6B,EAAAzG,QACA0G,EAAA,MAAAL,EAAA,GA2BAxI,EAAAmK,cAAAtB,EAAAO,EAAAC,EAEArJ,EAAAoK,cAAAvB,EAAAQ,EAAAD,EA2BApJ,EAAAqK,aAAAxB,EAAAS,EAAAC,EAEAvJ,EAAAsK,aAAAzB,EAAAU,EAAAD,IAmCAtJ,EAAAmK,cAAArB,EAAAe,KAAA,KAAAC,EAAA,EAAA,GACA9J,EAAAoK,cAAAtB,EAAAe,KAAA,KAAAE,EAAA,EAAA,GAiBA/J,EAAAqK,aAAApB,EAAAY,KAAA,KAAAG,EAAA,EAAA,GACAhK,EAAAsK,aAAArB,EAAAY,KAAA,KAAAI,EAAA,EAAA,IAIAjK,EAKA,SAAA8J,EAAAvC,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAGA,SAAAwC,EAAAxC,EAAAC,EAAAC,GACAD,EAAAC,GAAAF,IAAA,GACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAA,IAAAF,EAGA,SAAAyC,EAAAxC,EAAAC,GACA,OAAAD,EAAAC,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,MAAA,EAGA,SAAAwC,EAAAzC,EAAAC,GACA,OAAAD,EAAAC,IAAA,GACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,MAAA,EA3UAjH,EAAAR,QAAAoH,EAAAA,I,uBCOA,SAAAnB,EAAAsE,GACA,IACA,IAAAC,EAAAC,KAAA,UAAAA,CAAAF,GACA,GAAAC,IAAAA,EAAAxJ,QAAAkD,OAAAC,KAAAqG,GAAAxJ,QACA,OAAAwJ,EACA,MAAAE,IACA,OAAA,KAdAlK,EAAAR,QAAAiG,G,uBCMA,IAEA0E,EAMAC,EAAAD,WAAA,SAAAC,GACA,MAAA,eAAAvH,KAAAuH,IAGAC,EAMAD,EAAAC,UAAA,SAAAD,GAGA,IAAArI,GAFAqI,EAAAA,EAAAlG,QAAA,MAAA,KACAA,QAAA,UAAA,MACAoG,MAAA,KACAC,EAAAJ,EAAAC,GACAI,EAAA,GACAD,IACAC,EAAAzI,EAAA0I,QAAA,KACA,IAAA,IAAAhJ,EAAA,EAAAA,EAAAM,EAAAvB,QACA,OAAAuB,EAAAN,GACA,EAAAA,GAAA,OAAAM,EAAAN,EAAA,GACAM,EAAAoD,SAAA1D,EAAA,GACA8I,EACAxI,EAAAoD,OAAA1D,EAAA,KAEAA,EACA,MAAAM,EAAAN,GACAM,EAAAoD,OAAA1D,EAAA,KAEAA,EAEA,OAAA+I,EAAAzI,EAAAQ,KAAA,MAUA6H,EAAAvJ,QAAA,SAAA6J,EAAAC,EAAAC,GAGA,OAFAA,IACAD,EAAAN,EAAAM,KACAR,EAAAQ,KAIAD,GADAA,EADAE,EAEAF,EADAL,EAAAK,IACAxG,QAAA,iBAAA,KAAA1D,OAAA6J,EAAAK,EAAA,IAAAC,GAHAA,I,uBC3DA3K,EAAAR,QA6BA,SAAAqL,EAAAvI,EAAAwI,GACA,IAAAC,EAAAD,GAAA,KACAE,EAAAD,IAAA,EACAE,EAAA,KACAxK,EAAAsK,EACA,OAAA,SAAAD,GACA,GAAAA,EAAA,GAAAE,EAAAF,EACA,OAAAD,EAAAC,GACAC,EAAAtK,EAAAqK,IACAG,EAAAJ,EAAAE,GACAtK,EAAA,GAEAuG,EAAA1E,EAAA/C,KAAA0L,EAAAxK,EAAAA,GAAAqK,GAGA,OAFA,EAAArK,IACAA,EAAA,GAAA,EAAAA,IACAuG,K,wBC/BAkE,EAAA1K,OAAA,SAAAU,GAGA,IAFA,IACAwB,EADAyI,EAAA,EAEA1J,EAAA,EAAAA,EAAAP,EAAAV,SAAAiB,GACAiB,EAAAxB,EAAAyB,WAAAlB,IACA,IACA0J,GAAA,EACAzI,EAAA,KACAyI,GAAA,EACA,QAAA,MAAAzI,IAAA,QAAA,MAAAxB,EAAAyB,WAAAlB,EAAA,OACAA,EACA0J,GAAA,GAEAA,GAAA,EAEA,OAAAA,GAUAD,EAAAE,KAAA,SAAAzJ,EAAAC,EAAAC,GAEA,GADAA,EAAAD,EACA,EACA,MAAA,GAKA,IAJA,IAGAE,EAHAC,EAAA,KACAC,EAAA,GACAP,EAAA,EAEAG,EAAAC,IACAC,EAAAH,EAAAC,MACA,IACAI,EAAAP,KAAAK,EACA,IAAAA,GAAAA,EAAA,IACAE,EAAAP,MAAA,GAAAK,IAAA,EAAA,GAAAH,EAAAC,KACA,IAAAE,GAAAA,EAAA,KACAA,IAAA,EAAAA,IAAA,IAAA,GAAAH,EAAAC,OAAA,IAAA,GAAAD,EAAAC,OAAA,EAAA,GAAAD,EAAAC,MAAA,MACAI,EAAAP,KAAA,OAAAK,GAAA,IACAE,EAAAP,KAAA,OAAA,KAAAK,IAEAE,EAAAP,MAAA,GAAAK,IAAA,IAAA,GAAAH,EAAAC,OAAA,EAAA,GAAAD,EAAAC,KACA,KAAAH,KACAM,EAAAA,GAAA,IAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,IACAP,EAAA,GAGA,OAAAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,KACAM,EAAAQ,KAAA,KAEAH,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,KAUAyJ,EAAAG,MAAA,SAAAnK,EAAAS,EAAAlB,GAIA,IAHA,IACA6K,EACAC,EAFA3J,EAAAnB,EAGAgB,EAAA,EAAAA,EAAAP,EAAAV,SAAAiB,GACA6J,EAAApK,EAAAyB,WAAAlB,IACA,IACAE,EAAAlB,KAAA6K,GACAA,EAAA,KACA3J,EAAAlB,KAAA6K,GAAA,EAAA,KAEA,QAAA,MAAAA,IAAA,QAAA,OAAAC,EAAArK,EAAAyB,WAAAlB,EAAA,QAEAA,EACAE,EAAAlB,MAFA6K,EAAA,QAAA,KAAAA,IAAA,KAAA,KAAAC,KAEA,GAAA,IACA5J,EAAAlB,KAAA6K,GAAA,GAAA,GAAA,KAIA3J,EAAAlB,KAAA6K,GAAA,GAAA,IAHA3J,EAAAlB,KAAA6K,GAAA,EAAA,GAAA,KANA3J,EAAAlB,KAAA,GAAA6K,EAAA,KAcA,OAAA7K,EAAAmB,I,wBClGA,IAEA4J,EAAAtL,EAAA,IACAT,EAAAS,EAAA,IAWA,SAAAuL,EAAAC,EAAAC,EAAAC,EAAAC,GAEA,GAAAF,EAAAG,aACA,GAAAH,EAAAG,wBAAAN,EAAA,CAAAE,EACA,eAAAG,GACA,IAAA,IAAAE,EAAAJ,EAAAG,aAAAC,OAAApI,EAAAD,OAAAC,KAAAoI,GAAAtK,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACAkK,EAAAK,UAAAD,EAAApI,EAAAlC,MAAAkK,EAAAM,aAAAP,EACA,YACAA,EACA,UAAA/H,EAAAlC,GADAiK,CAEA,WAAAK,EAAApI,EAAAlC,IAFAiK,CAGA,SAAAG,EAAAE,EAAApI,EAAAlC,IAHAiK,CAIA,SACAA,EACA,UACAA,EACA,4BAAAG,EADAH,CAEA,sBAAAC,EAAAO,SAAA,oBAFAR,CAGA,gCAAAG,EAAAD,EAAAC,OACA,CACA,IAAAM,GAAA,EACA,OAAAR,EAAAS,MACA,IAAA,SACA,IAAA,QAAAV,EACA,kBAAAG,EAAAA,GACA,MACA,IAAA,SACA,IAAA,UAAAH,EACA,cAAAG,EAAAA,GACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,WAAAH,EACA,YAAAG,EAAAA,GACA,MACA,IAAA,SACAM,GAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAT,EACA,gBADAA,CAEA,6CAAAG,EAAAA,EAAAM,EAFAT,CAGA,iCAAAG,EAHAH,CAIA,uBAAAG,EAAAA,EAJAH,CAKA,iCAAAG,EALAH,CAMA,UAAAG,EAAAA,EANAH,CAOA,iCAAAG,EAPAH,CAQA,+DAAAG,EAAAA,EAAAA,EAAAM,EAAA,OAAA,IACA,MACA,IAAA,QAAAT,EACA,4BAAAG,EADAH,CAEA,wEAAAG,EAAAA,EAAAA,EAFAH,CAGA,sBAAAG,EAHAH,CAIA,UAAAG,EAAAA,GACA,MACA,IAAA,SAAAH,EACA,kBAAAG,EAAAA,GACA,MACA,IAAA,OAAAH,EACA,mBAAAG,EAAAA,IAOA,OAAAH,EAmEA,SAAAW,EAAAX,EAAAC,EAAAC,EAAAC,GAEA,GAAAF,EAAAG,aACAH,EAAAG,wBAAAN,EAAAE,EACA,iDAAAG,EAAAD,EAAAC,EAAAA,GACAH,EACA,gCAAAG,EAAAD,EAAAC,OACA,CACA,IAAAM,GAAA,EACA,OAAAR,EAAAS,MACA,IAAA,SACA,IAAA,QAAAV,EACA,6CAAAG,EAAAA,EAAAA,EAAAA,GACA,MACA,IAAA,SACAM,GAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAT,EACA,4BAAAG,EADAH,CAEA,uCAAAG,EAAAA,EAAAA,EAFAH,CAGA,OAHAA,CAIA,4IAAAG,EAAAA,EAAAA,EAAAA,EAAAM,EAAA,OAAA,GAAAN,GACA,MACA,IAAA,QAAAH,EACA,gHAAAG,EAAAA,EAAAA,EAAAA,EAAAA,GACA,MACA,QAAAH,EACA,UAAAG,EAAAA,IAIA,OAAAH,EA5FAY,EAAAC,WAAA,SAAAC,GAEA,IAAAC,EAAAD,EAAAE,YACAhB,EAAAjM,EAAAqD,QAAA,CAAA,KAAA0J,EAAAnN,KAAA,cAAAI,CACA,6BADAA,CAEA,YACA,IAAAgN,EAAAjM,OAAA,OAAAkL,EACA,wBACAA,EACA,uBACA,IAAA,IAAAjK,EAAA,EAAAA,EAAAgL,EAAAjM,SAAAiB,EAAA,CACA,IAAAkK,EAAAc,EAAAhL,GAAAZ,UACAgL,EAAApM,EAAAkN,SAAAhB,EAAAtM,MAGAsM,EAAAiB,KAAAlB,EACA,WAAAG,EADAH,CAEA,4BAAAG,EAFAH,CAGA,sBAAAC,EAAAO,SAAA,oBAHAR,CAIA,SAAAG,EAJAH,CAKA,oDAAAG,GACAJ,EAAAC,EAAAC,EAAAlK,EAAAoK,EAAA,UAAAJ,CACA,IADAA,CAEA,MAGAE,EAAAK,UAAAN,EACA,WAAAG,EADAH,CAEA,0BAAAG,EAFAH,CAGA,sBAAAC,EAAAO,SAAA,mBAHAR,CAIA,SAAAG,EAJAH,CAKA,iCAAAG,GACAJ,EAAAC,EAAAC,EAAAlK,EAAAoK,EAAA,MAAAJ,CACA,IADAA,CAEA,OAIAE,EAAAG,wBAAAN,GAAAE,EACA,iBAAAG,GACAJ,EAAAC,EAAAC,EAAAlK,EAAAoK,GACAF,EAAAG,wBAAAN,GAAAE,EACA,MAEA,OAAAA,EACA,aAwDAY,EAAAO,SAAA,SAAAL,GAEA,IAAAC,EAAAD,EAAAE,YAAApK,QAAAwK,KAAArN,EAAAsN,mBACA,IAAAN,EAAAjM,OACA,OAAAf,EAAAqD,SAAArD,CAAA,aAUA,IATA,IAAAiM,EAAAjM,EAAAqD,QAAA,CAAA,IAAA,KAAA0J,EAAAnN,KAAA,YAAAI,CACA,SADAA,CAEA,OAFAA,CAGA,YAEAuN,EAAA,GACAC,EAAA,GACAC,EAAA,GACAzL,EAAA,EACAA,EAAAgL,EAAAjM,SAAAiB,EACAgL,EAAAhL,GAAA0L,SACAV,EAAAhL,GAAAZ,UAAAmL,SAAAgB,EACAP,EAAAhL,GAAAmL,IAAAK,EACAC,GAAA/K,KAAAsK,EAAAhL,IAEA,GAAAuL,EAAAxM,OAAA,CAEA,IAFAkL,EACA,6BACAjK,EAAA,EAAAA,EAAAuL,EAAAxM,SAAAiB,EAAAiK,EACA,SAAAjM,EAAAkN,SAAAK,EAAAvL,GAAApC,OACAqM,EACA,KAGA,GAAAuB,EAAAzM,OAAA,CAEA,IAFAkL,EACA,8BACAjK,EAAA,EAAAA,EAAAwL,EAAAzM,SAAAiB,EAAAiK,EACA,SAAAjM,EAAAkN,SAAAM,EAAAxL,GAAApC,OACAqM,EACA,KAGA,GAAAwB,EAAA1M,OAAA,CAEA,IAFAkL,EACA,mBACAjK,EAAA,EAAAA,EAAAyL,EAAA1M,SAAAiB,EAAA,CACA,IAWA2L,EAXAzB,EAAAuB,EAAAzL,GACAoK,EAAApM,EAAAkN,SAAAhB,EAAAtM,MACAsM,EAAAG,wBAAAN,EAAAE,EACA,6BAAAG,EAAAF,EAAAG,aAAAuB,WAAA1B,EAAAM,aAAAN,EAAAM,aACAN,EAAA2B,KAAA5B,EACA,iBADAA,CAEA,gCAAAC,EAAAM,YAAAsB,IAAA5B,EAAAM,YAAAuB,KAAA7B,EAAAM,YAAAwB,SAFA/B,CAGA,oEAAAG,EAHAH,CAIA,QAJAA,CAKA,6BAAAG,EAAAF,EAAAM,YAAA5I,WAAAsI,EAAAM,YAAAyB,YACA/B,EAAAgC,OACAP,EAAA,IAAA9M,MAAAwE,UAAAxC,MAAA/C,KAAAoM,EAAAM,aAAA1J,KAAA,KAAA,IACAmJ,EACA,6BAAAG,EAAAzJ,OAAAC,aAAArB,MAAAoB,OAAAuJ,EAAAM,aADAP,CAEA,QAFAA,CAGA,SAAAG,EAAAuB,EAHA1B,CAIA,6CAAAG,EAAAA,EAJAH,CAKA,MACAA,EACA,SAAAG,EAAAF,EAAAM,aACAP,EACA,KAGA,IADA,IAAAkC,GAAA,EACAnM,EAAA,EAAAA,EAAAgL,EAAAjM,SAAAiB,EAAA,CACA,IAAAkK,EAAAc,EAAAhL,GACAf,EAAA8L,EAAAqB,EAAAC,QAAAnC,GACAE,EAAApM,EAAAkN,SAAAhB,EAAAtM,MACAsM,EAAAiB,KACAgB,IAAAA,GAAA,EAAAlC,EACA,YACAA,EACA,0CAAAG,EAAAA,EADAH,CAEA,SAAAG,EAFAH,CAGA,kCACAW,EAAAX,EAAAC,EAAAjL,EAAAmL,EAAA,WAAAQ,CACA,MACAV,EAAAK,UAAAN,EACA,uBAAAG,EAAAA,EADAH,CAEA,SAAAG,EAFAH,CAGA,iCAAAG,GACAQ,EAAAX,EAAAC,EAAAjL,EAAAmL,EAAA,MAAAQ,CACA,OACAX,EACA,uCAAAG,EAAAF,EAAAtM,MACAgN,EAAAX,EAAAC,EAAAjL,EAAAmL,GACAF,EAAAwB,QAAAzB,EACA,eADAA,CAEA,SAAAjM,EAAAkN,SAAAhB,EAAAwB,OAAA9N,MAAAsM,EAAAtM,OAEAqM,EACA,KAEA,OAAAA,EACA,c,mCCjSA1L,EAAAR,QAeA,SAAAgN,GAEA,IAAAd,EAAAjM,EAAAqD,QAAA,CAAA,IAAA,KAAA0J,EAAAnN,KAAA,UAAAI,CACA,6BADAA,CAEA,qBAFAA,CAGA,qDAAA+M,EAAAE,YAAAqB,OAAA,SAAApC,GAAA,OAAAA,EAAAiB,MAAApM,OAAA,WAAA,IAHAf,CAIA,kBAJAA,CAKA,oBACA+M,EAAAwB,OAAAtC,EACA,gBADAA,CAEA,SACAA,EACA,kBAGA,IADA,IAAAjK,EAAA,EACAA,EAAA+K,EAAAE,YAAAlM,SAAAiB,EAAA,CACA,IAAAkK,EAAAa,EAAAqB,EAAApM,GAAAZ,UACAuL,EAAAT,EAAAG,wBAAAN,EAAA,QAAAG,EAAAS,KACA6B,EAAA,IAAAxO,EAAAkN,SAAAhB,EAAAtM,MAAAqM,EACA,WAAAC,EAAAuC,IAGAvC,EAAAiB,KAAAlB,EACA,4BAAAuC,EADAvC,CAEA,QAAAuC,EAFAvC,CAGA,6BAEAyC,EAAAC,SAAAzC,EAAA0C,WAAAtP,EAAA2M,EACA,OAAAyC,EAAAC,SAAAzC,EAAA0C,UACA3C,EACA,UAEAyC,EAAAC,SAAAhC,KAAArN,EAAA2M,EACA,WAAAyC,EAAAC,SAAAhC,IACAV,EACA,cAEAA,EACA,mBADAA,CAEA,sBAFAA,CAGA,oBAHAA,CAIA,0BAAAC,EAAA0C,QAJA3C,CAKA,WAEAyC,EAAAG,MAAAlC,KAAArN,EAAA2M,EACA,uCAAAjK,GACAiK,EACA,eAAAU,GAEAV,EACA,QADAA,CAEA,WAFAA,CAGA,qBAHAA,CAIA,QAJAA,CAKA,IALAA,CAMA,KAEAyC,EAAAb,KAAA3B,EAAA0C,WAAAtP,EAAA2M,EACA,qDAAAuC,GACAvC,EACA,cAAAuC,IAGAtC,EAAAK,UAAAN,EAEA,uBAAAuC,EAAAA,EAFAvC,CAGA,QAAAuC,GAGAE,EAAAI,OAAAnC,KAAArN,GAAA2M,EACA,iBADAA,CAEA,0BAFAA,CAGA,kBAHAA,CAIA,kBAAAuC,EAAA7B,EAJAV,CAKA,SAGAyC,EAAAG,MAAAlC,KAAArN,EAAA2M,EAAAC,EAAAG,aAAAkC,MACA,+BACA,0CAAAC,EAAAxM,GACAiK,EACA,kBAAAuC,EAAA7B,IAGA+B,EAAAG,MAAAlC,KAAArN,EAAA2M,EAAAC,EAAAG,aAAAkC,MACA,yBACA,oCAAAC,EAAAxM,GACAiK,EACA,YAAAuC,EAAA7B,GACAV,EACA,SAWA,IATAA,EACA,WADAA,CAEA,kBAFAA,CAGA,QAHAA,CAKA,IALAA,CAMA,KAGAjK,EAAA,EAAAA,EAAA+K,EAAAqB,EAAArN,SAAAiB,EAAA,CACA,IAAA+M,EAAAhC,EAAAqB,EAAApM,GACA+M,EAAAC,UAAA/C,EACA,4BAAA8C,EAAAnP,KADAqM,CAEA,4CAjHA,qBAiHA8C,EAjHAnP,KAAA,KAoHA,OAAAqM,EACA,aA1HA,IAAAF,EAAAtL,EAAA,IACAiO,EAAAjO,EAAA,IACAT,EAAAS,EAAA,K,yCCJAF,EAAAR,QA0BA,SAAAgN,GAWA,IATA,IAIAyB,EAJAvC,EAAAjM,EAAAqD,QAAA,CAAA,IAAA,KAAA0J,EAAAnN,KAAA,UAAAI,CACA,SADAA,CAEA,qBAKAgN,EAAAD,EAAAE,YAAApK,QAAAwK,KAAArN,EAAAsN,mBAEAtL,EAAA,EAAAA,EAAAgL,EAAAjM,SAAAiB,EAAA,CACA,IAAAkK,EAAAc,EAAAhL,GAAAZ,UACAH,EAAA8L,EAAAqB,EAAAC,QAAAnC,GACAS,EAAAT,EAAAG,wBAAAN,EAAA,QAAAG,EAAAS,KACAsC,EAAAP,EAAAG,MAAAlC,GACA6B,EAAA,IAAAxO,EAAAkN,SAAAhB,EAAAtM,MAGAsM,EAAAiB,KACAlB,EACA,kDAAAuC,EAAAtC,EAAAtM,KADAqM,CAEA,mDAAAuC,EAFAvC,CAGA,4CAAAC,EAAAuC,IAAA,EAAA,KAAA,EAAA,EAAAC,EAAAQ,OAAAhD,EAAA0C,SAAA1C,EAAA0C,SACAK,IAAA3P,EAAA2M,EACA,oEAAAhL,EAAAuN,GACAvC,EACA,qCAAA,GAAAgD,EAAAtC,EAAA6B,GACAvC,EACA,IADAA,CAEA,MAGAC,EAAAK,UAAAN,EACA,2BAAAuC,EAAAA,GAGAtC,EAAA4C,QAAAJ,EAAAI,OAAAnC,KAAArN,EAAA2M,EAEA,uBAAAC,EAAAuC,IAAA,EAAA,KAAA,EAFAxC,CAGA,+BAAAuC,EAHAvC,CAIA,cAAAU,EAAA6B,EAJAvC,CAKA,eAGAA,EAEA,+BAAAuC,GACAS,IAAA3P,EACA6P,EAAAlD,EAAAC,EAAAjL,EAAAuN,EAAA,OACAvC,EACA,0BAAAC,EAAAuC,IAAA,EAAAQ,KAAA,EAAAtC,EAAA6B,IAEAvC,EACA,OAIAC,EAAAkD,UAAAnD,EACA,iDAAAuC,EAAAtC,EAAAtM,MAEAqP,IAAA3P,EACA6P,EAAAlD,EAAAC,EAAAjL,EAAAuN,GACAvC,EACA,uBAAAC,EAAAuC,IAAA,EAAAQ,KAAA,EAAAtC,EAAA6B,IAKA,OAAAvC,EACA,aA9FA,IAAAF,EAAAtL,EAAA,IACAiO,EAAAjO,EAAA,IACAT,EAAAS,EAAA,IAWA,SAAA0O,EAAAlD,EAAAC,EAAAC,EAAAqC,GACAtC,EAAAG,aAAAkC,MACAtC,EAAA,+CAAAE,EAAAqC,GAAAtC,EAAAuC,IAAA,EAAA,KAAA,GAAAvC,EAAAuC,IAAA,EAAA,KAAA,GACAxC,EAAA,oDAAAE,EAAAqC,GAAAtC,EAAAuC,IAAA,EAAA,KAAA,K,yCClBAlO,EAAAR,QAAAgM,EAGA,IAAAsD,EAAA5O,EAAA,IAGA6O,KAFAvD,EAAA1G,UAAApB,OAAAsL,OAAAF,EAAAhK,YAAAmK,YAAAzD,GAAA0D,UAAA,OAEAhP,EAAA,KACAT,EAAAS,EAAA,IAaA,SAAAsL,EAAAnM,EAAA0M,EAAApG,EAAAwJ,EAAAC,GAGA,GAFAN,EAAAvP,KAAAqF,KAAAvF,EAAAsG,GAEAoG,GAAA,iBAAAA,EACA,MAAAsD,UAAA,4BAoCA,GA9BAzK,KAAAyI,WAAA,GAMAzI,KAAAmH,OAAArI,OAAAsL,OAAApK,KAAAyI,YAMAzI,KAAAuK,QAAAA,EAMAvK,KAAAwK,SAAAA,GAAA,GAMAxK,KAAA0K,SAAAvQ,EAMAgN,EACA,IAAA,IAAApI,EAAAD,OAAAC,KAAAoI,GAAAtK,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACA,iBAAAsK,EAAApI,EAAAlC,MACAmD,KAAAyI,WAAAzI,KAAAmH,OAAApI,EAAAlC,IAAAsK,EAAApI,EAAAlC,KAAAkC,EAAAlC,IAiBA+J,EAAA+D,SAAA,SAAAlQ,EAAAmQ,GACAC,EAAA,IAAAjE,EAAAnM,EAAAmQ,EAAAzD,OAAAyD,EAAA7J,QAAA6J,EAAAL,QAAAK,EAAAJ,UAEA,OADAK,EAAAH,SAAAE,EAAAF,SACAG,GAQAjE,EAAA1G,UAAA4K,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAAnQ,EAAAoN,SAAA,CACA,UAAAjI,KAAAe,QACA,SAAAf,KAAAmH,OACA,WAAAnH,KAAA0K,UAAA1K,KAAA0K,SAAA9O,OAAAoE,KAAA0K,SAAAvQ,EACA,UAAA6Q,EAAAhL,KAAAuK,QAAApQ,EACA,WAAA6Q,EAAAhL,KAAAwK,SAAArQ,KAaAyM,EAAA1G,UAAA+K,IAAA,SAAAxQ,EAAA6O,EAAAiB,GAGA,IAAA1P,EAAAqQ,SAAAzQ,GACA,MAAAgQ,UAAA,yBAEA,IAAA5P,EAAAsQ,UAAA7B,GACA,MAAAmB,UAAA,yBAEA,GAAAzK,KAAAmH,OAAA1M,KAAAN,EACA,MAAA6D,MAAA,mBAAAvD,EAAA,QAAAuF,MAEA,GAAAA,KAAAoL,aAAA9B,GACA,MAAAtL,MAAA,MAAAsL,EAAA,mBAAAtJ,MAEA,GAAAA,KAAAqL,eAAA5Q,GACA,MAAAuD,MAAA,SAAAvD,EAAA,oBAAAuF,MAEA,GAAAA,KAAAyI,WAAAa,KAAAnP,EAAA,CACA,IAAA6F,KAAAe,UAAAf,KAAAe,QAAAuK,YACA,MAAAtN,MAAA,gBAAAsL,EAAA,OAAAtJ,MACAA,KAAAmH,OAAA1M,GAAA6O,OAEAtJ,KAAAyI,WAAAzI,KAAAmH,OAAA1M,GAAA6O,GAAA7O,EAGA,OADAuF,KAAAwK,SAAA/P,GAAA8P,GAAA,KACAvK,MAUA4G,EAAA1G,UAAAqL,OAAA,SAAA9Q,GAEA,IAAAI,EAAAqQ,SAAAzQ,GACA,MAAAgQ,UAAA,yBAEA,IAAAtI,EAAAnC,KAAAmH,OAAA1M,GACA,GAAA,MAAA0H,EACA,MAAAnE,MAAA,SAAAvD,EAAA,uBAAAuF,MAMA,cAJAA,KAAAyI,WAAAtG,UACAnC,KAAAmH,OAAA1M,UACAuF,KAAAwK,SAAA/P,GAEAuF,MAQA4G,EAAA1G,UAAAkL,aAAA,SAAA9B,GACA,OAAAa,EAAAiB,aAAApL,KAAA0K,SAAApB,IAQA1C,EAAA1G,UAAAmL,eAAA,SAAA5Q,GACA,OAAA0P,EAAAkB,eAAArL,KAAA0K,SAAAjQ,K,yCClLAW,EAAAR,QAAA4Q,EAGA,IAOAC,EAPAvB,EAAA5O,EAAA,IAGAsL,KAFA4E,EAAAtL,UAAApB,OAAAsL,OAAAF,EAAAhK,YAAAmK,YAAAmB,GAAAlB,UAAA,QAEAhP,EAAA,KACAiO,EAAAjO,EAAA,IACAT,EAAAS,EAAA,IAIAoQ,EAAA,+BAyCA,SAAAF,EAAA/Q,EAAA6O,EAAA9B,EAAAmE,EAAAC,EAAA7K,EAAAwJ,GAcA,GAZA1P,EAAAgR,SAAAF,IACApB,EAAAqB,EACA7K,EAAA4K,EACAA,EAAAC,EAAAzR,GACAU,EAAAgR,SAAAD,KACArB,EAAAxJ,EACAA,EAAA6K,EACAA,EAAAzR,GAGA+P,EAAAvP,KAAAqF,KAAAvF,EAAAsG,IAEAlG,EAAAsQ,UAAA7B,IAAAA,EAAA,EACA,MAAAmB,UAAA,qCAEA,IAAA5P,EAAAqQ,SAAA1D,GACA,MAAAiD,UAAA,yBAEA,GAAAkB,IAAAxR,IAAAuR,EAAAzN,KAAA0N,EAAAA,EAAAlN,WAAAqN,eACA,MAAArB,UAAA,8BAEA,GAAAmB,IAAAzR,IAAAU,EAAAqQ,SAAAU,GACA,MAAAnB,UAAA,2BASAzK,KAAA2L,MANAA,EADA,oBAAAA,EACA,WAMAA,IAAA,aAAAA,EAAAA,EAAAxR,EAMA6F,KAAAwH,KAAAA,EAMAxH,KAAAsJ,GAAAA,EAMAtJ,KAAA4L,OAAAA,GAAAzR,EAMA6F,KAAA6J,SAAA,aAAA8B,EAMA3L,KAAAiK,UAAAjK,KAAA6J,SAMA7J,KAAAoH,SAAA,aAAAuE,EAMA3L,KAAAgI,KAAA,EAMAhI,KAAA+L,QAAA,KAMA/L,KAAAuI,OAAA,KAMAvI,KAAAqH,YAAA,KAMArH,KAAAgM,aAAA,KAMAhM,KAAA0I,OAAA7N,EAAAI,MAAAsO,EAAAb,KAAAlB,KAAArN,EAMA6F,KAAA+I,MAAA,UAAAvB,EAMAxH,KAAAkH,aAAA,KAMAlH,KAAAiM,eAAA,KAMAjM,KAAAkM,eAAA,KAOAlM,KAAAmM,EAAA,KAMAnM,KAAAuK,QAAAA,EAhKAiB,EAAAb,SAAA,SAAAlQ,EAAAmQ,GACA,OAAA,IAAAY,EAAA/Q,EAAAmQ,EAAAtB,GAAAsB,EAAApD,KAAAoD,EAAAe,KAAAf,EAAAgB,OAAAhB,EAAA7J,QAAA6J,EAAAL,UAwKAzL,OAAAsN,eAAAZ,EAAAtL,UAAA,SAAA,CACAmM,IAAA,WAIA,OAFA,OAAArM,KAAAmM,IACAnM,KAAAmM,GAAA,IAAAnM,KAAAsM,UAAA,WACAtM,KAAAmM,KAOAX,EAAAtL,UAAAqM,UAAA,SAAA9R,EAAAgF,EAAA+M,GAGA,MAFA,WAAA/R,IACAuF,KAAAmM,EAAA,MACAjC,EAAAhK,UAAAqM,UAAA5R,KAAAqF,KAAAvF,EAAAgF,EAAA+M,IAwBAhB,EAAAtL,UAAA4K,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAAnQ,EAAAoN,SAAA,CACA,OAAA,aAAAjI,KAAA2L,MAAA3L,KAAA2L,MAAAxR,EACA,OAAA6F,KAAAwH,KACA,KAAAxH,KAAAsJ,GACA,SAAAtJ,KAAA4L,OACA,UAAA5L,KAAAe,QACA,UAAAiK,EAAAhL,KAAAuK,QAAApQ,KASAqR,EAAAtL,UAAAjE,QAAA,WAEA,OAAA+D,KAAAyM,SACAzM,OAEAA,KAAAqH,YAAAkC,EAAAC,SAAAxJ,KAAAwH,SAAArN,IACA6F,KAAAkH,cAAAlH,KAAAkM,gBAAAlM,MAAA0M,OAAAC,iBAAA3M,KAAAwH,MACAxH,KAAAkH,wBAAAuE,EACAzL,KAAAqH,YAAA,KAEArH,KAAAqH,YAAArH,KAAAkH,aAAAC,OAAArI,OAAAC,KAAAiB,KAAAkH,aAAAC,QAAA,KAIAnH,KAAAe,SAAA,MAAAf,KAAAe,QAAA,UACAf,KAAAqH,YAAArH,KAAAe,QAAA,QACAf,KAAAkH,wBAAAN,GAAA,iBAAA5G,KAAAqH,cACArH,KAAAqH,YAAArH,KAAAkH,aAAAC,OAAAnH,KAAAqH,eAIArH,KAAAe,WACA,IAAAf,KAAAe,QAAA4I,SAAA3J,KAAAe,QAAA4I,SAAAxP,IAAA6F,KAAAkH,cAAAlH,KAAAkH,wBAAAN,WACA5G,KAAAe,QAAA4I,OACA7K,OAAAC,KAAAiB,KAAAe,SAAAnF,SACAoE,KAAAe,QAAA5G,IAIA6F,KAAA0I,MACA1I,KAAAqH,YAAAxM,EAAAI,KAAA2R,WAAA5M,KAAAqH,YAAA,MAAArH,KAAAwH,KAAA,IAAAxH,KAGAlB,OAAA+N,QACA/N,OAAA+N,OAAA7M,KAAAqH,cAEArH,KAAA+I,OAAA,iBAAA/I,KAAAqH,cAEAxM,EAAAwB,OAAA4B,KAAA+B,KAAAqH,aACAxM,EAAAwB,OAAAwB,OAAAmC,KAAAqH,YAAAjF,EAAAvH,EAAAiS,UAAAjS,EAAAwB,OAAAT,OAAAoE,KAAAqH,cAAA,GAEAxM,EAAAyL,KAAAG,MAAAzG,KAAAqH,YAAAjF,EAAAvH,EAAAiS,UAAAjS,EAAAyL,KAAA1K,OAAAoE,KAAAqH,cAAA,GACArH,KAAAqH,YAAAjF,GAIApC,KAAAgI,IACAhI,KAAAgM,aAAAnR,EAAAkS,YACA/M,KAAAoH,SACApH,KAAAgM,aAAAnR,EAAAmS,WAEAhN,KAAAgM,aAAAhM,KAAAqH,YAGArH,KAAA0M,kBAAAjB,IACAzL,KAAA0M,OAAAO,KAAA/M,UAAAF,KAAAvF,MAAAuF,KAAAgM,cAEA9B,EAAAhK,UAAAjE,QAAAtB,KAAAqF,OA5BA,IAQAoC,GA2CAoJ,EAAA0B,EAAA,SAAAC,EAAAC,EAAAC,EAAArB,GAUA,MAPA,mBAAAoB,EACAA,EAAAvS,EAAAyS,aAAAF,GAAA3S,KAGA2S,GAAA,iBAAAA,IACAA,EAAAvS,EAAA0S,aAAAH,GAAA3S,MAEA,SAAAyF,EAAAsN,GACA3S,EAAAyS,aAAApN,EAAAmK,aACAY,IAAA,IAAAO,EAAAgC,EAAAL,EAAAC,EAAAC,EAAA,CAAAI,QAAAzB,OAkBAR,EAAAkC,EAAA,SAAAC,GACAlC,EAAAkC,I,+CCnXA,IAAApT,EAAAa,EAAAR,QAAAU,EAAA,IAEAf,EAAAqT,MAAA,QAoDArT,EAAAsT,KAjCA,SAAA/M,EAAAgN,EAAA9M,GAMA,OAHA8M,EAFA,mBAAAA,GACA9M,EAAA8M,EACA,IAAAvT,EAAAwT,MACAD,GACA,IAAAvT,EAAAwT,MACAF,KAAA/M,EAAAE,IA2CAzG,EAAAyT,SANA,SAAAlN,EAAAgN,GAGA,OADAA,EADAA,GACA,IAAAvT,EAAAwT,MACAC,SAAAlN,IAMAvG,EAAA0T,QAAA3S,EAAA,IACAf,EAAA2T,QAAA5S,EAAA,IACAf,EAAA4T,SAAA7S,EAAA,IACAf,EAAAmN,UAAApM,EAAA,IAGAf,EAAA2P,iBAAA5O,EAAA,IACAf,EAAA4P,UAAA7O,EAAA,IACAf,EAAAwT,KAAAzS,EAAA,IACAf,EAAAqM,KAAAtL,EAAA,IACAf,EAAAkR,KAAAnQ,EAAA,IACAf,EAAAiR,MAAAlQ,EAAA,IACAf,EAAA6T,MAAA9S,EAAA,IACAf,EAAA8T,SAAA/S,EAAA,IACAf,EAAA+T,QAAAhT,EAAA,IACAf,EAAAgU,OAAAjT,EAAA,IAGAf,EAAAiU,QAAAlT,EAAA,IACAf,EAAAkU,SAAAnT,EAAA,IAGAf,EAAAgP,MAAAjO,EAAA,IACAf,EAAAM,KAAAS,EAAA,IAGAf,EAAA2P,iBAAAwD,EAAAnT,EAAAwT,MACAxT,EAAA4P,UAAAuD,EAAAnT,EAAAkR,KAAAlR,EAAA+T,QAAA/T,EAAAqM,MACArM,EAAAwT,KAAAL,EAAAnT,EAAAkR,MACAlR,EAAAiR,MAAAkC,EAAAnT,EAAAkR,O,yICtGA,IAAAlR,EAAAK,EA2BA,SAAAO,IACAZ,EAAAM,KAAA6S,IACAnT,EAAAmU,OAAAhB,EAAAnT,EAAAoU,cACApU,EAAAqU,OAAAlB,EAAAnT,EAAAsU,cAtBAtU,EAAAqT,MAAA,UAGArT,EAAAmU,OAAApT,EAAA,IACAf,EAAAoU,aAAArT,EAAA,IACAf,EAAAqU,OAAAtT,EAAA,IACAf,EAAAsU,aAAAvT,EAAA,IAGAf,EAAAM,KAAAS,EAAA,IACAf,EAAAuU,IAAAxT,EAAA,IACAf,EAAAwU,MAAAzT,EAAA,IACAf,EAAAY,UAAAA,EAcAA,K,iEClCAC,EAAAR,QAAAyT,EAGA,IAAA7C,EAAAlQ,EAAA,IAGAiO,KAFA8E,EAAAnO,UAAApB,OAAAsL,OAAAoB,EAAAtL,YAAAmK,YAAAgE,GAAA/D,UAAA,WAEAhP,EAAA,KACAT,EAAAS,EAAA,IAcA,SAAA+S,EAAA5T,EAAA6O,EAAAG,EAAAjC,EAAAzG,EAAAwJ,GAIA,GAHAiB,EAAA7Q,KAAAqF,KAAAvF,EAAA6O,EAAA9B,EAAArN,EAAAA,EAAA4G,EAAAwJ,IAGA1P,EAAAqQ,SAAAzB,GACA,MAAAgB,UAAA,4BAMAzK,KAAAyJ,QAAAA,EAMAzJ,KAAAgP,gBAAA,KAGAhP,KAAAgI,KAAA,EAwBAqG,EAAA1D,SAAA,SAAAlQ,EAAAmQ,GACA,OAAA,IAAAyD,EAAA5T,EAAAmQ,EAAAtB,GAAAsB,EAAAnB,QAAAmB,EAAApD,KAAAoD,EAAA7J,QAAA6J,EAAAL,UAQA8D,EAAAnO,UAAA4K,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAAnQ,EAAAoN,SAAA,CACA,UAAAjI,KAAAyJ,QACA,OAAAzJ,KAAAwH,KACA,KAAAxH,KAAAsJ,GACA,SAAAtJ,KAAA4L,OACA,UAAA5L,KAAAe,QACA,UAAAiK,EAAAhL,KAAAuK,QAAApQ,KAOAkU,EAAAnO,UAAAjE,QAAA,WACA,GAAA+D,KAAAyM,SACA,OAAAzM,KAGA,GAAAuJ,EAAAQ,OAAA/J,KAAAyJ,WAAAtP,EACA,MAAA6D,MAAA,qBAAAgC,KAAAyJ,SAEA,OAAA+B,EAAAtL,UAAAjE,QAAAtB,KAAAqF,OAaAqO,EAAAnB,EAAA,SAAAC,EAAA8B,EAAAC,GAUA,MAPA,mBAAAA,EACAA,EAAArU,EAAAyS,aAAA4B,GAAAzU,KAGAyU,GAAA,iBAAAA,IACAA,EAAArU,EAAA0S,aAAA2B,GAAAzU,MAEA,SAAAyF,EAAAsN,GACA3S,EAAAyS,aAAApN,EAAAmK,aACAY,IAAA,IAAAoD,EAAAb,EAAAL,EAAA8B,EAAAC,O,yCC1HA9T,EAAAR,QAAA4T,EAEA,IAAA3T,EAAAS,EAAA,IASA,SAAAkT,EAAAW,GAEA,GAAAA,EACA,IAAA,IAAApQ,EAAAD,OAAAC,KAAAoQ,GAAAtS,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACAmD,KAAAjB,EAAAlC,IAAAsS,EAAApQ,EAAAlC,IA0BA2R,EAAApE,OAAA,SAAA+E,GACA,OAAAnP,KAAAoP,MAAAhF,OAAA+E,IAWAX,EAAA1R,OAAA,SAAAiP,EAAAsD,GACA,OAAArP,KAAAoP,MAAAtS,OAAAiP,EAAAsD,IAWAb,EAAAc,gBAAA,SAAAvD,EAAAsD,GACA,OAAArP,KAAAoP,MAAAE,gBAAAvD,EAAAsD,IAYAb,EAAA3Q,OAAA,SAAA0R,GACA,OAAAvP,KAAAoP,MAAAvR,OAAA0R,IAYAf,EAAAgB,gBAAA,SAAAD,GACA,OAAAvP,KAAAoP,MAAAI,gBAAAD,IAUAf,EAAAiB,OAAA,SAAA1D,GACA,OAAA/L,KAAAoP,MAAAK,OAAA1D,IAUAyC,EAAA7G,WAAA,SAAA+H,GACA,OAAA1P,KAAAoP,MAAAzH,WAAA+H,IAWAlB,EAAAvG,SAAA,SAAA8D,EAAAhL,GACA,OAAAf,KAAAoP,MAAAnH,SAAA8D,EAAAhL,IAOAyN,EAAAtO,UAAA4K,OAAA,WACA,OAAA9K,KAAAoP,MAAAnH,SAAAjI,KAAAnF,EAAAkQ,iB,6BCtIA3P,EAAAR,QAAA2T,EAGA,IAAArE,EAAA5O,EAAA,IAGAT,KAFA0T,EAAArO,UAAApB,OAAAsL,OAAAF,EAAAhK,YAAAmK,YAAAkE,GAAAjE,UAAA,SAEAhP,EAAA,KAiBA,SAAAiT,EAAA9T,EAAA+M,EAAAmI,EAAA9N,EAAA+N,EAAAC,EAAA9O,EAAAwJ,EAAAuF,GAYA,GATAjV,EAAAgR,SAAA+D,IACA7O,EAAA6O,EACAA,EAAAC,EAAA1V,GACAU,EAAAgR,SAAAgE,KACA9O,EAAA8O,EACAA,EAAA1V,GAIAqN,IAAArN,IAAAU,EAAAqQ,SAAA1D,GACA,MAAAiD,UAAA,yBAGA,IAAA5P,EAAAqQ,SAAAyE,GACA,MAAAlF,UAAA,gCAGA,IAAA5P,EAAAqQ,SAAArJ,GACA,MAAA4I,UAAA,iCAEAP,EAAAvP,KAAAqF,KAAAvF,EAAAsG,GAMAf,KAAAwH,KAAAA,GAAA,MAMAxH,KAAA2P,YAAAA,EAMA3P,KAAA4P,gBAAAA,GAAAzV,EAMA6F,KAAA6B,aAAAA,EAMA7B,KAAA6P,iBAAAA,GAAA1V,EAMA6F,KAAA+P,oBAAA,KAMA/P,KAAAgQ,qBAAA,KAMAhQ,KAAAuK,QAAAA,EAKAvK,KAAA8P,cAAAA,EAuBAvB,EAAA5D,SAAA,SAAAlQ,EAAAmQ,GACA,OAAA,IAAA2D,EAAA9T,EAAAmQ,EAAApD,KAAAoD,EAAA+E,YAAA/E,EAAA/I,aAAA+I,EAAAgF,cAAAhF,EAAAiF,eAAAjF,EAAA7J,QAAA6J,EAAAL,QAAAK,EAAAkF,gBAQAvB,EAAArO,UAAA4K,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAAnQ,EAAAoN,SAAA,CACA,OAAA,QAAAjI,KAAAwH,MAAAxH,KAAAwH,MAAArN,EACA,cAAA6F,KAAA2P,YACA,gBAAA3P,KAAA4P,cACA,eAAA5P,KAAA6B,aACA,iBAAA7B,KAAA6P,eACA,UAAA7P,KAAAe,QACA,UAAAiK,EAAAhL,KAAAuK,QAAApQ,EACA,gBAAA6F,KAAA8P,iBAOAvB,EAAArO,UAAAjE,QAAA,WAGA,OAAA+D,KAAAyM,SACAzM,MAEAA,KAAA+P,oBAAA/P,KAAA0M,OAAAuD,WAAAjQ,KAAA2P,aACA3P,KAAAgQ,qBAAAhQ,KAAA0M,OAAAuD,WAAAjQ,KAAA6B,cAEAqI,EAAAhK,UAAAjE,QAAAtB,KAAAqF,S,mCC7JA5E,EAAAR,QAAAuP,EAGA,IAOAsB,EACA6C,EACA1H,EATAsD,EAAA5O,EAAA,IAGAkQ,KAFArB,EAAAjK,UAAApB,OAAAsL,OAAAF,EAAAhK,YAAAmK,YAAAF,GAAAG,UAAA,YAEAhP,EAAA,KACA8S,EAAA9S,EAAA,IACAT,EAAAS,EAAA,IAoCA,SAAA4U,EAAAC,EAAApF,GACA,IAAAoF,IAAAA,EAAAvU,OACA,OAAAzB,EAEA,IADA,IAAAiW,EAAA,GACAvT,EAAA,EAAAA,EAAAsT,EAAAvU,SAAAiB,EACAuT,EAAAD,EAAAtT,GAAApC,MAAA0V,EAAAtT,GAAAiO,OAAAC,GACA,OAAAqF,EA4CA,SAAAjG,EAAA1P,EAAAsG,GACAmJ,EAAAvP,KAAAqF,KAAAvF,EAAAsG,GAMAf,KAAAqQ,OAAAlW,EAOA6F,KAAAsQ,EAAA,KAGA,SAAAC,EAAAC,GAEA,OADAA,EAAAF,EAAA,KACAE,EAhFArG,EAAAQ,SAAA,SAAAlQ,EAAAmQ,GACA,OAAA,IAAAT,EAAA1P,EAAAmQ,EAAA7J,SAAA0P,QAAA7F,EAAAyF,SAmBAlG,EAAA+F,YAAAA,EAQA/F,EAAAiB,aAAA,SAAAV,EAAApB,GACA,GAAAoB,EACA,IAAA,IAAA7N,EAAA,EAAAA,EAAA6N,EAAA9O,SAAAiB,EACA,GAAA,iBAAA6N,EAAA7N,IAAA6N,EAAA7N,GAAA,IAAAyM,GAAAoB,EAAA7N,GAAA,GAAAyM,EACA,OAAA,EACA,OAAA,GASAa,EAAAkB,eAAA,SAAAX,EAAAjQ,GACA,GAAAiQ,EACA,IAAA,IAAA7N,EAAA,EAAAA,EAAA6N,EAAA9O,SAAAiB,EACA,GAAA6N,EAAA7N,KAAApC,EACA,OAAA,EACA,OAAA,GA0CAqE,OAAAsN,eAAAjC,EAAAjK,UAAA,cAAA,CACAmM,IAAA,WACA,OAAArM,KAAAsQ,IAAAtQ,KAAAsQ,EAAAzV,EAAA6V,QAAA1Q,KAAAqQ,YA6BAlG,EAAAjK,UAAA4K,OAAA,SAAAC,GACA,OAAAlQ,EAAAoN,SAAA,CACA,UAAAjI,KAAAe,QACA,SAAAmP,EAAAlQ,KAAA2Q,YAAA5F,MASAZ,EAAAjK,UAAAuQ,QAAA,SAAAG,GAGA,GAAAA,EACA,IAAA,IAAAP,EAAAQ,EAAA/R,OAAAC,KAAA6R,GAAA/T,EAAA,EAAAA,EAAAgU,EAAAjV,SAAAiB,EACAwT,EAAAO,EAAAC,EAAAhU,IAJAmD,KAKAiL,KACAoF,EAAAxI,SAAA1N,EACAsR,EACA4E,EAAAlJ,SAAAhN,EACAyM,EACAyJ,EAAAS,UAAA3W,EACAmU,EACA+B,EAAA/G,KAAAnP,EACAqR,EACArB,GAPAQ,SAOAkG,EAAAhU,GAAAwT,IAIA,OAAArQ,MAQAmK,EAAAjK,UAAAmM,IAAA,SAAA5R,GACA,OAAAuF,KAAAqQ,QAAArQ,KAAAqQ,OAAA5V,IACA,MAUA0P,EAAAjK,UAAA6Q,QAAA,SAAAtW,GACA,GAAAuF,KAAAqQ,QAAArQ,KAAAqQ,OAAA5V,aAAAmM,EACA,OAAA5G,KAAAqQ,OAAA5V,GAAA0M,OACA,MAAAnJ,MAAA,iBAAAvD,IAUA0P,EAAAjK,UAAA+K,IAAA,SAAAyE,GAEA,KAAAA,aAAAlE,GAAAkE,EAAA9D,SAAAzR,GAAAuV,aAAAjE,GAAAiE,aAAA9I,GAAA8I,aAAApB,GAAAoB,aAAAvF,GAAAuF,aAAAtB,GACA,MAAA3D,UAAA,wCAEA,GAAAzK,KAAAqQ,OAEA,CACA,IAAAW,EAAAhR,KAAAqM,IAAAqD,EAAAjV,MACA,GAAAuW,EAAA,CACA,KAAAA,aAAA7G,GAAAuF,aAAAvF,IAAA6G,aAAAvF,GAAAuF,aAAA1C,EAWA,MAAAtQ,MAAA,mBAAA0R,EAAAjV,KAAA,QAAAuF,MARA,IADA,IAAAqQ,EAAAW,EAAAL,YACA9T,EAAA,EAAAA,EAAAwT,EAAAzU,SAAAiB,EACA6S,EAAAzE,IAAAoF,EAAAxT,IACAmD,KAAAuL,OAAAyF,GACAhR,KAAAqQ,SACArQ,KAAAqQ,OAAA,IACAX,EAAAuB,WAAAD,EAAAjQ,SAAA,SAZAf,KAAAqQ,OAAA,GAoBA,OAFArQ,KAAAqQ,OAAAX,EAAAjV,MAAAiV,GACAwB,MAAAlR,MACAuQ,EAAAvQ,OAUAmK,EAAAjK,UAAAqL,OAAA,SAAAmE,GAEA,KAAAA,aAAAxF,GACA,MAAAO,UAAA,qCACA,GAAAiF,EAAAhD,SAAA1M,KACA,MAAAhC,MAAA0R,EAAA,uBAAA1P,MAOA,cALAA,KAAAqQ,OAAAX,EAAAjV,MACAqE,OAAAC,KAAAiB,KAAAqQ,QAAAzU,SACAoE,KAAAqQ,OAAAlW,GAEAuV,EAAAyB,SAAAnR,MACAuQ,EAAAvQ,OASAmK,EAAAjK,UAAAnF,OAAA,SAAAyK,EAAAoF,GAEA,GAAA/P,EAAAqQ,SAAA1F,GACAA,EAAAA,EAAAE,MAAA,UACA,IAAAhK,MAAA0V,QAAA5L,GACA,MAAAiF,UAAA,gBACA,GAAAjF,GAAAA,EAAA5J,QAAA,KAAA4J,EAAA,GACA,MAAAxH,MAAA,yBAGA,IADA,IAAAqT,EAAArR,KACA,EAAAwF,EAAA5J,QAAA,CACA,IAAA0V,EAAA9L,EAAAK,QACA,GAAAwL,EAAAhB,QAAAgB,EAAAhB,OAAAiB,IAEA,MADAD,EAAAA,EAAAhB,OAAAiB,cACAnH,GACA,MAAAnM,MAAA,kDAEAqT,EAAApG,IAAAoG,EAAA,IAAAlH,EAAAmH,IAIA,OAFA1G,GACAyG,EAAAZ,QAAA7F,GACAyG,GAOAlH,EAAAjK,UAAAqR,WAAA,WAEA,IADA,IAAAlB,EAAArQ,KAAA2Q,YAAA9T,EAAA,EACAA,EAAAwT,EAAAzU,QACAyU,EAAAxT,aAAAsN,EACAkG,EAAAxT,KAAA0U,aAEAlB,EAAAxT,KAAAZ,UACA,OAAA+D,KAAA/D,WAUAkO,EAAAjK,UAAAsR,OAAA,SAAAhM,EAAAiM,EAAAC,GASA,GANA,kBAAAD,GACAC,EAAAD,EACAA,EAAAtX,GACAsX,IAAA/V,MAAA0V,QAAAK,KACAA,EAAA,CAAAA,IAEA5W,EAAAqQ,SAAA1F,IAAAA,EAAA5J,OAAA,CACA,GAAA,MAAA4J,EACA,OAAAxF,KAAA8N,KACAtI,EAAAA,EAAAE,MAAA,UACA,IAAAF,EAAA5J,OACA,OAAAoE,KAGA,GAAA,KAAAwF,EAAA,GACA,OAAAxF,KAAA8N,KAAA0D,OAAAhM,EAAA9H,MAAA,GAAA+T,GAGA,IAAAE,EAAA3R,KAAAqM,IAAA7G,EAAA,IACA,GAAAmM,GACA,GAAA,IAAAnM,EAAA5J,QACA,IAAA6V,IAAAA,EAAAvI,QAAAyI,EAAAtH,aACA,OAAAsH,OACA,GAAAA,aAAAxH,IAAAwH,EAAAA,EAAAH,OAAAhM,EAAA9H,MAAA,GAAA+T,GAAA,IACA,OAAAE,OAIA,IAAA,IAAA9U,EAAA,EAAAA,EAAAmD,KAAA2Q,YAAA/U,SAAAiB,EACA,GAAAmD,KAAAsQ,EAAAzT,aAAAsN,IAAAwH,EAAA3R,KAAAsQ,EAAAzT,GAAA2U,OAAAhM,EAAAiM,GAAA,IACA,OAAAE,EAGA,OAAA,OAAA3R,KAAA0M,QAAAgF,EACA,KACA1R,KAAA0M,OAAA8E,OAAAhM,EAAAiM,IAqBAtH,EAAAjK,UAAA+P,WAAA,SAAAzK,GACA,IAAAmM,EAAA3R,KAAAwR,OAAAhM,EAAA,CAAAiG,IACA,GAAAkG,EAEA,OAAAA,EADA,MAAA3T,MAAA,iBAAAwH,IAWA2E,EAAAjK,UAAA0R,WAAA,SAAApM,GACA,IAAAmM,EAAA3R,KAAAwR,OAAAhM,EAAA,CAAAoB,IACA,GAAA+K,EAEA,OAAAA,EADA,MAAA3T,MAAA,iBAAAwH,EAAA,QAAAxF,OAWAmK,EAAAjK,UAAAyM,iBAAA,SAAAnH,GACA,IAAAmM,EAAA3R,KAAAwR,OAAAhM,EAAA,CAAAiG,EAAA7E,IACA,GAAA+K,EAEA,OAAAA,EADA,MAAA3T,MAAA,yBAAAwH,EAAA,QAAAxF,OAWAmK,EAAAjK,UAAA2R,cAAA,SAAArM,GACA,IAAAmM,EAAA3R,KAAAwR,OAAAhM,EAAA,CAAA8I,IACA,GAAAqD,EAEA,OAAAA,EADA,MAAA3T,MAAA,oBAAAwH,EAAA,QAAAxF,OAKAmK,EAAAuD,EAAA,SAAAC,EAAAmE,EAAAC,GACAtG,EAAAkC,EACAW,EAAAwD,EACAlL,EAAAmL,I,gDC/aA3W,EAAAR,QAAAsP,GAEAI,UAAA,mBAEA,IAEAyD,EAFAlT,EAAAS,EAAA,IAYA,SAAA4O,EAAAzP,EAAAsG,GAEA,IAAAlG,EAAAqQ,SAAAzQ,GACA,MAAAgQ,UAAA,yBAEA,GAAA1J,IAAAlG,EAAAgR,SAAA9K,GACA,MAAA0J,UAAA,6BAMAzK,KAAAe,QAAAA,EAMAf,KAAA8P,cAAA,KAMA9P,KAAAvF,KAAAA,EAMAuF,KAAA0M,OAAA,KAMA1M,KAAAyM,UAAA,EAMAzM,KAAAuK,QAAA,KAMAvK,KAAAc,SAAA,KAGAhC,OAAAkT,iBAAA9H,EAAAhK,UAAA,CAQA4N,KAAA,CACAzB,IAAA,WAEA,IADA,IAAAgF,EAAArR,KACA,OAAAqR,EAAA3E,QACA2E,EAAAA,EAAA3E,OACA,OAAA2E,IAUA/J,SAAA,CACA+E,IAAA,WAGA,IAFA,IAAA7G,EAAA,CAAAxF,KAAAvF,MACA4W,EAAArR,KAAA0M,OACA2E,GACA7L,EAAAyM,QAAAZ,EAAA5W,MACA4W,EAAAA,EAAA3E,OAEA,OAAAlH,EAAA7H,KAAA,SAUAuM,EAAAhK,UAAA4K,OAAA,WACA,MAAA9M,SAQAkM,EAAAhK,UAAAgR,MAAA,SAAAxE,GACA1M,KAAA0M,QAAA1M,KAAA0M,SAAAA,GACA1M,KAAA0M,OAAAnB,OAAAvL,MACAA,KAAA0M,OAAAA,EACA1M,KAAAyM,UAAA,EACAqB,EAAApB,EAAAoB,KACAA,aAAAC,GACAD,EAAAoE,EAAAlS,OAQAkK,EAAAhK,UAAAiR,SAAA,SAAAzE,GACAoB,EAAApB,EAAAoB,KACAA,aAAAC,GACAD,EAAAqE,EAAAnS,MACAA,KAAA0M,OAAA,KACA1M,KAAAyM,UAAA,GAOAvC,EAAAhK,UAAAjE,QAAA,WACA,OAAA+D,KAAAyM,UAEAzM,KAAA8N,gBAAAC,IACA/N,KAAAyM,UAAA,GAFAzM,MAWAkK,EAAAhK,UAAAoM,UAAA,SAAA7R,GACA,OAAAuF,KAAAe,QACAf,KAAAe,QAAAtG,GACAN,GAUA+P,EAAAhK,UAAAqM,UAAA,SAAA9R,EAAAgF,EAAA+M,GAGA,OAFAA,GAAAxM,KAAAe,SAAAf,KAAAe,QAAAtG,KAAAN,KACA6F,KAAAe,UAAAf,KAAAe,QAAA,KAAAtG,GAAAgF,GACAO,MAUAkK,EAAAhK,UAAAkS,gBAAA,SAAA3X,EAAAgF,EAAA4S,GACArS,KAAA8P,gBACA9P,KAAA8P,cAAA,IAEA,IAIAwC,EAeAC,EAnBAzC,EAAA9P,KAAA8P,cAuBA,OAtBAuC,GAGAC,EAAAxC,EAAA0C,KAAA,SAAAF,GACA,OAAAxT,OAAAoB,UAAAuS,eAAA9X,KAAA2X,EAAA7X,OAIAiY,EAAAJ,EAAA7X,GACAI,EAAA8X,YAAAD,EAAAL,EAAA5S,MAGA6S,EAAA,IACA7X,GAAAI,EAAA8X,YAAA,GAAAN,EAAA5S,GACAqQ,EAAAvS,KAAA+U,MAIAC,EAAA,IACA9X,GAAAgF,EACAqQ,EAAAvS,KAAAgV,IAEAvS,MASAkK,EAAAhK,UAAA+Q,WAAA,SAAAlQ,EAAAyL,GACA,GAAAzL,EACA,IAAA,IAAAhC,EAAAD,OAAAC,KAAAgC,GAAAlE,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACAmD,KAAAuM,UAAAxN,EAAAlC,GAAAkE,EAAAhC,EAAAlC,IAAA2P,GACA,OAAAxM,MAOAkK,EAAAhK,UAAAzB,SAAA,WACA,IAAA6L,EAAAtK,KAAAqK,YAAAC,UACAhD,EAAAtH,KAAAsH,SACA,OAAAA,EAAA1L,OACA0O,EAAA,IAAAhD,EACAgD,GAIAJ,EAAAwD,EAAA,SAAAkF,GACA7E,EAAA6E,I,6BChPAxX,EAAAR,QAAAwT,EAGA,IAAAlE,EAAA5O,EAAA,IAGAkQ,KAFA4C,EAAAlO,UAAApB,OAAAsL,OAAAF,EAAAhK,YAAAmK,YAAA+D,GAAA9D,UAAA,QAEAhP,EAAA,KACAT,EAAAS,EAAA,IAYA,SAAA8S,EAAA3T,EAAAoY,EAAA9R,EAAAwJ,GAQA,GAPA7O,MAAA0V,QAAAyB,KACA9R,EAAA8R,EACAA,EAAA1Y,GAEA+P,EAAAvP,KAAAqF,KAAAvF,EAAAsG,GAGA8R,IAAA1Y,IAAAuB,MAAA0V,QAAAyB,GACA,MAAApI,UAAA,+BAMAzK,KAAA8S,MAAAD,GAAA,GAOA7S,KAAA8H,YAAA,GAMA9H,KAAAuK,QAAAA,EA0CA,SAAAwI,EAAAD,GACA,GAAAA,EAAApG,OACA,IAAA,IAAA7P,EAAA,EAAAA,EAAAiW,EAAAhL,YAAAlM,SAAAiB,EACAiW,EAAAhL,YAAAjL,GAAA6P,QACAoG,EAAApG,OAAAzB,IAAA6H,EAAAhL,YAAAjL,IA7BAuR,EAAAzD,SAAA,SAAAlQ,EAAAmQ,GACA,OAAA,IAAAwD,EAAA3T,EAAAmQ,EAAAkI,MAAAlI,EAAA7J,QAAA6J,EAAAL,UAQA6D,EAAAlO,UAAA4K,OAAA,SAAAC,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAAnQ,EAAAoN,SAAA,CACA,UAAAjI,KAAAe,QACA,QAAAf,KAAA8S,MACA,UAAA9H,EAAAhL,KAAAuK,QAAApQ,KAuBAiU,EAAAlO,UAAA+K,IAAA,SAAAlE,GAGA,GAAAA,aAAAyE,EASA,OANAzE,EAAA2F,QAAA3F,EAAA2F,SAAA1M,KAAA0M,QACA3F,EAAA2F,OAAAnB,OAAAxE,GACA/G,KAAA8S,MAAAvV,KAAAwJ,EAAAtM,MACAuF,KAAA8H,YAAAvK,KAAAwJ,GAEAgM,EADAhM,EAAAwB,OAAAvI,MAEAA,KARA,MAAAyK,UAAA,0BAgBA2D,EAAAlO,UAAAqL,OAAA,SAAAxE,GAGA,KAAAA,aAAAyE,GACA,MAAAf,UAAA,yBAEA,IAAA3O,EAAAkE,KAAA8H,YAAAoB,QAAAnC,GAGA,GAAAjL,EAAA,EACA,MAAAkC,MAAA+I,EAAA,uBAAA/G,MAUA,OARAA,KAAA8H,YAAAvH,OAAAzE,EAAA,IAIA,GAHAA,EAAAkE,KAAA8S,MAAA5J,QAAAnC,EAAAtM,QAIAuF,KAAA8S,MAAAvS,OAAAzE,EAAA,GAEAiL,EAAAwB,OAAA,KACAvI,MAMAoO,EAAAlO,UAAAgR,MAAA,SAAAxE,GACAxC,EAAAhK,UAAAgR,MAAAvW,KAAAqF,KAAA0M,GAGA,IAFA,IAEA7P,EAAA,EAAAA,EAAAmD,KAAA8S,MAAAlX,SAAAiB,EAAA,CACA,IAAAkK,EAAA2F,EAAAL,IAAArM,KAAA8S,MAAAjW,IACAkK,IAAAA,EAAAwB,SACAxB,EAAAwB,OALAvI,MAMA8H,YAAAvK,KAAAwJ,GAIAgM,EAAA/S,OAMAoO,EAAAlO,UAAAiR,SAAA,SAAAzE,GACA,IAAA,IAAA3F,EAAAlK,EAAA,EAAAA,EAAAmD,KAAA8H,YAAAlM,SAAAiB,GACAkK,EAAA/G,KAAA8H,YAAAjL,IAAA6P,QACA3F,EAAA2F,OAAAnB,OAAAxE,GACAmD,EAAAhK,UAAAiR,SAAAxW,KAAAqF,KAAA0M,IAmBA0B,EAAAlB,EAAA,WAGA,IAFA,IAAA2F,EAAAnX,MAAAC,UAAAC,QACAE,EAAA,EACAA,EAAAH,UAAAC,QACAiX,EAAA/W,GAAAH,UAAAG,KACA,OAAA,SAAAoE,EAAA8S,GACAnY,EAAAyS,aAAApN,EAAAmK,aACAY,IAAA,IAAAmD,EAAA4E,EAAAH,IACA/T,OAAAsN,eAAAlM,EAAA8S,EAAA,CACA3G,IAAAxR,EAAAoY,YAAAJ,GACAK,IAAArY,EAAAsY,YAAAN,Q,yCCtMAzX,EAAAR,QAAAgU,EAEA,IAEAC,EAFAhU,EAAAS,EAAA,IAIA8X,EAAAvY,EAAAuY,SACA9M,EAAAzL,EAAAyL,KAGA,SAAA+M,EAAA9D,EAAA+D,GACA,OAAAC,WAAA,uBAAAhE,EAAAlN,IAAA,OAAAiR,GAAA,GAAA,MAAA/D,EAAAhJ,KASA,SAAAqI,EAAA7R,GAMAiD,KAAAoC,IAAArF,EAMAiD,KAAAqC,IAAA,EAMArC,KAAAuG,IAAAxJ,EAAAnB,OAgBA,SAAAwO,IACA,OAAAvP,EAAA2Y,OACA,SAAAzW,GACA,OAAA6R,EAAAxE,OAAA,SAAArN,GACA,OAAAlC,EAAA2Y,OAAAC,SAAA1W,GACA,IAAA8R,EAAA9R,GAEA2W,EAAA3W,KACAA,IAGA2W,EAxBA,IA4CAjU,EA5CAiU,EAAA,oBAAA/R,WACA,SAAA5E,GACA,GAAAA,aAAA4E,YAAAjG,MAAA0V,QAAArU,GACA,OAAA,IAAA6R,EAAA7R,GACA,MAAAiB,MAAA,mBAGA,SAAAjB,GACA,GAAArB,MAAA0V,QAAArU,GACA,OAAA,IAAA6R,EAAA7R,GACA,MAAAiB,MAAA,mBAsEA,SAAA2V,IAEA,IAAAC,EAAA,IAAAR,EAAA,EAAA,GACAvW,EAAA,EACA,KAAA,EAAAmD,KAAAuG,IAAAvG,KAAAqC,KAaA,CACA,KAAAxF,EAAA,IAAAA,EAAA,CAEA,GAAAmD,KAAAqC,KAAArC,KAAAuG,IACA,MAAA8M,EAAArT,MAGA,GADA4T,EAAA9P,IAAA8P,EAAA9P,IAAA,IAAA9D,KAAAoC,IAAApC,KAAAqC,OAAA,EAAAxF,KAAA,EACAmD,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAAuR,EAIA,OADAA,EAAA9P,IAAA8P,EAAA9P,IAAA,IAAA9D,KAAAoC,IAAApC,KAAAqC,SAAA,EAAAxF,KAAA,EACA+W,EAxBA,KAAA/W,EAAA,IAAAA,EAGA,GADA+W,EAAA9P,IAAA8P,EAAA9P,IAAA,IAAA9D,KAAAoC,IAAApC,KAAAqC,OAAA,EAAAxF,KAAA,EACAmD,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAAuR,EAKA,GAFAA,EAAA9P,IAAA8P,EAAA9P,IAAA,IAAA9D,KAAAoC,IAAApC,KAAAqC,OAAA,MAAA,EACAuR,EAAA7P,IAAA6P,EAAA7P,IAAA,IAAA/D,KAAAoC,IAAApC,KAAAqC,OAAA,KAAA,EACArC,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAAuR,EAgBA,GAfA/W,EAAA,EAeA,EAAAmD,KAAAuG,IAAAvG,KAAAqC,KACA,KAAAxF,EAAA,IAAAA,EAGA,GADA+W,EAAA7P,IAAA6P,EAAA7P,IAAA,IAAA/D,KAAAoC,IAAApC,KAAAqC,OAAA,EAAAxF,EAAA,KAAA,EACAmD,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAAuR,OAGA,KAAA/W,EAAA,IAAAA,EAAA,CAEA,GAAAmD,KAAAqC,KAAArC,KAAAuG,IACA,MAAA8M,EAAArT,MAGA,GADA4T,EAAA7P,IAAA6P,EAAA7P,IAAA,IAAA/D,KAAAoC,IAAApC,KAAAqC,OAAA,EAAAxF,EAAA,KAAA,EACAmD,KAAAoC,IAAApC,KAAAqC,OAAA,IACA,OAAAuR,EAIA,MAAA5V,MAAA,2BAkCA,SAAA6V,EAAAzR,EAAAnF,GACA,OAAAmF,EAAAnF,EAAA,GACAmF,EAAAnF,EAAA,IAAA,EACAmF,EAAAnF,EAAA,IAAA,GACAmF,EAAAnF,EAAA,IAAA,MAAA,EA+BA,SAAA6W,IAGA,GAAA9T,KAAAqC,IAAA,EAAArC,KAAAuG,IACA,MAAA8M,EAAArT,KAAA,GAEA,OAAA,IAAAoT,EAAAS,EAAA7T,KAAAoC,IAAApC,KAAAqC,KAAA,GAAAwR,EAAA7T,KAAAoC,IAAApC,KAAAqC,KAAA,IA3KAuM,EAAAxE,OAAAA,IAEAwE,EAAA1O,UAAA6T,EAAAlZ,EAAAa,MAAAwE,UAAA8T,UAAAnZ,EAAAa,MAAAwE,UAAAxC,MAOAkR,EAAA1O,UAAA+T,QACAxU,EAAA,WACA,WACA,GAAAA,GAAA,IAAAO,KAAAoC,IAAApC,KAAAqC,QAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EACA,GAAAA,GAAAA,GAAA,IAAAO,KAAAoC,IAAApC,KAAAqC,OAAA,KAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EACA,GAAAA,GAAAA,GAAA,IAAAO,KAAAoC,IAAApC,KAAAqC,OAAA,MAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EACA,GAAAA,GAAAA,GAAA,IAAAO,KAAAoC,IAAApC,KAAAqC,OAAA,MAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EACA,GAAAA,GAAAA,GAAA,GAAAO,KAAAoC,IAAApC,KAAAqC,OAAA,MAAA,EAAArC,KAAAoC,IAAApC,KAAAqC,OAAA,IAAA,OAAA5C,EAGA,IAAAO,KAAAqC,KAAA,GAAArC,KAAAuG,IAEA,MADAvG,KAAAqC,IAAArC,KAAAuG,IACA8M,EAAArT,KAAA,IAEA,OAAAP,IAQAmP,EAAA1O,UAAAgU,MAAA,WACA,OAAA,EAAAlU,KAAAiU,UAOArF,EAAA1O,UAAAiU,OAAA,WACA,IAAA1U,EAAAO,KAAAiU,SACA,OAAAxU,IAAA,IAAA,EAAAA,GAAA,GAqFAmP,EAAA1O,UAAAkU,KAAA,WACA,OAAA,IAAApU,KAAAiU,UAcArF,EAAA1O,UAAAmU,QAAA,WAGA,GAAArU,KAAAqC,IAAA,EAAArC,KAAAuG,IACA,MAAA8M,EAAArT,KAAA,GAEA,OAAA6T,EAAA7T,KAAAoC,IAAApC,KAAAqC,KAAA,IAOAuM,EAAA1O,UAAAoU,SAAA,WAGA,GAAAtU,KAAAqC,IAAA,EAAArC,KAAAuG,IACA,MAAA8M,EAAArT,KAAA,GAEA,OAAA,EAAA6T,EAAA7T,KAAAoC,IAAApC,KAAAqC,KAAA,IAmCAuM,EAAA1O,UAAAqU,MAAA,WAGA,GAAAvU,KAAAqC,IAAA,EAAArC,KAAAuG,IACA,MAAA8M,EAAArT,KAAA,GAEA,IAAAP,EAAA5E,EAAA0Z,MAAAhQ,YAAAvE,KAAAoC,IAAApC,KAAAqC,KAEA,OADArC,KAAAqC,KAAA,EACA5C,GAQAmP,EAAA1O,UAAAsU,OAAA,WAGA,GAAAxU,KAAAqC,IAAA,EAAArC,KAAAuG,IACA,MAAA8M,EAAArT,KAAA,GAEA,IAAAP,EAAA5E,EAAA0Z,MAAAtP,aAAAjF,KAAAoC,IAAApC,KAAAqC,KAEA,OADArC,KAAAqC,KAAA,EACA5C,GAOAmP,EAAA1O,UAAA6I,MAAA,WACA,IAAAnN,EAAAoE,KAAAiU,SACAjX,EAAAgD,KAAAqC,IACApF,EAAA+C,KAAAqC,IAAAzG,EAGA,GAAAqB,EAAA+C,KAAAuG,IACA,MAAA8M,EAAArT,KAAApE,GAGA,OADAoE,KAAAqC,KAAAzG,EACAF,MAAA0V,QAAApR,KAAAoC,KACApC,KAAAoC,IAAA1E,MAAAV,EAAAC,GACAD,IAAAC,EACA,IAAA+C,KAAAoC,IAAAiI,YAAA,GACArK,KAAA+T,EAAApZ,KAAAqF,KAAAoC,IAAApF,EAAAC,IAOA2R,EAAA1O,UAAA5D,OAAA,WACA,IAAAyM,EAAA/I,KAAA+I,QACA,OAAAzC,EAAAE,KAAAuC,EAAA,EAAAA,EAAAnN,SAQAgT,EAAA1O,UAAAuU,KAAA,SAAA7Y,GACA,GAAA,iBAAAA,EAAA,CAEA,GAAAoE,KAAAqC,IAAAzG,EAAAoE,KAAAuG,IACA,MAAA8M,EAAArT,KAAApE,GACAoE,KAAAqC,KAAAzG,OAEA,GAEA,GAAAoE,KAAAqC,KAAArC,KAAAuG,IACA,MAAA8M,EAAArT,YACA,IAAAA,KAAAoC,IAAApC,KAAAqC,QAEA,OAAArC,MAQA4O,EAAA1O,UAAAwU,SAAA,SAAA5K,GACA,OAAAA,GACA,KAAA,EACA9J,KAAAyU,OACA,MACA,KAAA,EACAzU,KAAAyU,KAAA,GACA,MACA,KAAA,EACAzU,KAAAyU,KAAAzU,KAAAiU,UACA,MACA,KAAA,EACA,KAAA,IAAAnK,EAAA,EAAA9J,KAAAiU,WACAjU,KAAA0U,SAAA5K,GAEA,MACA,KAAA,EACA9J,KAAAyU,KAAA,GACA,MAGA,QACA,MAAAzW,MAAA,qBAAA8L,EAAA,cAAA9J,KAAAqC,KAEA,OAAArC,MAGA4O,EAAAlB,EAAA,SAAAiH,GACA9F,EAAA8F,EACA/F,EAAAxE,OAAAA,IACAyE,EAAAnB,IAEA,IAAAnS,EAAAV,EAAAI,KAAA,SAAA,WACAJ,EAAA+Z,MAAAhG,EAAA1O,UAAA,CAEA2U,MAAA,WACA,OAAAlB,EAAAhZ,KAAAqF,MAAAzE,IAAA,IAGAuZ,OAAA,WACA,OAAAnB,EAAAhZ,KAAAqF,MAAAzE,IAAA,IAGAwZ,OAAA,WACA,OAAApB,EAAAhZ,KAAAqF,MAAAgV,WAAAzZ,IAAA,IAGA0Z,QAAA,WACA,OAAAnB,EAAAnZ,KAAAqF,MAAAzE,IAAA,IAGA2Z,SAAA,WACA,OAAApB,EAAAnZ,KAAAqF,MAAAzE,IAAA,Q,6BCrZAH,EAAAR,QAAAiU,EAGA,IAAAD,EAAAtT,EAAA,IAGAT,IAFAgU,EAAA3O,UAAApB,OAAAsL,OAAAwE,EAAA1O,YAAAmK,YAAAwE,EAEAvT,EAAA,KASA,SAAAuT,EAAA9R,GACA6R,EAAAjU,KAAAqF,KAAAjD,GASA8R,EAAAnB,EAAA,WAEA7S,EAAA2Y,SACA3E,EAAA3O,UAAA6T,EAAAlZ,EAAA2Y,OAAAtT,UAAAxC,QAOAmR,EAAA3O,UAAA5D,OAAA,WACA,IAAAiK,EAAAvG,KAAAiU,SACA,OAAAjU,KAAAoC,IAAA+S,UACAnV,KAAAoC,IAAA+S,UAAAnV,KAAAqC,IAAArC,KAAAqC,IAAA5F,KAAA2Y,IAAApV,KAAAqC,IAAAkE,EAAAvG,KAAAuG,MACAvG,KAAAoC,IAAA3D,SAAA,QAAAuB,KAAAqC,IAAArC,KAAAqC,IAAA5F,KAAA2Y,IAAApV,KAAAqC,IAAAkE,EAAAvG,KAAAuG,OAUAsI,EAAAnB,K,mCCjDAtS,EAAAR,QAAAmT,EAGA,IAQAtC,EACA4J,EACAC,EAVAnL,EAAA7O,EAAA,IAGAkQ,KAFAuC,EAAA7N,UAAApB,OAAAsL,OAAAD,EAAAjK,YAAAmK,YAAA0D,GAAAzD,UAAA,OAEAhP,EAAA,KACAsL,EAAAtL,EAAA,IACA8S,EAAA9S,EAAA,IACAT,EAAAS,EAAA,IAaA,SAAAyS,EAAAhN,GACAoJ,EAAAxP,KAAAqF,KAAA,GAAAe,GAMAf,KAAAuV,SAAA,GAMAvV,KAAAwV,MAAA,GAuCA,SAAAC,KA9BA1H,EAAApD,SAAA,SAAAC,EAAAkD,GAKA,OAHAA,EADAA,GACA,IAAAC,EACAnD,EAAA7J,SACA+M,EAAAmD,WAAArG,EAAA7J,SACA+M,EAAA2C,QAAA7F,EAAAyF,SAWAtC,EAAA7N,UAAAwV,YAAA7a,EAAA2K,KAAAvJ,QAUA8R,EAAA7N,UAAAQ,MAAA7F,EAAA6F,MAaAqN,EAAA7N,UAAA2N,KAAA,SAAAA,EAAA/M,EAAAC,EAAAC,GACA,mBAAAD,IACAC,EAAAD,EACAA,EAAA5G,GAEA,IAAAwb,EAAA3V,KACA,IAAAgB,EACA,OAAAnG,EAAA8F,UAAAkN,EAAA8H,EAAA7U,EAAAC,GAEA,IAAA6U,EAAA5U,IAAAyU,EAGA,SAAAI,EAAA1Z,EAAA2R,GAEA,GAAA9M,EAAA,CAEA,IAAA8U,EAAA9U,EAEA,GADAA,EAAA,KACA4U,EACA,MAAAzZ,EACA2Z,EAAA3Z,EAAA2R,IAIA,SAAAiI,EAAAjV,GACA,IAAAkV,EAAAlV,EAAAmV,YAAA,oBACA,IAAA,EAAAD,EAAA,CACAE,EAAApV,EAAAqV,UAAAH,GACA,GAAAE,KAAAZ,EAAA,OAAAY,EAEA,OAAA,KAIA,SAAAE,EAAAtV,EAAAtC,GACA,IAGA,GAFA3D,EAAAqQ,SAAA1M,IAAA,MAAAA,EAAA,IAAAA,MACAA,EAAAoB,KAAAyV,MAAA7W,IACA3D,EAAAqQ,SAAA1M,GAEA,CACA6W,EAAAvU,SAAAA,EACA,IACA2L,EADA4J,EAAAhB,EAAA7W,EAAAmX,EAAA5U,GAEAlE,EAAA,EACA,GAAAwZ,EAAAC,QACA,KAAAzZ,EAAAwZ,EAAAC,QAAA1a,SAAAiB,GACA4P,EAAAsJ,EAAAM,EAAAC,QAAAzZ,KAAA8Y,EAAAD,YAAA5U,EAAAuV,EAAAC,QAAAzZ,MACA6D,EAAA+L,GACA,GAAA4J,EAAAE,YACA,IAAA1Z,EAAA,EAAAA,EAAAwZ,EAAAE,YAAA3a,SAAAiB,GACA4P,EAAAsJ,EAAAM,EAAAE,YAAA1Z,KAAA8Y,EAAAD,YAAA5U,EAAAuV,EAAAE,YAAA1Z,MACA6D,EAAA+L,GAAA,QAbAkJ,EAAA1E,WAAAzS,EAAAuC,SAAA0P,QAAAjS,EAAA6R,QAeA,MAAAlU,GACA0Z,EAAA1Z,GAEAyZ,GAAAY,GACAX,EAAA,KAAAF,GAIA,SAAAjV,EAAAI,EAAA2V,GAGA,KAAAd,EAAAH,MAAAtM,QAAApI,GAKA,GAHA6U,EAAAH,MAAAjY,KAAAuD,GAGAA,KAAAwU,EACAM,EACAQ,EAAAtV,EAAAwU,EAAAxU,OAEA0V,EACAE,WAAA,aACAF,EACAJ,EAAAtV,EAAAwU,EAAAxU,YAOA,GAAA8U,EAAA,CACA,IAAApX,EACA,IACAA,EAAA3D,EAAA+F,GAAA+V,aAAA7V,GAAArC,SAAA,QACA,MAAAtC,GAGA,YAFAsa,GACAZ,EAAA1Z,IAGAia,EAAAtV,EAAAtC,SAEAgY,EACAb,EAAAjV,MAAAI,EAAA,SAAA3E,EAAAqC,KACAgY,EAEAxV,IAEA7E,EAEAsa,EAEAD,GACAX,EAAA,KAAAF,GAFAE,EAAA1Z,GAKAia,EAAAtV,EAAAtC,MAIA,IAAAgY,EAAA,EAIA3b,EAAAqQ,SAAApK,KACAA,EAAA,CAAAA,IACA,IAAA,IAAA2L,EAAA5P,EAAA,EAAAA,EAAAiE,EAAAlF,SAAAiB,GACA4P,EAAAkJ,EAAAD,YAAA,GAAA5U,EAAAjE,MACA6D,EAAA+L,GAEA,OAAAmJ,EACAD,GACAa,GACAX,EAAA,KAAAF,GACAxb,IAgCA4T,EAAA7N,UAAA8N,SAAA,SAAAlN,EAAAC,GACA,GAAAlG,EAAA+b,OAEA,OAAA5W,KAAA6N,KAAA/M,EAAAC,EAAA0U,GADA,MAAAzX,MAAA,kBAOA+P,EAAA7N,UAAAqR,WAAA,WACA,GAAAvR,KAAAuV,SAAA3Z,OACA,MAAAoC,MAAA,4BAAAgC,KAAAuV,SAAAvN,IAAA,SAAAjB,GACA,MAAA,WAAAA,EAAA6E,OAAA,QAAA7E,EAAA2F,OAAApF,WACA3J,KAAA,OACA,OAAAwM,EAAAjK,UAAAqR,WAAA5W,KAAAqF,OAIA,IAAA6W,EAAA,SAUA,SAAAC,EAAAhJ,EAAA/G,GACA,IAEAgQ,EAFAC,EAAAjQ,EAAA2F,OAAA8E,OAAAzK,EAAA6E,QACA,GAAAoL,EAKA,QAJAD,EAAA,IAAAvL,EAAAzE,EAAAO,SAAAP,EAAAuC,GAAAvC,EAAAS,KAAAT,EAAA4E,KAAAxR,EAAA4M,EAAAhG,UACAmL,eAAAnF,GACAkF,eAAA8K,EACAC,EAAA/L,IAAA8L,GACA,EAWAhJ,EAAA7N,UAAAgS,EAAA,SAAAxC,GACA,GAAAA,aAAAlE,EAEAkE,EAAA9D,SAAAzR,GAAAuV,EAAAzD,gBACA6K,EAAA9W,EAAA0P,IACA1P,KAAAuV,SAAAhY,KAAAmS,QAEA,GAAAA,aAAA9I,EAEAiQ,EAAA5Y,KAAAyR,EAAAjV,QACAiV,EAAAhD,OAAAgD,EAAAjV,MAAAiV,EAAAvI,aAEA,KAAAuI,aAAAtB,GAAA,CAEA,GAAAsB,aAAAjE,EACA,IAAA,IAAA5O,EAAA,EAAAA,EAAAmD,KAAAuV,SAAA3Z,QACAkb,EAAA9W,EAAAA,KAAAuV,SAAA1Y,IACAmD,KAAAuV,SAAAhV,OAAA1D,EAAA,KAEAA,EACA,IAAA,IAAAQ,EAAA,EAAAA,EAAAqS,EAAAiB,YAAA/U,SAAAyB,EACA2C,KAAAkS,EAAAxC,EAAAY,EAAAjT,IACAwZ,EAAA5Y,KAAAyR,EAAAjV,QACAiV,EAAAhD,OAAAgD,EAAAjV,MAAAiV,KAcA3B,EAAA7N,UAAAiS,EAAA,SAAAzC,GAGA,IAKA5T,EAPA,GAAA4T,aAAAlE,EAEAkE,EAAA9D,SAAAzR,IACAuV,EAAAzD,gBACAyD,EAAAzD,eAAAS,OAAAnB,OAAAmE,EAAAzD,gBACAyD,EAAAzD,eAAA,OAIA,GAFAnQ,EAAAkE,KAAAuV,SAAArM,QAAAwG,KAGA1P,KAAAuV,SAAAhV,OAAAzE,EAAA,SAIA,GAAA4T,aAAA9I,EAEAiQ,EAAA5Y,KAAAyR,EAAAjV,cACAiV,EAAAhD,OAAAgD,EAAAjV,WAEA,GAAAiV,aAAAvF,EAAA,CAEA,IAAA,IAAAtN,EAAA,EAAAA,EAAA6S,EAAAiB,YAAA/U,SAAAiB,EACAmD,KAAAmS,EAAAzC,EAAAY,EAAAzT,IAEAga,EAAA5Y,KAAAyR,EAAAjV,cACAiV,EAAAhD,OAAAgD,EAAAjV,QAMAsT,EAAAL,EAAA,SAAAC,EAAAsJ,EAAAC,GACAzL,EAAAkC,EACA0H,EAAA4B,EACA3B,EAAA4B,I,qDCxWA9b,EAAAR,QAAA,I,wBCKAA,EA6BA0T,QAAAhT,EAAA,K,6BClCAF,EAAAR,QAAA0T,EAEA,IAAAzT,EAAAS,EAAA,IAsCA,SAAAgT,EAAA6I,EAAAC,EAAAC,GAEA,GAAA,mBAAAF,EACA,MAAA1M,UAAA,8BAEA5P,EAAAkF,aAAApF,KAAAqF,MAMAA,KAAAmX,QAAAA,EAMAnX,KAAAoX,mBAAAA,EAMApX,KAAAqX,oBAAAA,IA1DA/I,EAAApO,UAAApB,OAAAsL,OAAAvP,EAAAkF,aAAAG,YAAAmK,YAAAiE,GAwEApO,UAAAoX,QAAA,SAAAA,EAAAC,EAAAC,EAAAC,EAAAC,EAAA1W,GAEA,IAAA0W,EACA,MAAAjN,UAAA,6BAEA,IAAAkL,EAAA3V,KACA,IAAAgB,EACA,OAAAnG,EAAA8F,UAAA2W,EAAA3B,EAAA4B,EAAAC,EAAAC,EAAAC,GAEA,IAAA/B,EAAAwB,QAEA,OADAT,WAAA,WAAA1V,EAAAhD,MAAA,mBAAA,GACA7D,EAGA,IACA,OAAAwb,EAAAwB,QACAI,EACAC,EAAA7B,EAAAyB,iBAAA,kBAAA,UAAAM,GAAA7B,SACA,SAAA1Z,EAAAsF,GAEA,GAAAtF,EAEA,OADAwZ,EAAAnV,KAAA,QAAArE,EAAAob,GACAvW,EAAA7E,GAGA,GAAA,OAAAsF,EAEA,OADAkU,EAAA1Y,KAAA,GACA9C,EAGA,KAAAsH,aAAAgW,GACA,IACAhW,EAAAgW,EAAA9B,EAAA0B,kBAAA,kBAAA,UAAA5V,GACA,MAAAtF,GAEA,OADAwZ,EAAAnV,KAAA,QAAArE,EAAAob,GACAvW,EAAA7E,GAKA,OADAwZ,EAAAnV,KAAA,OAAAiB,EAAA8V,GACAvW,EAAA,KAAAS,KAGA,MAAAtF,GAGA,OAFAwZ,EAAAnV,KAAA,QAAArE,EAAAob,GACAb,WAAA,WAAA1V,EAAA7E,IAAA,GACAhC,IASAmU,EAAApO,UAAAjD,IAAA,SAAA0a,GAOA,OANA3X,KAAAmX,UACAQ,GACA3X,KAAAmX,QAAA,KAAA,KAAA,MACAnX,KAAAmX,QAAA,KACAnX,KAAAQ,KAAA,OAAAH,OAEAL,O,6BC3IA5E,EAAAR,QAAA0T,EAGA,IAAAnE,EAAA7O,EAAA,IAGAiT,KAFAD,EAAApO,UAAApB,OAAAsL,OAAAD,EAAAjK,YAAAmK,YAAAiE,GAAAhE,UAAA,UAEAhP,EAAA,KACAT,EAAAS,EAAA,IACAwT,EAAAxT,EAAA,IAWA,SAAAgT,EAAA7T,EAAAsG,GACAoJ,EAAAxP,KAAAqF,KAAAvF,EAAAsG,GAMAf,KAAA8Q,QAAA,GAOA9Q,KAAA4X,EAAA,KAyDA,SAAArH,EAAAsH,GAEA,OADAA,EAAAD,EAAA,KACAC,EA1CAvJ,EAAA3D,SAAA,SAAAlQ,EAAAmQ,GACA,IAAAiN,EAAA,IAAAvJ,EAAA7T,EAAAmQ,EAAA7J,SAEA,GAAA6J,EAAAkG,QACA,IAAA,IAAAD,EAAA/R,OAAAC,KAAA6L,EAAAkG,SAAAjU,EAAA,EAAAA,EAAAgU,EAAAjV,SAAAiB,EACAgb,EAAA5M,IAAAsD,EAAA5D,SAAAkG,EAAAhU,GAAA+N,EAAAkG,QAAAD,EAAAhU,MAIA,OAHA+N,EAAAyF,QACAwH,EAAApH,QAAA7F,EAAAyF,QACAwH,EAAAtN,QAAAK,EAAAL,QACAsN,GAQAvJ,EAAApO,UAAA4K,OAAA,SAAAC,GACA,IAAA+M,EAAA3N,EAAAjK,UAAA4K,OAAAnQ,KAAAqF,KAAA+K,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAAnQ,EAAAoN,SAAA,CACA,UAAA6P,GAAAA,EAAA/W,SAAA5G,EACA,UAAAgQ,EAAA+F,YAAAlQ,KAAA+X,aAAAhN,IAAA,GACA,SAAA+M,GAAAA,EAAAzH,QAAAlW,EACA,UAAA6Q,EAAAhL,KAAAuK,QAAApQ,KAUA2E,OAAAsN,eAAAkC,EAAApO,UAAA,eAAA,CACAmM,IAAA,WACA,OAAArM,KAAA4X,IAAA5X,KAAA4X,EAAA/c,EAAA6V,QAAA1Q,KAAA8Q,aAYAxC,EAAApO,UAAAmM,IAAA,SAAA5R,GACA,OAAAuF,KAAA8Q,QAAArW,IACA0P,EAAAjK,UAAAmM,IAAA1R,KAAAqF,KAAAvF,IAMA6T,EAAApO,UAAAqR,WAAA,WAEA,IADA,IAAAT,EAAA9Q,KAAA+X,aACAlb,EAAA,EAAAA,EAAAiU,EAAAlV,SAAAiB,EACAiU,EAAAjU,GAAAZ,UACA,OAAAkO,EAAAjK,UAAAjE,QAAAtB,KAAAqF,OAMAsO,EAAApO,UAAA+K,IAAA,SAAAyE,GAGA,GAAA1P,KAAAqM,IAAAqD,EAAAjV,MACA,MAAAuD,MAAA,mBAAA0R,EAAAjV,KAAA,QAAAuF,MAEA,OAAA0P,aAAAnB,EAGAgC,GAFAvQ,KAAA8Q,QAAApB,EAAAjV,MAAAiV,GACAhD,OAAA1M,MAGAmK,EAAAjK,UAAA+K,IAAAtQ,KAAAqF,KAAA0P,IAMApB,EAAApO,UAAAqL,OAAA,SAAAmE,GACA,GAAAA,aAAAnB,EAAA,CAGA,GAAAvO,KAAA8Q,QAAApB,EAAAjV,QAAAiV,EACA,MAAA1R,MAAA0R,EAAA,uBAAA1P,MAIA,cAFAA,KAAA8Q,QAAApB,EAAAjV,MACAiV,EAAAhD,OAAA,KACA6D,EAAAvQ,MAEA,OAAAmK,EAAAjK,UAAAqL,OAAA5Q,KAAAqF,KAAA0P,IAUApB,EAAApO,UAAAkK,OAAA,SAAA+M,EAAAC,EAAAC,GAEA,IADA,IACAE,EADAS,EAAA,IAAAlJ,EAAAR,QAAA6I,EAAAC,EAAAC,GACAxa,EAAA,EAAAA,EAAAmD,KAAA+X,aAAAnc,SAAAiB,EAAA,CACA,IAAAob,EAAApd,EAAAqd,SAAAX,EAAAvX,KAAA4X,EAAA/a,IAAAZ,UAAAxB,MAAA6E,QAAA,WAAA,IACA0Y,EAAAC,GAAApd,EAAAqD,QAAA,CAAA,IAAA,KAAArD,EAAAsd,WAAAF,GAAAA,EAAA,IAAAA,EAAApd,CAAA,iCAAAA,CAAA,CACAud,EAAAb,EACAc,EAAAd,EAAAxH,oBAAA9C,KACAqL,EAAAf,EAAAvH,qBAAA/C,OAGA,OAAA+K,I,+CCpKA5c,EAAAR,QAAA6Q,EAGA,IAAAtB,EAAA7O,EAAA,IAGAsL,KAFA6E,EAAAvL,UAAApB,OAAAsL,OAAAD,EAAAjK,YAAAmK,YAAAoB,GAAAnB,UAAA,OAEAhP,EAAA,KACA8S,EAAA9S,EAAA,IACAkQ,EAAAlQ,EAAA,IACA+S,EAAA/S,EAAA,IACAgT,EAAAhT,EAAA,IACAkT,EAAAlT,EAAA,IACAsT,EAAAtT,EAAA,IACAoT,EAAApT,EAAA,IACAT,EAAAS,EAAA,IACA2S,EAAA3S,EAAA,IACA4S,EAAA5S,EAAA,IACA6S,EAAA7S,EAAA,IACAoM,EAAApM,EAAA,IACAmT,EAAAnT,EAAA,IAUA,SAAAmQ,EAAAhR,EAAAsG,GACAoJ,EAAAxP,KAAAqF,KAAAvF,EAAAsG,GAMAf,KAAA6H,OAAA,GAMA7H,KAAAuY,OAAApe,EAMA6F,KAAAwY,WAAAre,EAMA6F,KAAA0K,SAAAvQ,EAMA6F,KAAAoJ,MAAAjP,EAOA6F,KAAAyY,EAAA,KAOAzY,KAAAiJ,EAAA,KAOAjJ,KAAA0Y,EAAA,KAOA1Y,KAAA2Y,EAAA,KA0HA,SAAApI,EAAA/I,GAKA,OAJAA,EAAAiR,EAAAjR,EAAAyB,EAAAzB,EAAAkR,EAAA,YACAlR,EAAA1K,cACA0K,EAAA3J,cACA2J,EAAAiI,OACAjI,EA5HA1I,OAAAkT,iBAAAvG,EAAAvL,UAAA,CAQA0Y,WAAA,CACAvM,IAAA,WAGA,GAAArM,KAAAyY,EACA,OAAAzY,KAAAyY,EAEAzY,KAAAyY,EAAA,GACA,IAAA,IAAA5H,EAAA/R,OAAAC,KAAAiB,KAAA6H,QAAAhL,EAAA,EAAAA,EAAAgU,EAAAjV,SAAAiB,EAAA,CACA,IAAAkK,EAAA/G,KAAA6H,OAAAgJ,EAAAhU,IACAyM,EAAAvC,EAAAuC,GAGA,GAAAtJ,KAAAyY,EAAAnP,GACA,MAAAtL,MAAA,gBAAAsL,EAAA,OAAAtJ,MAEAA,KAAAyY,EAAAnP,GAAAvC,EAEA,OAAA/G,KAAAyY,IAUA3Q,YAAA,CACAuE,IAAA,WACA,OAAArM,KAAAiJ,IAAAjJ,KAAAiJ,EAAApO,EAAA6V,QAAA1Q,KAAA6H,WAUAgR,YAAA,CACAxM,IAAA,WACA,OAAArM,KAAA0Y,IAAA1Y,KAAA0Y,EAAA7d,EAAA6V,QAAA1Q,KAAAuY,WAUAtL,KAAA,CACAZ,IAAA,WACA,OAAArM,KAAA2Y,IAAA3Y,KAAAiN,KAAAxB,EAAAqN,oBAAA9Y,KAAAyL,KAEAyH,IAAA,SAAAjG,GAmBA,IAhBA,IAAA/M,EAAA+M,EAAA/M,UAeArD,GAdAqD,aAAAsO,KACAvB,EAAA/M,UAAA,IAAAsO,GAAAnE,YAAA4C,EACApS,EAAA+Z,MAAA3H,EAAA/M,UAAAA,IAIA+M,EAAAmC,MAAAnC,EAAA/M,UAAAkP,MAAApP,KAGAnF,EAAA+Z,MAAA3H,EAAAuB,GAAA,GAEAxO,KAAA2Y,EAAA1L,EAGA,GACApQ,EAAAmD,KAAA8H,YAAAlM,SAAAiB,EACAmD,KAAAiJ,EAAApM,GAAAZ,UAIA,IADA,IAAA8c,EAAA,GACAlc,EAAA,EAAAA,EAAAmD,KAAA6Y,YAAAjd,SAAAiB,EACAkc,EAAA/Y,KAAA0Y,EAAA7b,GAAAZ,UAAAxB,MAAA,CACA4R,IAAAxR,EAAAoY,YAAAjT,KAAA0Y,EAAA7b,GAAAiW,OACAI,IAAArY,EAAAsY,YAAAnT,KAAA0Y,EAAA7b,GAAAiW,QAEAjW,GACAiC,OAAAkT,iBAAA/E,EAAA/M,UAAA6Y,OAUAtN,EAAAqN,oBAAA,SAAAlR,GAIA,IAFA,IAEAb,EAFAD,EAAAjM,EAAAqD,QAAA,CAAA,KAAA0J,EAAAnN,MAEAoC,EAAA,EAAAA,EAAA+K,EAAAE,YAAAlM,SAAAiB,GACAkK,EAAAa,EAAAqB,EAAApM,IAAAmL,IAAAlB,EACA,YAAAjM,EAAAkN,SAAAhB,EAAAtM,OACAsM,EAAAK,UAAAN,EACA,YAAAjM,EAAAkN,SAAAhB,EAAAtM,OACA,OAAAqM,EACA,wEADAA,CAEA,yBA6BA2E,EAAAd,SAAA,SAAAlQ,EAAAmQ,GAMA,IALA,IAAApD,EAAA,IAAAiE,EAAAhR,EAAAmQ,EAAA7J,SAGA8P,GAFArJ,EAAAgR,WAAA5N,EAAA4N,WACAhR,EAAAkD,SAAAE,EAAAF,SACA5L,OAAAC,KAAA6L,EAAA/C,SACAhL,EAAA,EACAA,EAAAgU,EAAAjV,SAAAiB,EACA2K,EAAAyD,UACA,IAAAL,EAAA/C,OAAAgJ,EAAAhU,IAAA4M,QACA4E,EACA7C,GADAb,SACAkG,EAAAhU,GAAA+N,EAAA/C,OAAAgJ,EAAAhU,MAEA,GAAA+N,EAAA2N,OACA,IAAA1H,EAAA/R,OAAAC,KAAA6L,EAAA2N,QAAA1b,EAAA,EAAAA,EAAAgU,EAAAjV,SAAAiB,EACA2K,EAAAyD,IAAAmD,EAAAzD,SAAAkG,EAAAhU,GAAA+N,EAAA2N,OAAA1H,EAAAhU,MACA,GAAA+N,EAAAyF,OACA,IAAAQ,EAAA/R,OAAAC,KAAA6L,EAAAyF,QAAAxT,EAAA,EAAAA,EAAAgU,EAAAjV,SAAAiB,EAAA,CACA,IAAAwT,EAAAzF,EAAAyF,OAAAQ,EAAAhU,IACA2K,EAAAyD,KACAoF,EAAA/G,KAAAnP,EACAqR,EACA6E,EAAAxI,SAAA1N,EACAsR,EACA4E,EAAAlJ,SAAAhN,EACAyM,EACAyJ,EAAAS,UAAA3W,EACAmU,EACAnE,GAPAQ,SAOAkG,EAAAhU,GAAAwT,IAWA,OARAzF,EAAA4N,YAAA5N,EAAA4N,WAAA5c,SACA4L,EAAAgR,WAAA5N,EAAA4N,YACA5N,EAAAF,UAAAE,EAAAF,SAAA9O,SACA4L,EAAAkD,SAAAE,EAAAF,UACAE,EAAAxB,QACA5B,EAAA4B,OAAA,GACAwB,EAAAL,UACA/C,EAAA+C,QAAAK,EAAAL,SACA/C,GAQAiE,EAAAvL,UAAA4K,OAAA,SAAAC,GACA,IAAA+M,EAAA3N,EAAAjK,UAAA4K,OAAAnQ,KAAAqF,KAAA+K,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAAnQ,EAAAoN,SAAA,CACA,UAAA6P,GAAAA,EAAA/W,SAAA5G,EACA,SAAAgQ,EAAA+F,YAAAlQ,KAAA6Y,YAAA9N,GACA,SAAAZ,EAAA+F,YAAAlQ,KAAA8H,YAAAqB,OAAA,SAAAiH,GAAA,OAAAA,EAAAlE,iBAAAnB,IAAA,GACA,aAAA/K,KAAAwY,YAAAxY,KAAAwY,WAAA5c,OAAAoE,KAAAwY,WAAAre,EACA,WAAA6F,KAAA0K,UAAA1K,KAAA0K,SAAA9O,OAAAoE,KAAA0K,SAAAvQ,EACA,QAAA6F,KAAAoJ,OAAAjP,EACA,SAAA2d,GAAAA,EAAAzH,QAAAlW,EACA,UAAA6Q,EAAAhL,KAAAuK,QAAApQ,KAOAsR,EAAAvL,UAAAqR,WAAA,WAEA,IADA,IAAA1J,EAAA7H,KAAA8H,YAAAjL,EAAA,EACAA,EAAAgL,EAAAjM,QACAiM,EAAAhL,KAAAZ,UAEA,IADA,IAAAsc,EAAAvY,KAAA6Y,YAAAhc,EAAA,EACAA,EAAA0b,EAAA3c,QACA2c,EAAA1b,KAAAZ,UACA,OAAAkO,EAAAjK,UAAAqR,WAAA5W,KAAAqF,OAMAyL,EAAAvL,UAAAmM,IAAA,SAAA5R,GACA,OAAAuF,KAAA6H,OAAApN,IACAuF,KAAAuY,QAAAvY,KAAAuY,OAAA9d,IACAuF,KAAAqQ,QAAArQ,KAAAqQ,OAAA5V,IACA,MAUAgR,EAAAvL,UAAA+K,IAAA,SAAAyE,GAEA,GAAA1P,KAAAqM,IAAAqD,EAAAjV,MACA,MAAAuD,MAAA,mBAAA0R,EAAAjV,KAAA,QAAAuF,MAEA,GAAA0P,aAAAlE,GAAAkE,EAAA9D,SAAAzR,EAAA,CAMA,IAAA6F,KAAAyY,GAAAzY,KAAA4Y,YAAAlJ,EAAApG,IACA,MAAAtL,MAAA,gBAAA0R,EAAApG,GAAA,OAAAtJ,MACA,GAAAA,KAAAoL,aAAAsE,EAAApG,IACA,MAAAtL,MAAA,MAAA0R,EAAApG,GAAA,mBAAAtJ,MACA,GAAAA,KAAAqL,eAAAqE,EAAAjV,MACA,MAAAuD,MAAA,SAAA0R,EAAAjV,KAAA,oBAAAuF,MAOA,OALA0P,EAAAhD,QACAgD,EAAAhD,OAAAnB,OAAAmE,IACA1P,KAAA6H,OAAA6H,EAAAjV,MAAAiV,GACA3D,QAAA/L,KACA0P,EAAAwB,MAAAlR,MACAuQ,EAAAvQ,MAEA,OAAA0P,aAAAtB,GACApO,KAAAuY,SACAvY,KAAAuY,OAAA,KACAvY,KAAAuY,OAAA7I,EAAAjV,MAAAiV,GACAwB,MAAAlR,MACAuQ,EAAAvQ,OAEAmK,EAAAjK,UAAA+K,IAAAtQ,KAAAqF,KAAA0P,IAUAjE,EAAAvL,UAAAqL,OAAA,SAAAmE,GACA,GAAAA,aAAAlE,GAAAkE,EAAA9D,SAAAzR,EAAA,CAIA,GAAA6F,KAAA6H,QAAA7H,KAAA6H,OAAA6H,EAAAjV,QAAAiV,EAMA,cAHA1P,KAAA6H,OAAA6H,EAAAjV,MACAiV,EAAAhD,OAAA,KACAgD,EAAAyB,SAAAnR,MACAuQ,EAAAvQ,MALA,MAAAhC,MAAA0R,EAAA,uBAAA1P,MAOA,GAAA0P,aAAAtB,EAAA,CAGA,GAAApO,KAAAuY,QAAAvY,KAAAuY,OAAA7I,EAAAjV,QAAAiV,EAMA,cAHA1P,KAAAuY,OAAA7I,EAAAjV,MACAiV,EAAAhD,OAAA,KACAgD,EAAAyB,SAAAnR,MACAuQ,EAAAvQ,MALA,MAAAhC,MAAA0R,EAAA,uBAAA1P,MAOA,OAAAmK,EAAAjK,UAAAqL,OAAA5Q,KAAAqF,KAAA0P,IAQAjE,EAAAvL,UAAAkL,aAAA,SAAA9B,GACA,OAAAa,EAAAiB,aAAApL,KAAA0K,SAAApB,IAQAmC,EAAAvL,UAAAmL,eAAA,SAAA5Q,GACA,OAAA0P,EAAAkB,eAAArL,KAAA0K,SAAAjQ,IAQAgR,EAAAvL,UAAAkK,OAAA,SAAA+E,GACA,OAAA,IAAAnP,KAAAiN,KAAAkC,IAOA1D,EAAAvL,UAAA8Y,MAAA,WAMA,IAFA,IAAA1R,EAAAtH,KAAAsH,SACAiC,EAAA,GACA1M,EAAA,EAAAA,EAAAmD,KAAA8H,YAAAlM,SAAAiB,EACA0M,EAAAhM,KAAAyC,KAAAiJ,EAAApM,GAAAZ,UAAAiL,cAGAlH,KAAAlD,OAAAmR,EAAAjO,KAAAiO,CAAA,CACAS,OAAAA,EACAnF,MAAAA,EACA1O,KAAAA,IAEAmF,KAAAnC,OAAAqQ,EAAAlO,KAAAkO,CAAA,CACAU,OAAAA,EACArF,MAAAA,EACA1O,KAAAA,IAEAmF,KAAAyP,OAAAtB,EAAAnO,KAAAmO,CAAA,CACA5E,MAAAA,EACA1O,KAAAA,IAEAmF,KAAA2H,WAAAD,EAAAC,WAAA3H,KAAA0H,CAAA,CACA6B,MAAAA,EACA1O,KAAAA,IAEAmF,KAAAiI,SAAAP,EAAAO,SAAAjI,KAAA0H,CAAA,CACA6B,MAAAA,EACA1O,KAAAA,IAIA,IAEAoe,EAFAC,EAAAzK,EAAAnH,GAaA,OAZA4R,KACAD,EAAAna,OAAAsL,OAAApK,OAEA2H,WAAA3H,KAAA2H,WACA3H,KAAA2H,WAAAuR,EAAAvR,WAAAlD,KAAAwU,GAGAA,EAAAhR,SAAAjI,KAAAiI,SACAjI,KAAAiI,SAAAiR,EAAAjR,SAAAxD,KAAAwU,IAIAjZ,MASAyL,EAAAvL,UAAApD,OAAA,SAAAiP,EAAAsD,GACA,OAAArP,KAAAgZ,QAAAlc,OAAAiP,EAAAsD,IASA5D,EAAAvL,UAAAoP,gBAAA,SAAAvD,EAAAsD,GACA,OAAArP,KAAAlD,OAAAiP,EAAAsD,GAAAA,EAAA9I,IAAA8I,EAAA8J,OAAA9J,GAAA+J,UAWA3N,EAAAvL,UAAArC,OAAA,SAAA0R,EAAA3T,GACA,OAAAoE,KAAAgZ,QAAAnb,OAAA0R,EAAA3T,IAUA6P,EAAAvL,UAAAsP,gBAAA,SAAAD,GAGA,OAFAA,aAAAX,IACAW,EAAAX,EAAAxE,OAAAmF,IACAvP,KAAAnC,OAAA0R,EAAAA,EAAA0E,WAQAxI,EAAAvL,UAAAuP,OAAA,SAAA1D,GACA,OAAA/L,KAAAgZ,QAAAvJ,OAAA1D,IAQAN,EAAAvL,UAAAyH,WAAA,SAAA+H,GACA,OAAA1P,KAAAgZ,QAAArR,WAAA+H,IA4BAjE,EAAAvL,UAAA+H,SAAA,SAAA8D,EAAAhL,GACA,OAAAf,KAAAgZ,QAAA/Q,SAAA8D,EAAAhL,IAkBA0K,EAAAyB,EAAA,SAAAmM,GACA,OAAA,SAAAC,GACAze,EAAAyS,aAAAgM,EAAAD,M,iHCpkBA,IAEAxe,EAAAS,EAAA,IAEAgd,EAAA,CACA,SACA,QACA,QACA,SACA,SACA,UACA,WACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,SAGA,SAAAiB,EAAApS,EAAAtL,GACA,IAAAgB,EAAA,EAAA2c,EAAA,GAEA,IADA3d,GAAA,EACAgB,EAAAsK,EAAAvL,QAAA4d,EAAAlB,EAAAzb,EAAAhB,IAAAsL,EAAAtK,KACA,OAAA2c,EAuBAjQ,EAAAG,MAAA6P,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,IAwBAhQ,EAAAC,SAAA+P,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GACA,EACA,GACA1e,EAAAmS,WACA,OAaAzD,EAAAb,KAAA6Q,EAAA,CACA,EACA,EACA,EACA,EACA,GACA,GAmBAhQ,EAAAQ,OAAAwP,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GACA,GAoBAhQ,EAAAI,OAAA4P,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,K,6BC5LA,IAIA9N,EACA7E,EALA/L,EAAAO,EAAAR,QAAAU,EAAA,IAEAyT,EAAAzT,EAAA,IAiDAme,GA5CA5e,EAAAqD,QAAA5C,EAAA,GACAT,EAAA6F,MAAApF,EAAA,GACAT,EAAA2K,KAAAlK,EAAA,GAMAT,EAAA+F,GAAA/F,EAAAgG,QAAA,MAOAhG,EAAA6V,QAAA,SAAAhB,GACA,GAAAA,EAAA,CAIA,IAHA,IAAA3Q,EAAAD,OAAAC,KAAA2Q,GACAS,EAAAzU,MAAAqD,EAAAnD,QACAE,EAAA,EACAA,EAAAiD,EAAAnD,QACAuU,EAAArU,GAAA4T,EAAA3Q,EAAAjD,MACA,OAAAqU,EAEA,MAAA,IAQAtV,EAAAoN,SAAA,SAAAkI,GAGA,IAFA,IAAAT,EAAA,GACA5T,EAAA,EACAA,EAAAqU,EAAAvU,QAAA,CACA,IAAA8d,EAAAvJ,EAAArU,KACAqG,EAAAgO,EAAArU,KACAqG,IAAAhI,IACAuV,EAAAgK,GAAAvX,GAEA,OAAAuN,GAGA,OACAiK,EAAA,KA+BAC,GAxBA/e,EAAAsd,WAAA,SAAA1d,GACA,MAAA,uTAAAwD,KAAAxD,IAQAI,EAAAkN,SAAA,SAAAd,GACA,OAAA,YAAAhJ,KAAAgJ,IAAApM,EAAAsd,WAAAlR,GACA,KAAAA,EAAA3H,QAAAma,EAAA,QAAAna,QAAAqa,EAAA,OAAA,KACA,IAAA1S,GAQApM,EAAAgf,QAAA,SAAAC,GACA,OAAAA,EAAA,IAAAA,IAAAC,cAAAD,EAAA3D,UAAA,IAGA,aAuDA6D,GAhDAnf,EAAAof,UAAA,SAAAH,GACA,OAAAA,EAAA3D,UAAA,EAAA,GACA2D,EAAA3D,UAAA,GACA7W,QAAAsa,EAAA,SAAAra,EAAAC,GAAA,OAAAA,EAAAua,iBASAlf,EAAAsN,kBAAA,SAAA+R,EAAA5c,GACA,OAAA4c,EAAA5Q,GAAAhM,EAAAgM,IAWAzO,EAAAyS,aAAA,SAAAL,EAAAoM,GAGA,GAAApM,EAAAmC,MAMA,OALAiK,GAAApM,EAAAmC,MAAA3U,OAAA4e,IACAxe,EAAAsf,aAAA5O,OAAA0B,EAAAmC,OACAnC,EAAAmC,MAAA3U,KAAA4e,EACAxe,EAAAsf,aAAAlP,IAAAgC,EAAAmC,QAEAnC,EAAAmC,MAOA5H,EAAA,IAFAiE,EADAA,GACAnQ,EAAA,KAEA+d,GAAApM,EAAAxS,MAKA,OAJAI,EAAAsf,aAAAlP,IAAAzD,GACAA,EAAAyF,KAAAA,EACAnO,OAAAsN,eAAAa,EAAA,QAAA,CAAAxN,MAAA+H,EAAA4S,YAAA,IACAtb,OAAAsN,eAAAa,EAAA/M,UAAA,QAAA,CAAAT,MAAA+H,EAAA4S,YAAA,IACA5S,GAGA,GAOA3M,EAAA0S,aAAA,SAAAmC,GAGA,GAAAA,EAAAN,MACA,OAAAM,EAAAN,MAMA,IAAAvE,EAAA,IAFAjE,EADAA,GACAtL,EAAA,KAEA,OAAA0e,IAAAtK,GAGA,OAFA7U,EAAAsf,aAAAlP,IAAAJ,GACA/L,OAAAsN,eAAAsD,EAAA,QAAA,CAAAjQ,MAAAoL,EAAAuP,YAAA,IACAvP,GAWAhQ,EAAA8X,YAAA,SAAA0H,EAAA7U,EAAA/F,GAiBA,GAAA,iBAAA4a,EACA,MAAA5P,UAAA,yBACA,GAAAjF,EAIA,OAtBA,SAAA8U,EAAAD,EAAA7U,EAAA/F,GACA,IAAA6R,EAAA9L,EAAAK,QACA,MAAA,cAAAyL,GAAA,cAAAA,IAGA,EAAA9L,EAAA5J,OACAye,EAAA/I,GAAAgJ,EAAAD,EAAA/I,IAAA,GAAA9L,EAAA/F,KAEA8a,EAAAF,EAAA/I,MAEA7R,EAAA,GAAA+a,OAAAD,GAAAC,OAAA/a,IACA4a,EAAA/I,GAAA7R,IARA4a,EAmBAC,CAAAD,EADA7U,EAAAA,EAAAE,MAAA,KACAjG,GAHA,MAAAgL,UAAA,2BAYA3L,OAAAsN,eAAAvR,EAAA,eAAA,CACAwR,IAAA,WACA,OAAA0C,EAAA,YAAAA,EAAA,UAAA,IAAAzT,EAAA,U,iEChNAF,EAAAR,QAAAwY,EAEA,IAAAvY,EAAAS,EAAA,IAUA,SAAA8X,EAAAtP,EAAAC,GASA/D,KAAA8D,GAAAA,IAAA,EAMA9D,KAAA+D,GAAAA,IAAA,EAQA,IAAA0W,EAAArH,EAAAqH,KAAA,IAAArH,EAAA,EAAA,GAoFArV,GAlFA0c,EAAA3R,SAAA,WAAA,OAAA,GACA2R,EAAAC,SAAAD,EAAAzF,SAAA,WAAA,OAAAhV,MACAya,EAAA7e,OAAA,WAAA,OAAA,GAOAwX,EAAAuH,SAAA,mBAOAvH,EAAAxG,WAAA,SAAAnN,GACA,GAAA,IAAAA,EACA,OAAAgb,EACA,IAAAnY,EAAA7C,EAAA,EAGAqE,GADArE,EADA6C,GACA7C,EACAA,KAAA,EACAsE,GAAAtE,EAAAqE,GAAA,aAAA,EAUA,OATAxB,IACAyB,GAAAA,IAAA,EACAD,GAAAA,IAAA,EACA,aAAAA,IACAA,EAAA,EACA,aAAAC,IACAA,EAAA,KAGA,IAAAqP,EAAAtP,EAAAC,IAQAqP,EAAAwH,KAAA,SAAAnb,GACA,GAAA,iBAAAA,EACA,OAAA2T,EAAAxG,WAAAnN,GACA,GAAA5E,EAAAqQ,SAAAzL,GAAA,CAEA,IAAA5E,EAAAI,KAGA,OAAAmY,EAAAxG,WAAAiO,SAAApb,EAAA,KAFAA,EAAA5E,EAAAI,KAAA6f,WAAArb,GAIA,OAAAA,EAAAkJ,KAAAlJ,EAAAmJ,KAAA,IAAAwK,EAAA3T,EAAAkJ,MAAA,EAAAlJ,EAAAmJ,OAAA,GAAA6R,GAQArH,EAAAlT,UAAA4I,SAAA,SAAAD,GACA,IAEA9E,EAFA,OAAA8E,GAAA7I,KAAA+D,KAAA,IACAD,EAAA,GAAA9D,KAAA8D,KAAA,EACAC,GAAA/D,KAAA+D,KAAA,IAGAD,EAAA,YADAC,EADAD,EAEAC,EADAA,EAAA,IAAA,KAGA/D,KAAA8D,GAAA,WAAA9D,KAAA+D,IAQAqP,EAAAlT,UAAA6a,OAAA,SAAAlS,GACA,OAAAhO,EAAAI,KACA,IAAAJ,EAAAI,KAAA,EAAA+E,KAAA8D,GAAA,EAAA9D,KAAA+D,KAAA8E,GAEA,CAAAF,IAAA,EAAA3I,KAAA8D,GAAA8E,KAAA,EAAA5I,KAAA+D,GAAA8E,WAAAA,IAGArL,OAAA0C,UAAAnC,YAOAqV,EAAA4H,SAAA,SAAAC,GACA,MAjFA7H,qBAiFA6H,EACAR,EACA,IAAArH,GACArV,EAAApD,KAAAsgB,EAAA,GACAld,EAAApD,KAAAsgB,EAAA,IAAA,EACAld,EAAApD,KAAAsgB,EAAA,IAAA,GACAld,EAAApD,KAAAsgB,EAAA,IAAA,MAAA,GAEAld,EAAApD,KAAAsgB,EAAA,GACAld,EAAApD,KAAAsgB,EAAA,IAAA,EACAld,EAAApD,KAAAsgB,EAAA,IAAA,GACAld,EAAApD,KAAAsgB,EAAA,IAAA,MAAA,IAQA7H,EAAAlT,UAAAgb,OAAA,WACA,OAAA1d,OAAAC,aACA,IAAAuC,KAAA8D,GACA9D,KAAA8D,KAAA,EAAA,IACA9D,KAAA8D,KAAA,GAAA,IACA9D,KAAA8D,KAAA,GACA,IAAA9D,KAAA+D,GACA/D,KAAA+D,KAAA,EAAA,IACA/D,KAAA+D,KAAA,GAAA,IACA/D,KAAA+D,KAAA,KAQAqP,EAAAlT,UAAAwa,SAAA,WACA,IAAAS,EAAAnb,KAAA+D,IAAA,GAGA,OAFA/D,KAAA+D,KAAA/D,KAAA+D,IAAA,EAAA/D,KAAA8D,KAAA,IAAAqX,KAAA,EACAnb,KAAA8D,IAAA9D,KAAA8D,IAAA,EAAAqX,KAAA,EACAnb,MAOAoT,EAAAlT,UAAA8U,SAAA,WACA,IAAAmG,IAAA,EAAAnb,KAAA8D,IAGA,OAFA9D,KAAA8D,KAAA9D,KAAA8D,KAAA,EAAA9D,KAAA+D,IAAA,IAAAoX,KAAA,EACAnb,KAAA+D,IAAA/D,KAAA+D,KAAA,EAAAoX,KAAA,EACAnb,MAOAoT,EAAAlT,UAAAtE,OAAA,WACA,IAAAwf,EAAApb,KAAA8D,GACAuX,GAAArb,KAAA8D,KAAA,GAAA9D,KAAA+D,IAAA,KAAA,EACAuX,EAAAtb,KAAA+D,KAAA,GACA,OAAA,GAAAuX,EACA,GAAAD,EACAD,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,IAAA,EAAA,K,6BCrMA,IAAAzgB,EAAAD,EA2OA,SAAAga,EAAAyF,EAAAkB,EAAA/O,GACA,IAAA,IAAAzN,EAAAD,OAAAC,KAAAwc,GAAA1e,EAAA,EAAAA,EAAAkC,EAAAnD,SAAAiB,EACAwd,EAAAtb,EAAAlC,MAAA1C,GAAAqS,IACA6N,EAAAtb,EAAAlC,IAAA0e,EAAAxc,EAAAlC,KACA,OAAAwd,EAoBA,SAAAmB,EAAA/gB,GAEA,SAAAghB,EAAA1P,EAAAoD,GAEA,KAAAnP,gBAAAyb,GACA,OAAA,IAAAA,EAAA1P,EAAAoD,GAKArQ,OAAAsN,eAAApM,KAAA,UAAA,CAAAqM,IAAA,WAAA,OAAAN,KAGA/N,MAAA0d,kBACA1d,MAAA0d,kBAAA1b,KAAAyb,GAEA3c,OAAAsN,eAAApM,KAAA,QAAA,CAAAP,MAAAzB,QAAA2d,OAAA,KAEAxM,GACAyF,EAAA5U,KAAAmP,GAWA,OARAsM,EAAAvb,UAAApB,OAAAsL,OAAApM,MAAAkC,YAAAmK,YAAAoR,EAEA3c,OAAAsN,eAAAqP,EAAAvb,UAAA,OAAA,CAAAmM,IAAA,WAAA,OAAA5R,KAEAghB,EAAAvb,UAAAzB,SAAA,WACA,OAAAuB,KAAAvF,KAAA,KAAAuF,KAAA+L,SAGA0P,EA9RA5gB,EAAA8F,UAAArF,EAAA,GAGAT,EAAAwB,OAAAf,EAAA,GAGAT,EAAAkF,aAAAzE,EAAA,GAGAT,EAAA0Z,MAAAjZ,EAAA,GAGAT,EAAAgG,QAAAvF,EAAA,GAGAT,EAAAyL,KAAAhL,EAAA,IAGAT,EAAA+gB,KAAAtgB,EAAA,GAGAT,EAAAuY,SAAA9X,EAAA,IAOAT,EAAA+b,UAAA,oBAAA9b,QACAA,QACAA,OAAAsb,SACAtb,OAAAsb,QAAAyF,UACA/gB,OAAAsb,QAAAyF,SAAAC,MAOAjhB,EAAAC,OAAAD,EAAA+b,QAAA9b,QACA,oBAAAihB,QAAAA,QACA,oBAAApG,MAAAA,MACA3V,KAQAnF,EAAAmS,WAAAlO,OAAA+N,OAAA/N,OAAA+N,OAAA,IAAA,GAOAhS,EAAAkS,YAAAjO,OAAA+N,OAAA/N,OAAA+N,OAAA,IAAA,GAQAhS,EAAAsQ,UAAAzL,OAAAyL,WAAA,SAAA1L,GACA,MAAA,iBAAAA,GAAAuc,SAAAvc,IAAAhD,KAAAkD,MAAAF,KAAAA,GAQA5E,EAAAqQ,SAAA,SAAAzL,GACA,MAAA,iBAAAA,GAAAA,aAAAjC,QAQA3C,EAAAgR,SAAA,SAAApM,GACA,OAAAA,GAAA,iBAAAA,GAWA5E,EAAAohB,MAQAphB,EAAAqhB,MAAA,SAAA9L,EAAAnJ,GACA,IAAAxH,EAAA2Q,EAAAnJ,GACA,OAAA,MAAAxH,GAAA2Q,EAAAqC,eAAAxL,KACA,iBAAAxH,GAAA,GAAA/D,MAAA0V,QAAA3R,GAAAA,EAAAX,OAAAC,KAAAU,IAAA7D,SAeAf,EAAA2Y,OAAA,WACA,IACA,IAAAA,EAAA3Y,EAAAgG,QAAA,UAAA2S,OAEA,OAAAA,EAAAtT,UAAAic,UAAA3I,EAAA,KACA,MAAAlO,GAEA,OAAA,MAPA,GAYAzK,EAAAuhB,EAAA,KAGAvhB,EAAAwhB,EAAA,KAOAxhB,EAAAiS,UAAA,SAAAwP,GAEA,MAAA,iBAAAA,EACAzhB,EAAA2Y,OACA3Y,EAAAwhB,EAAAC,GACA,IAAAzhB,EAAAa,MAAA4gB,GACAzhB,EAAA2Y,OACA3Y,EAAAuhB,EAAAE,GACA,oBAAA3a,WACA2a,EACA,IAAA3a,WAAA2a,IAOAzhB,EAAAa,MAAA,oBAAAiG,WAAAA,WAAAjG,MAeAb,EAAAI,KAAAJ,EAAAC,OAAAyhB,SAAA1hB,EAAAC,OAAAyhB,QAAAthB,MACAJ,EAAAC,OAAAG,MACAJ,EAAAgG,QAAA,QAOAhG,EAAA2hB,OAAA,mBAOA3hB,EAAA4hB,QAAA,wBAOA5hB,EAAA6hB,QAAA,6CAOA7hB,EAAA8hB,WAAA,SAAAld,GACA,OAAAA,EACA5E,EAAAuY,SAAAwH,KAAAnb,GAAAyb,SACArgB,EAAAuY,SAAAuH,UASA9f,EAAA+hB,aAAA,SAAA3B,EAAApS,GACA+K,EAAA/Y,EAAAuY,SAAA4H,SAAAC,GACA,OAAApgB,EAAAI,KACAJ,EAAAI,KAAA4hB,SAAAjJ,EAAA9P,GAAA8P,EAAA7P,GAAA8E,GACA+K,EAAA9K,WAAAD,IAkBAhO,EAAA+Z,MAAAA,EAOA/Z,EAAAqd,QAAA,SAAA4B,GACA,OAAAA,EAAA,IAAAA,IAAAhO,cAAAgO,EAAA3D,UAAA,IA0CAtb,EAAA2gB,SAAAA,EAmBA3gB,EAAAiiB,cAAAtB,EAAA,iBAoBA3gB,EAAAoY,YAAA,SAAAJ,GAEA,IADA,IAAAkK,EAAA,GACAlgB,EAAA,EAAAA,EAAAgW,EAAAjX,SAAAiB,EACAkgB,EAAAlK,EAAAhW,IAAA,EAOA,OAAA,WACA,IAAA,IAAAkC,EAAAD,OAAAC,KAAAiB,MAAAnD,EAAAkC,EAAAnD,OAAA,GAAA,EAAAiB,IAAAA,EACA,GAAA,IAAAkgB,EAAAhe,EAAAlC,KAAAmD,KAAAjB,EAAAlC,MAAA1C,GAAA,OAAA6F,KAAAjB,EAAAlC,IACA,OAAAkC,EAAAlC,KAiBAhC,EAAAsY,YAAA,SAAAN,GAQA,OAAA,SAAApY,GACA,IAAA,IAAAoC,EAAA,EAAAA,EAAAgW,EAAAjX,SAAAiB,EACAgW,EAAAhW,KAAApC,UACAuF,KAAA6S,EAAAhW,MAoBAhC,EAAAkQ,cAAA,CACAiS,MAAAxf,OACAyf,MAAAzf,OACAuL,MAAAvL,OACAoN,MAAA,GAIA/P,EAAA6S,EAAA,WACA,IAAA8F,EAAA3Y,EAAA2Y,OAEAA,GAMA3Y,EAAAuhB,EAAA5I,EAAAoH,OAAAjZ,WAAAiZ,MAAApH,EAAAoH,MAEA,SAAAnb,EAAAyd,GACA,OAAA,IAAA1J,EAAA/T,EAAAyd,IAEAriB,EAAAwhB,EAAA7I,EAAA2J,aAEA,SAAAjX,GACA,OAAA,IAAAsN,EAAAtN,KAbArL,EAAAuhB,EAAAvhB,EAAAwhB,EAAA,O,2DCpZAjhB,EAAAR,QAwHA,SAAAgN,GAGA,IAAAd,EAAAjM,EAAAqD,QAAA,CAAA,KAAA0J,EAAAnN,KAAA,UAAAI,CACA,oCADAA,CAEA,WAAA,mBACA0d,EAAA3Q,EAAAiR,YACAuE,EAAA,GACA7E,EAAA3c,QAAAkL,EACA,YAEA,IAAA,IAAAjK,EAAA,EAAAA,EAAA+K,EAAAE,YAAAlM,SAAAiB,EAAA,CACA,IA2BAwgB,EA3BAtW,EAAAa,EAAAqB,EAAApM,GAAAZ,UACAoN,EAAA,IAAAxO,EAAAkN,SAAAhB,EAAAtM,MAEAsM,EAAAkD,UAAAnD,EACA,sCAAAuC,EAAAtC,EAAAtM,MAGAsM,EAAAiB,KAAAlB,EACA,yBAAAuC,EADAvC,CAEA,WAAAwW,EAAAvW,EAAA,UAFAD,CAGA,wBAAAuC,EAHAvC,CAIA,gCAxDA,SAAAA,EAAAC,EAAAsC,GAEA,OAAAtC,EAAA0C,SACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAA3C,EACA,6BAAAuC,EADAvC,CAEA,WAAAwW,EAAAvW,EAAA,gBACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,6BAAAuC,EADAvC,CAEA,WAAAwW,EAAAvW,EAAA,qBACA,MACA,IAAA,OAAAD,EACA,4BAAAuC,EADAvC,CAEA,WAAAwW,EAAAvW,EAAA,iBAoCAwW,CAAAzW,EAAAC,EAAA,QACAyW,EAAA1W,EAAAC,EAAAlK,EAAAwM,EAAA,SAAAmU,CACA,MAGAzW,EAAAK,UAAAN,EACA,yBAAAuC,EADAvC,CAEA,WAAAwW,EAAAvW,EAAA,SAFAD,CAGA,gCAAAuC,GACAmU,EAAA1W,EAAAC,EAAAlK,EAAAwM,EAAA,MAAAmU,CACA,OAIAzW,EAAAwB,SACA8U,EAAAxiB,EAAAkN,SAAAhB,EAAAwB,OAAA9N,MACA,IAAA2iB,EAAArW,EAAAwB,OAAA9N,OAAAqM,EACA,cAAAuW,EADAvW,CAEA,WAAAC,EAAAwB,OAAA9N,KAAA,qBACA2iB,EAAArW,EAAAwB,OAAA9N,MAAA,EACAqM,EACA,QAAAuW,IAEAG,EAAA1W,EAAAC,EAAAlK,EAAAwM,IAEAtC,EAAAkD,UAAAnD,EACA,KAEA,OAAAA,EACA,gBA3KA,IAAAF,EAAAtL,EAAA,IACAT,EAAAS,EAAA,IAEA,SAAAgiB,EAAAvW,EAAA0W,GACA,OAAA1W,EAAAtM,KAAA,KAAAgjB,GAAA1W,EAAAK,UAAA,UAAAqW,EAAA,KAAA1W,EAAAiB,KAAA,WAAAyV,EAAA,MAAA1W,EAAA0C,QAAA,IAAA,IAAA,YAYA,SAAA+T,EAAA1W,EAAAC,EAAAC,EAAAqC,GAEA,GAAAtC,EAAAG,aACA,GAAAH,EAAAG,wBAAAN,EAAA,CAAAE,EACA,cAAAuC,EADAvC,CAEA,WAFAA,CAGA,WAAAwW,EAAAvW,EAAA,eACA,IAAA,IAAAhI,EAAAD,OAAAC,KAAAgI,EAAAG,aAAAC,QAAA9J,EAAA,EAAAA,EAAA0B,EAAAnD,SAAAyB,EAAAyJ,EACA,WAAAC,EAAAG,aAAAC,OAAApI,EAAA1B,KACAyJ,EACA,QADAA,CAEA,UAEAA,EACA,IADAA,CAEA,8BAAAE,EAAAqC,EAFAvC,CAGA,QAHAA,CAIA,aAAAC,EAAAtM,KAAA,IAJAqM,CAKA,UAGA,OAAAC,EAAAS,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAV,EACA,0BAAAuC,EADAvC,CAEA,WAAAwW,EAAAvW,EAAA,YACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,kFAAAuC,EAAAA,EAAAA,EAAAA,EADAvC,CAEA,WAAAwW,EAAAvW,EAAA,iBACA,MACA,IAAA,QACA,IAAA,SAAAD,EACA,2BAAAuC,EADAvC,CAEA,WAAAwW,EAAAvW,EAAA,WACA,MACA,IAAA,OAAAD,EACA,4BAAAuC,EADAvC,CAEA,WAAAwW,EAAAvW,EAAA,YACA,MACA,IAAA,SAAAD,EACA,yBAAAuC,EADAvC,CAEA,WAAAwW,EAAAvW,EAAA,WACA,MACA,IAAA,QAAAD,EACA,4DAAAuC,EAAAA,EAAAA,EADAvC,CAEA,WAAAwW,EAAAvW,EAAA,WAIA,OAAAD,I,mCCrEA,IAEA0H,EAAAlT,EAAA,IA6BAmT,EAAA,wBAAA,CAEA9G,WAAA,SAAA+H,GAGA,GAAAA,GAAAA,EAAA,SAAA,CAEA,IAKAgO,EALAjjB,EAAAiV,EAAA,SAAAyG,UAAA,EAAAzG,EAAA,SAAAuG,YAAA,MACAzO,EAAAxH,KAAAwR,OAAA/W,GAEA,GAAA+M,EAQA,QANAkW,EAAA,MAAAhO,EAAA,SAAA,IAAAA,IACAA,EAAA,SAAAiO,OAAA,GAAAjO,EAAA,UAEAxG,QAAA,OACAwU,EAAA,IAAAA,GAEA1d,KAAAoK,OAAA,CACAsT,SAAAA,EACAje,MAAA+H,EAAA1K,OAAA0K,EAAAG,WAAA+H,IAAAmG,WAKA,OAAA7V,KAAA2H,WAAA+H,IAGAzH,SAAA,SAAA8D,EAAAhL,GAGA,IAkBA2O,EACAkO,EAlBAhY,EAAA,GACAnL,EAAA,GAeA,OAZAsG,GAAAA,EAAA6J,MAAAmB,EAAA2R,UAAA3R,EAAAtM,QAEAhF,EAAAsR,EAAA2R,SAAAvH,UAAA,EAAApK,EAAA2R,SAAAzH,YAAA,MAEArQ,EAAAmG,EAAA2R,SAAAvH,UAAA,EAAA,EAAApK,EAAA2R,SAAAzH,YAAA,OACAzO,EAAAxH,KAAAwR,OAAA/W,MAGAsR,EAAAvE,EAAA3J,OAAAkO,EAAAtM,WAIAsM,aAAA/L,KAAAiN,OAAAlB,aAAAyC,GACAkB,EAAA3D,EAAAqD,MAAAnH,SAAA8D,EAAAhL,GACA6c,EAAA,MAAA7R,EAAAqD,MAAA9H,SAAA,GACAyE,EAAAqD,MAAA9H,SAAAqW,OAAA,GAAA5R,EAAAqD,MAAA9H,SAMAoI,EAAA,SADAjV,GAFAmL,EADA,KAAAA,EAtBA,uBAyBAA,GAAAgY,EAEAlO,GAGA1P,KAAAiI,SAAA8D,EAAAhL,M,6BClGA3F,EAAAR,QAAA8T,EAEA,IAEAC,EAFA9T,EAAAS,EAAA,IAIA8X,EAAAvY,EAAAuY,SACA/W,EAAAxB,EAAAwB,OACAiK,EAAAzL,EAAAyL,KAWA,SAAAuX,EAAAtiB,EAAAgL,EAAApE,GAMAnC,KAAAzE,GAAAA,EAMAyE,KAAAuG,IAAAA,EAMAvG,KAAA8d,KAAA3jB,EAMA6F,KAAAmC,IAAAA,EAIA,SAAA4b,KAUA,SAAAC,EAAA3O,GAMArP,KAAAie,KAAA5O,EAAA4O,KAMAje,KAAAke,KAAA7O,EAAA6O,KAMAle,KAAAuG,IAAA8I,EAAA9I,IAMAvG,KAAA8d,KAAAzO,EAAA8O,OAQA,SAAAzP,IAMA1O,KAAAuG,IAAA,EAMAvG,KAAAie,KAAA,IAAAJ,EAAAE,EAAA,EAAA,GAMA/d,KAAAke,KAAAle,KAAAie,KAMAje,KAAAme,OAAA,KASA,SAAA/T,IACA,OAAAvP,EAAA2Y,OACA,WACA,OAAA9E,EAAAtE,OAAA,WACA,OAAA,IAAAuE,OAIA,WACA,OAAA,IAAAD,GAuCA,SAAA0P,EAAAjc,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EAoBA,SAAAkc,EAAA9X,EAAApE,GACAnC,KAAAuG,IAAAA,EACAvG,KAAA8d,KAAA3jB,EACA6F,KAAAmC,IAAAA,EA8CA,SAAAmc,EAAAnc,EAAAC,EAAAC,GACA,KAAAF,EAAA4B,IACA3B,EAAAC,KAAA,IAAAF,EAAA2B,GAAA,IACA3B,EAAA2B,IAAA3B,EAAA2B,KAAA,EAAA3B,EAAA4B,IAAA,MAAA,EACA5B,EAAA4B,MAAA,EAEA,KAAA,IAAA5B,EAAA2B,IACA1B,EAAAC,KAAA,IAAAF,EAAA2B,GAAA,IACA3B,EAAA2B,GAAA3B,EAAA2B,KAAA,EAEA1B,EAAAC,KAAAF,EAAA2B,GA2CA,SAAAya,EAAApc,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GA7JAuM,EAAAtE,OAAAA,IAOAsE,EAAAzI,MAAA,SAAAC,GACA,OAAA,IAAArL,EAAAa,MAAAwK,IAKArL,EAAAa,QAAAA,QACAgT,EAAAzI,MAAApL,EAAA+gB,KAAAlN,EAAAzI,MAAApL,EAAAa,MAAAwE,UAAA8T,WAUAtF,EAAAxO,UAAAse,EAAA,SAAAjjB,EAAAgL,EAAApE,GAGA,OAFAnC,KAAAke,KAAAle,KAAAke,KAAAJ,KAAA,IAAAD,EAAAtiB,EAAAgL,EAAApE,GACAnC,KAAAuG,KAAAA,EACAvG,OA8BAqe,EAAAne,UAAApB,OAAAsL,OAAAyT,EAAA3d,YACA3E,GAxBA,SAAA4G,EAAAC,EAAAC,GACA,KAAA,IAAAF,GACAC,EAAAC,KAAA,IAAAF,EAAA,IACAA,KAAA,EAEAC,EAAAC,GAAAF,GA0BAuM,EAAAxO,UAAA+T,OAAA,SAAAxU,GAWA,OARAO,KAAAuG,MAAAvG,KAAAke,KAAAle,KAAAke,KAAAJ,KAAA,IAAAO,GACA5e,KAAA,GACA,IAAA,EACAA,EAAA,MAAA,EACAA,EAAA,QAAA,EACAA,EAAA,UAAA,EACA,EACAA,IAAA8G,IACAvG,MASA0O,EAAAxO,UAAAgU,MAAA,SAAAzU,GACA,OAAAA,EAAA,EACAO,KAAAwe,EAAAF,EAAA,GAAAlL,EAAAxG,WAAAnN,IACAO,KAAAiU,OAAAxU,IAQAiP,EAAAxO,UAAAiU,OAAA,SAAA1U,GACA,OAAAO,KAAAiU,QAAAxU,GAAA,EAAAA,GAAA,MAAA,IAkCAiP,EAAAxO,UAAA2U,MAZAnG,EAAAxO,UAAA4U,OAAA,SAAArV,GACAmU,EAAAR,EAAAwH,KAAAnb,GACA,OAAAO,KAAAwe,EAAAF,EAAA1K,EAAAhY,SAAAgY,IAkBAlF,EAAAxO,UAAA6U,OAAA,SAAAtV,GACAmU,EAAAR,EAAAwH,KAAAnb,GAAAib,WACA,OAAA1a,KAAAwe,EAAAF,EAAA1K,EAAAhY,SAAAgY,IAQAlF,EAAAxO,UAAAkU,KAAA,SAAA3U,GACA,OAAAO,KAAAwe,EAAAJ,EAAA,EAAA3e,EAAA,EAAA,IAyBAiP,EAAAxO,UAAAoU,SAVA5F,EAAAxO,UAAAmU,QAAA,SAAA5U,GACA,OAAAO,KAAAwe,EAAAD,EAAA,EAAA9e,IAAA,IA6BAiP,EAAAxO,UAAAgV,SAZAxG,EAAAxO,UAAA+U,QAAA,SAAAxV,GACAmU,EAAAR,EAAAwH,KAAAnb,GACA,OAAAO,KAAAwe,EAAAD,EAAA,EAAA3K,EAAA9P,IAAA0a,EAAAD,EAAA,EAAA3K,EAAA7P,KAkBA2K,EAAAxO,UAAAqU,MAAA,SAAA9U,GACA,OAAAO,KAAAwe,EAAA3jB,EAAA0Z,MAAAlQ,aAAA,EAAA5E,IASAiP,EAAAxO,UAAAsU,OAAA,SAAA/U,GACA,OAAAO,KAAAwe,EAAA3jB,EAAA0Z,MAAAxP,cAAA,EAAAtF,IAGA,IAAAgf,EAAA5jB,EAAAa,MAAAwE,UAAAgT,IACA,SAAA/Q,EAAAC,EAAAC,GACAD,EAAA8Q,IAAA/Q,EAAAE,IAGA,SAAAF,EAAAC,EAAAC,GACA,IAAA,IAAAxF,EAAA,EAAAA,EAAAsF,EAAAvG,SAAAiB,EACAuF,EAAAC,EAAAxF,GAAAsF,EAAAtF,IAQA6R,EAAAxO,UAAA6I,MAAA,SAAAtJ,GACA,IAIA2C,EAJAmE,EAAA9G,EAAA7D,SAAA,EACA,OAAA2K,GAEA1L,EAAAqQ,SAAAzL,KACA2C,EAAAsM,EAAAzI,MAAAM,EAAAlK,EAAAT,OAAA6D,IACApD,EAAAwB,OAAA4B,EAAA2C,EAAA,GACA3C,EAAA2C,GAEApC,KAAAiU,OAAA1N,GAAAiY,EAAAC,EAAAlY,EAAA9G,IANAO,KAAAwe,EAAAJ,EAAA,EAAA,IAcA1P,EAAAxO,UAAA5D,OAAA,SAAAmD,GACA,IAAA8G,EAAAD,EAAA1K,OAAA6D,GACA,OAAA8G,EACAvG,KAAAiU,OAAA1N,GAAAiY,EAAAlY,EAAAG,MAAAF,EAAA9G,GACAO,KAAAwe,EAAAJ,EAAA,EAAA,IAQA1P,EAAAxO,UAAAiZ,KAAA,WAIA,OAHAnZ,KAAAme,OAAA,IAAAH,EAAAhe,MACAA,KAAAie,KAAAje,KAAAke,KAAA,IAAAL,EAAAE,EAAA,EAAA,GACA/d,KAAAuG,IAAA,EACAvG,MAOA0O,EAAAxO,UAAAwe,MAAA,WAUA,OATA1e,KAAAme,QACAne,KAAAie,KAAAje,KAAAme,OAAAF,KACAje,KAAAke,KAAAle,KAAAme,OAAAD,KACAle,KAAAuG,IAAAvG,KAAAme,OAAA5X,IACAvG,KAAAme,OAAAne,KAAAme,OAAAL,OAEA9d,KAAAie,KAAAje,KAAAke,KAAA,IAAAL,EAAAE,EAAA,EAAA,GACA/d,KAAAuG,IAAA,GAEAvG,MAOA0O,EAAAxO,UAAAkZ,OAAA,WACA,IAAA6E,EAAAje,KAAAie,KACAC,EAAAle,KAAAke,KACA3X,EAAAvG,KAAAuG,IAOA,OANAvG,KAAA0e,QAAAzK,OAAA1N,GACAA,IACAvG,KAAAke,KAAAJ,KAAAG,EAAAH,KACA9d,KAAAke,KAAAA,EACAle,KAAAuG,KAAAA,GAEAvG,MAOA0O,EAAAxO,UAAA2V,OAAA,WAIA,IAHA,IAAAoI,EAAAje,KAAAie,KAAAH,KACA1b,EAAApC,KAAAqK,YAAApE,MAAAjG,KAAAuG,KACAlE,EAAA,EACA4b,GACAA,EAAA1iB,GAAA0iB,EAAA9b,IAAAC,EAAAC,GACAA,GAAA4b,EAAA1X,IACA0X,EAAAA,EAAAH,KAGA,OAAA1b,GAGAsM,EAAAhB,EAAA,SAAAiR,GACAhQ,EAAAgQ,EACAjQ,EAAAtE,OAAAA,IACAuE,EAAAjB,M,6BC9cAtS,EAAAR,QAAA+T,EAGA,IAAAD,EAAApT,EAAA,IAGAT,IAFA8T,EAAAzO,UAAApB,OAAAsL,OAAAsE,EAAAxO,YAAAmK,YAAAsE,EAEArT,EAAA,KAQA,SAAAqT,IACAD,EAAA/T,KAAAqF,MAwCA,SAAA4e,EAAAzc,EAAAC,EAAAC,GACAF,EAAAvG,OAAA,GACAf,EAAAyL,KAAAG,MAAAtE,EAAAC,EAAAC,GACAD,EAAA+Z,UACA/Z,EAAA+Z,UAAAha,EAAAE,GAEAD,EAAAqE,MAAAtE,EAAAE,GA3CAsM,EAAAjB,EAAA,WAOAiB,EAAA1I,MAAApL,EAAAwhB,EAEA1N,EAAAkQ,iBAAAhkB,EAAA2Y,QAAA3Y,EAAA2Y,OAAAtT,qBAAAyB,YAAA,QAAA9G,EAAA2Y,OAAAtT,UAAAgT,IAAAzY,KACA,SAAA0H,EAAAC,EAAAC,GACAD,EAAA8Q,IAAA/Q,EAAAE,IAIA,SAAAF,EAAAC,EAAAC,GACA,GAAAF,EAAA2c,KACA3c,EAAA2c,KAAA1c,EAAAC,EAAA,EAAAF,EAAAvG,aACA,IAAA,IAAAiB,EAAA,EAAAA,EAAAsF,EAAAvG,QACAwG,EAAAC,KAAAF,EAAAtF,OAQA8R,EAAAzO,UAAA6I,MAAA,SAAAtJ,GAGA,IAAA8G,GADA9G,EADA5E,EAAAqQ,SAAAzL,GACA5E,EAAAuhB,EAAA3c,EAAA,UACAA,GAAA7D,SAAA,EAIA,OAHAoE,KAAAiU,OAAA1N,GACAA,GACAvG,KAAAwe,EAAA7P,EAAAkQ,iBAAAtY,EAAA9G,GACAO,MAeA2O,EAAAzO,UAAA5D,OAAA,SAAAmD,GACA,IAAA8G,EAAA1L,EAAA2Y,OAAAuL,WAAAtf,GAIA,OAHAO,KAAAiU,OAAA1N,GACAA,GACAvG,KAAAwe,EAAAI,EAAArY,EAAA9G,GACAO,MAWA2O,EAAAjB,8BvCpFA", "file": "protobuf.min.js", "sourcesContent": ["(function prelude(modules, cache, entries) {\n\n    // This is the prelude used to bundle protobuf.js for the browser. Wraps up the CommonJS\n    // sources through a conflict-free require shim and is again wrapped within an iife that\n    // provides a minification-friendly `undefined` var plus a global \"use strict\" directive\n    // so that minification can remove the directives of each module.\n\n    function $require(name) {\n        var $module = cache[name];\n        if (!$module)\n            modules[name][0].call($module = cache[name] = { exports: {} }, $require, $module, $module.exports);\n        return $module.exports;\n    }\n\n    var protobuf = $require(entries[0]);\n\n    // Expose globally\n    protobuf.util.global.protobuf = protobuf;\n\n    // Be nice to AMD\n    if (typeof define === \"function\" && define.amd)\n        define([\"long\"], function(Long) {\n            if (Long && Long.isLong) {\n                protobuf.util.Long = Long;\n                protobuf.configure();\n            }\n            return protobuf;\n        });\n\n    // Be nice to CommonJS\n    if (typeof module === \"object\" && module && module.exports)\n        module.exports = protobuf;\n\n})/* end of prelude */", "\"use strict\";\r\nmodule.exports = asPromise;\r\n\r\n/**\r\n * Callback as used by {@link util.asPromise}.\r\n * @typedef asPromiseCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any\r\n * @param {...*} params Additional arguments\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Returns a promise from a node-style callback function.\r\n * @memberof util\r\n * @param {asPromiseCallback} fn Function to call\r\n * @param {*} ctx Function context\r\n * @param {...*} params Function arguments\r\n * @returns {Promise<*>} Promisified function\r\n */\r\nfunction asPromise(fn, ctx/*, varargs */) {\r\n    var params  = new Array(arguments.length - 1),\r\n        offset  = 0,\r\n        index   = 2,\r\n        pending = true;\r\n    while (index < arguments.length)\r\n        params[offset++] = arguments[index++];\r\n    return new Promise(function executor(resolve, reject) {\r\n        params[offset] = function callback(err/*, varargs */) {\r\n            if (pending) {\r\n                pending = false;\r\n                if (err)\r\n                    reject(err);\r\n                else {\r\n                    var params = new Array(arguments.length - 1),\r\n                        offset = 0;\r\n                    while (offset < params.length)\r\n                        params[offset++] = arguments[offset];\r\n                    resolve.apply(null, params);\r\n                }\r\n            }\r\n        };\r\n        try {\r\n            fn.apply(ctx || null, params);\r\n        } catch (err) {\r\n            if (pending) {\r\n                pending = false;\r\n                reject(err);\r\n            }\r\n        }\r\n    });\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal base64 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar base64 = exports;\r\n\r\n/**\r\n * Calculates the byte length of a base64 encoded string.\r\n * @param {string} string Base64 encoded string\r\n * @returns {number} Byte length\r\n */\r\nbase64.length = function length(string) {\r\n    var p = string.length;\r\n    if (!p)\r\n        return 0;\r\n    var n = 0;\r\n    while (--p % 4 > 1 && string.charAt(p) === \"=\")\r\n        ++n;\r\n    return Math.ceil(string.length * 3) / 4 - n;\r\n};\r\n\r\n// Base64 encoding table\r\nvar b64 = new Array(64);\r\n\r\n// Base64 decoding table\r\nvar s64 = new Array(123);\r\n\r\n// 65..90, 97..122, 48..57, 43, 47\r\nfor (var i = 0; i < 64;)\r\n    s64[b64[i] = i < 26 ? i + 65 : i < 52 ? i + 71 : i < 62 ? i - 4 : i - 59 | 43] = i++;\r\n\r\n/**\r\n * Encodes a buffer to a base64 encoded string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} Base64 encoded string\r\n */\r\nbase64.encode = function encode(buffer, start, end) {\r\n    var parts = null,\r\n        chunk = [];\r\n    var i = 0, // output index\r\n        j = 0, // goto index\r\n        t;     // temporary\r\n    while (start < end) {\r\n        var b = buffer[start++];\r\n        switch (j) {\r\n            case 0:\r\n                chunk[i++] = b64[b >> 2];\r\n                t = (b & 3) << 4;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                chunk[i++] = b64[t | b >> 4];\r\n                t = (b & 15) << 2;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                chunk[i++] = b64[t | b >> 6];\r\n                chunk[i++] = b64[b & 63];\r\n                j = 0;\r\n                break;\r\n        }\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (j) {\r\n        chunk[i++] = b64[t];\r\n        chunk[i++] = 61;\r\n        if (j === 1)\r\n            chunk[i++] = 61;\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\nvar invalidEncoding = \"invalid encoding\";\r\n\r\n/**\r\n * Decodes a base64 encoded string to a buffer.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Number of bytes written\r\n * @throws {Error} If encoding is invalid\r\n */\r\nbase64.decode = function decode(string, buffer, offset) {\r\n    var start = offset;\r\n    var j = 0, // goto index\r\n        t;     // temporary\r\n    for (var i = 0; i < string.length;) {\r\n        var c = string.charCodeAt(i++);\r\n        if (c === 61 && j > 1)\r\n            break;\r\n        if ((c = s64[c]) === undefined)\r\n            throw Error(invalidEncoding);\r\n        switch (j) {\r\n            case 0:\r\n                t = c;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                buffer[offset++] = t << 2 | (c & 48) >> 4;\r\n                t = c;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                buffer[offset++] = (t & 15) << 4 | (c & 60) >> 2;\r\n                t = c;\r\n                j = 3;\r\n                break;\r\n            case 3:\r\n                buffer[offset++] = (t & 3) << 6 | c;\r\n                j = 0;\r\n                break;\r\n        }\r\n    }\r\n    if (j === 1)\r\n        throw Error(invalidEncoding);\r\n    return offset - start;\r\n};\r\n\r\n/**\r\n * Tests if the specified string appears to be base64 encoded.\r\n * @param {string} string String to test\r\n * @returns {boolean} `true` if probably base64 encoded, otherwise false\r\n */\r\nbase64.test = function test(string) {\r\n    return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(string);\r\n};\r\n", "\"use strict\";\r\nmodule.exports = codegen;\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @param {string[]} functionParams Function parameter names\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n */\r\nfunction codegen(functionParams, functionName) {\r\n\r\n    /* istanbul ignore if */\r\n    if (typeof functionParams === \"string\") {\r\n        functionName = functionParams;\r\n        functionParams = undefined;\r\n    }\r\n\r\n    var body = [];\r\n\r\n    /**\r\n     * Appends code to the function's body or finishes generation.\r\n     * @typedef Codegen\r\n     * @type {function}\r\n     * @param {string|Object.<string,*>} [formatStringOrScope] Format string or, to finish the function, an object of additional scope variables, if any\r\n     * @param {...*} [formatParams] Format parameters\r\n     * @returns {Codegen|Function} Itself or the generated function if finished\r\n     * @throws {Error} If format parameter counts do not match\r\n     */\r\n\r\n    function Codegen(formatStringOrScope) {\r\n        // note that explicit array handling below makes this ~50% faster\r\n\r\n        // finish the function\r\n        if (typeof formatStringOrScope !== \"string\") {\r\n            var source = toString();\r\n            if (codegen.verbose)\r\n                console.log(\"codegen: \" + source); // eslint-disable-line no-console\r\n            source = \"return \" + source;\r\n            if (formatStringOrScope) {\r\n                var scopeKeys   = Object.keys(formatStringOrScope),\r\n                    scopeParams = new Array(scopeKeys.length + 1),\r\n                    scopeValues = new Array(scopeKeys.length),\r\n                    scopeOffset = 0;\r\n                while (scopeOffset < scopeKeys.length) {\r\n                    scopeParams[scopeOffset] = scopeKeys[scopeOffset];\r\n                    scopeValues[scopeOffset] = formatStringOrScope[scopeKeys[scopeOffset++]];\r\n                }\r\n                scopeParams[scopeOffset] = source;\r\n                return Function.apply(null, scopeParams).apply(null, scopeValues); // eslint-disable-line no-new-func\r\n            }\r\n            return Function(source)(); // eslint-disable-line no-new-func\r\n        }\r\n\r\n        // otherwise append to body\r\n        var formatParams = new Array(arguments.length - 1),\r\n            formatOffset = 0;\r\n        while (formatOffset < formatParams.length)\r\n            formatParams[formatOffset] = arguments[++formatOffset];\r\n        formatOffset = 0;\r\n        formatStringOrScope = formatStringOrScope.replace(/%([%dfijs])/g, function replace($0, $1) {\r\n            var value = formatParams[formatOffset++];\r\n            switch ($1) {\r\n                case \"d\": case \"f\": return String(Number(value));\r\n                case \"i\": return String(Math.floor(value));\r\n                case \"j\": return JSON.stringify(value);\r\n                case \"s\": return String(value);\r\n            }\r\n            return \"%\";\r\n        });\r\n        if (formatOffset !== formatParams.length)\r\n            throw Error(\"parameter count mismatch\");\r\n        body.push(formatStringOrScope);\r\n        return Codegen;\r\n    }\r\n\r\n    function toString(functionNameOverride) {\r\n        return \"function \" + (functionNameOverride || functionName || \"\") + \"(\" + (functionParams && functionParams.join(\",\") || \"\") + \"){\\n  \" + body.join(\"\\n  \") + \"\\n}\";\r\n    }\r\n\r\n    Codegen.toString = toString;\r\n    return Codegen;\r\n}\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @function codegen\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * When set to `true`, codegen will log generated code to console. Useful for debugging.\r\n * @name util.codegen.verbose\r\n * @type {boolean}\r\n */\r\ncodegen.verbose = false;\r\n", "\"use strict\";\r\nmodule.exports = EventEmitter;\r\n\r\n/**\r\n * Constructs a new event emitter instance.\r\n * @classdesc A minimal event emitter.\r\n * @memberof util\r\n * @constructor\r\n */\r\nfunction EventEmitter() {\r\n\r\n    /**\r\n     * Registered listeners.\r\n     * @type {Object.<string,*>}\r\n     * @private\r\n     */\r\n    this._listeners = {};\r\n}\r\n\r\n/**\r\n * Registers an event listener.\r\n * @param {string} evt Event name\r\n * @param {function} fn Listener\r\n * @param {*} [ctx] Listener context\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.on = function on(evt, fn, ctx) {\r\n    (this._listeners[evt] || (this._listeners[evt] = [])).push({\r\n        fn  : fn,\r\n        ctx : ctx || this\r\n    });\r\n    return this;\r\n};\r\n\r\n/**\r\n * Removes an event listener or any matching listeners if arguments are omitted.\r\n * @param {string} [evt] Event name. Removes all listeners if omitted.\r\n * @param {function} [fn] Listener to remove. Removes all listeners of `evt` if omitted.\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.off = function off(evt, fn) {\r\n    if (evt === undefined)\r\n        this._listeners = {};\r\n    else {\r\n        if (fn === undefined)\r\n            this._listeners[evt] = [];\r\n        else {\r\n            var listeners = this._listeners[evt];\r\n            for (var i = 0; i < listeners.length;)\r\n                if (listeners[i].fn === fn)\r\n                    listeners.splice(i, 1);\r\n                else\r\n                    ++i;\r\n        }\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Emits an event by calling its listeners with the specified arguments.\r\n * @param {string} evt Event name\r\n * @param {...*} args Arguments\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.emit = function emit(evt) {\r\n    var listeners = this._listeners[evt];\r\n    if (listeners) {\r\n        var args = [],\r\n            i = 1;\r\n        for (; i < arguments.length;)\r\n            args.push(arguments[i++]);\r\n        for (i = 0; i < listeners.length;)\r\n            listeners[i].fn.apply(listeners[i++].ctx, args);\r\n    }\r\n    return this;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = fetch;\r\n\r\nvar asPromise = require(1),\r\n    inquire   = require(7);\r\n\r\nvar fs = inquire(\"fs\");\r\n\r\n/**\r\n * Node-style callback as used by {@link util.fetch}.\r\n * @typedef FetchCallback\r\n * @type {function}\r\n * @param {?Error} error Error, if any, otherwise `null`\r\n * @param {string} [contents] File contents, if there hasn't been an error\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Options as used by {@link util.fetch}.\r\n * @typedef FetchOptions\r\n * @type {Object}\r\n * @property {boolean} [binary=false] Whether expecting a binary response\r\n * @property {boolean} [xhr=false] If `true`, forces the use of XMLHttpRequest\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @memberof util\r\n * @param {string} filename File path or url\r\n * @param {FetchOptions} options Fetch options\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n */\r\nfunction fetch(filename, options, callback) {\r\n    if (typeof options === \"function\") {\r\n        callback = options;\r\n        options = {};\r\n    } else if (!options)\r\n        options = {};\r\n\r\n    if (!callback)\r\n        return asPromise(fetch, this, filename, options); // eslint-disable-line no-invalid-this\r\n\r\n    // if a node-like filesystem is present, try it first but fall back to XHR if nothing is found.\r\n    if (!options.xhr && fs && fs.readFile)\r\n        return fs.readFile(filename, function fetchReadFileCallback(err, contents) {\r\n            return err && typeof XMLHttpRequest !== \"undefined\"\r\n                ? fetch.xhr(filename, options, callback)\r\n                : err\r\n                ? callback(err)\r\n                : callback(null, options.binary ? contents : contents.toString(\"utf8\"));\r\n        });\r\n\r\n    // use the XHR version otherwise.\r\n    return fetch.xhr(filename, options, callback);\r\n}\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchOptions} [options] Fetch options\r\n * @returns {Promise<string|Uint8Array>} Promise\r\n * @variation 3\r\n */\r\n\r\n/**/\r\nfetch.xhr = function fetch_xhr(filename, options, callback) {\r\n    var xhr = new XMLHttpRequest();\r\n    xhr.onreadystatechange /* works everywhere */ = function fetchOnReadyStateChange() {\r\n\r\n        if (xhr.readyState !== 4)\r\n            return undefined;\r\n\r\n        // local cors security errors return status 0 / empty string, too. afaik this cannot be\r\n        // reliably distinguished from an actually empty file for security reasons. feel free\r\n        // to send a pull request if you are aware of a solution.\r\n        if (xhr.status !== 0 && xhr.status !== 200)\r\n            return callback(Error(\"status \" + xhr.status));\r\n\r\n        // if binary data is expected, make sure that some sort of array is returned, even if\r\n        // ArrayBuffers are not supported. the binary string fallback, however, is unsafe.\r\n        if (options.binary) {\r\n            var buffer = xhr.response;\r\n            if (!buffer) {\r\n                buffer = [];\r\n                for (var i = 0; i < xhr.responseText.length; ++i)\r\n                    buffer.push(xhr.responseText.charCodeAt(i) & 255);\r\n            }\r\n            return callback(null, typeof Uint8Array !== \"undefined\" ? new Uint8Array(buffer) : buffer);\r\n        }\r\n        return callback(null, xhr.responseText);\r\n    };\r\n\r\n    if (options.binary) {\r\n        // ref: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/Sending_and_Receiving_Binary_Data#Receiving_binary_data_in_older_browsers\r\n        if (\"overrideMimeType\" in xhr)\r\n            xhr.overrideMimeType(\"text/plain; charset=x-user-defined\");\r\n        xhr.responseType = \"arraybuffer\";\r\n    }\r\n\r\n    xhr.open(\"GET\", filename);\r\n    xhr.send();\r\n};\r\n", "\"use strict\";\r\n\r\nmodule.exports = factory(factory);\r\n\r\n/**\r\n * Reads / writes floats / doubles from / to buffers.\r\n * @name util.float\r\n * @namespace\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using little endian byte order.\r\n * @name util.float.writeFloatLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using big endian byte order.\r\n * @name util.float.writeFloatBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using little endian byte order.\r\n * @name util.float.readFloatLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using big endian byte order.\r\n * @name util.float.readFloatBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using little endian byte order.\r\n * @name util.float.writeDoubleLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using big endian byte order.\r\n * @name util.float.writeDoubleBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using little endian byte order.\r\n * @name util.float.readDoubleLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using big endian byte order.\r\n * @name util.float.readDoubleBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n// Factory function for the purpose of node-based testing in modified global environments\r\nfunction factory(exports) {\r\n\r\n    // float: typed array\r\n    if (typeof Float32Array !== \"undefined\") (function() {\r\n\r\n        var f32 = new Float32Array([ -0 ]),\r\n            f8b = new Uint8Array(f32.buffer),\r\n            le  = f8b[3] === 128;\r\n\r\n        function writeFloat_f32_cpy(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n        }\r\n\r\n        function writeFloat_f32_rev(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[3];\r\n            buf[pos + 1] = f8b[2];\r\n            buf[pos + 2] = f8b[1];\r\n            buf[pos + 3] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeFloatLE = le ? writeFloat_f32_cpy : writeFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeFloatBE = le ? writeFloat_f32_rev : writeFloat_f32_cpy;\r\n\r\n        function readFloat_f32_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        function readFloat_f32_rev(buf, pos) {\r\n            f8b[3] = buf[pos    ];\r\n            f8b[2] = buf[pos + 1];\r\n            f8b[1] = buf[pos + 2];\r\n            f8b[0] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readFloatLE = le ? readFloat_f32_cpy : readFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.readFloatBE = le ? readFloat_f32_rev : readFloat_f32_cpy;\r\n\r\n    // float: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeFloat_ieee754(writeUint, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0)\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos);\r\n            else if (isNaN(val))\r\n                writeUint(2143289344, buf, pos);\r\n            else if (val > 3.4028234663852886e+38) // +-Infinity\r\n                writeUint((sign << 31 | 2139095040) >>> 0, buf, pos);\r\n            else if (val < 1.1754943508222875e-38) // denormal\r\n                writeUint((sign << 31 | Math.round(val / 1.401298464324817e-45)) >>> 0, buf, pos);\r\n            else {\r\n                var exponent = Math.floor(Math.log(val) / Math.LN2),\r\n                    mantissa = Math.round(val * Math.pow(2, -exponent) * 8388608) & 8388607;\r\n                writeUint((sign << 31 | exponent + 127 << 23 | mantissa) >>> 0, buf, pos);\r\n            }\r\n        }\r\n\r\n        exports.writeFloatLE = writeFloat_ieee754.bind(null, writeUintLE);\r\n        exports.writeFloatBE = writeFloat_ieee754.bind(null, writeUintBE);\r\n\r\n        function readFloat_ieee754(readUint, buf, pos) {\r\n            var uint = readUint(buf, pos),\r\n                sign = (uint >> 31) * 2 + 1,\r\n                exponent = uint >>> 23 & 255,\r\n                mantissa = uint & 8388607;\r\n            return exponent === 255\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 1.401298464324817e-45 * mantissa\r\n                : sign * Math.pow(2, exponent - 150) * (mantissa + 8388608);\r\n        }\r\n\r\n        exports.readFloatLE = readFloat_ieee754.bind(null, readUintLE);\r\n        exports.readFloatBE = readFloat_ieee754.bind(null, readUintBE);\r\n\r\n    })();\r\n\r\n    // double: typed array\r\n    if (typeof Float64Array !== \"undefined\") (function() {\r\n\r\n        var f64 = new Float64Array([-0]),\r\n            f8b = new Uint8Array(f64.buffer),\r\n            le  = f8b[7] === 128;\r\n\r\n        function writeDouble_f64_cpy(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n            buf[pos + 4] = f8b[4];\r\n            buf[pos + 5] = f8b[5];\r\n            buf[pos + 6] = f8b[6];\r\n            buf[pos + 7] = f8b[7];\r\n        }\r\n\r\n        function writeDouble_f64_rev(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[7];\r\n            buf[pos + 1] = f8b[6];\r\n            buf[pos + 2] = f8b[5];\r\n            buf[pos + 3] = f8b[4];\r\n            buf[pos + 4] = f8b[3];\r\n            buf[pos + 5] = f8b[2];\r\n            buf[pos + 6] = f8b[1];\r\n            buf[pos + 7] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleLE = le ? writeDouble_f64_cpy : writeDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleBE = le ? writeDouble_f64_rev : writeDouble_f64_cpy;\r\n\r\n        function readDouble_f64_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            f8b[4] = buf[pos + 4];\r\n            f8b[5] = buf[pos + 5];\r\n            f8b[6] = buf[pos + 6];\r\n            f8b[7] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        function readDouble_f64_rev(buf, pos) {\r\n            f8b[7] = buf[pos    ];\r\n            f8b[6] = buf[pos + 1];\r\n            f8b[5] = buf[pos + 2];\r\n            f8b[4] = buf[pos + 3];\r\n            f8b[3] = buf[pos + 4];\r\n            f8b[2] = buf[pos + 5];\r\n            f8b[1] = buf[pos + 6];\r\n            f8b[0] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readDoubleLE = le ? readDouble_f64_cpy : readDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.readDoubleBE = le ? readDouble_f64_rev : readDouble_f64_cpy;\r\n\r\n    // double: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeDouble_ieee754(writeUint, off0, off1, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos + off1);\r\n            } else if (isNaN(val)) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(2146959360, buf, pos + off1);\r\n            } else if (val > 1.7976931348623157e+308) { // +-Infinity\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint((sign << 31 | 2146435072) >>> 0, buf, pos + off1);\r\n            } else {\r\n                var mantissa;\r\n                if (val < 2.2250738585072014e-308) { // denormal\r\n                    mantissa = val / 5e-324;\r\n                    writeUint(mantissa >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | mantissa / 4294967296) >>> 0, buf, pos + off1);\r\n                } else {\r\n                    var exponent = Math.floor(Math.log(val) / Math.LN2);\r\n                    if (exponent === 1024)\r\n                        exponent = 1023;\r\n                    mantissa = val * Math.pow(2, -exponent);\r\n                    writeUint(mantissa * 4503599627370496 >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | exponent + 1023 << 20 | mantissa * 1048576 & 1048575) >>> 0, buf, pos + off1);\r\n                }\r\n            }\r\n        }\r\n\r\n        exports.writeDoubleLE = writeDouble_ieee754.bind(null, writeUintLE, 0, 4);\r\n        exports.writeDoubleBE = writeDouble_ieee754.bind(null, writeUintBE, 4, 0);\r\n\r\n        function readDouble_ieee754(readUint, off0, off1, buf, pos) {\r\n            var lo = readUint(buf, pos + off0),\r\n                hi = readUint(buf, pos + off1);\r\n            var sign = (hi >> 31) * 2 + 1,\r\n                exponent = hi >>> 20 & 2047,\r\n                mantissa = 4294967296 * (hi & 1048575) + lo;\r\n            return exponent === 2047\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 5e-324 * mantissa\r\n                : sign * Math.pow(2, exponent - 1075) * (mantissa + 4503599627370496);\r\n        }\r\n\r\n        exports.readDoubleLE = readDouble_ieee754.bind(null, readUintLE, 0, 4);\r\n        exports.readDoubleBE = readDouble_ieee754.bind(null, readUintBE, 4, 0);\r\n\r\n    })();\r\n\r\n    return exports;\r\n}\r\n\r\n// uint helpers\r\n\r\nfunction writeUintLE(val, buf, pos) {\r\n    buf[pos    ] =  val        & 255;\r\n    buf[pos + 1] =  val >>> 8  & 255;\r\n    buf[pos + 2] =  val >>> 16 & 255;\r\n    buf[pos + 3] =  val >>> 24;\r\n}\r\n\r\nfunction writeUintBE(val, buf, pos) {\r\n    buf[pos    ] =  val >>> 24;\r\n    buf[pos + 1] =  val >>> 16 & 255;\r\n    buf[pos + 2] =  val >>> 8  & 255;\r\n    buf[pos + 3] =  val        & 255;\r\n}\r\n\r\nfunction readUintLE(buf, pos) {\r\n    return (buf[pos    ]\r\n          | buf[pos + 1] << 8\r\n          | buf[pos + 2] << 16\r\n          | buf[pos + 3] << 24) >>> 0;\r\n}\r\n\r\nfunction readUintBE(buf, pos) {\r\n    return (buf[pos    ] << 24\r\n          | buf[pos + 1] << 16\r\n          | buf[pos + 2] << 8\r\n          | buf[pos + 3]) >>> 0;\r\n}\r\n", "\"use strict\";\r\nmodule.exports = inquire;\r\n\r\n/**\r\n * Requires a module only if available.\r\n * @memberof util\r\n * @param {string} moduleName Module to require\r\n * @returns {?Object} Required module if available and not empty, otherwise `null`\r\n */\r\nfunction inquire(moduleName) {\r\n    try {\r\n        var mod = eval(\"quire\".replace(/^/,\"re\"))(moduleName); // eslint-disable-line no-eval\r\n        if (mod && (mod.length || Object.keys(mod).length))\r\n            return mod;\r\n    } catch (e) {} // eslint-disable-line no-empty\r\n    return null;\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal path module to resolve Unix, Windows and URL paths alike.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar path = exports;\r\n\r\nvar isAbsolute =\r\n/**\r\n * Tests if the specified path is absolute.\r\n * @param {string} path Path to test\r\n * @returns {boolean} `true` if path is absolute\r\n */\r\npath.isAbsolute = function isAbsolute(path) {\r\n    return /^(?:\\/|\\w+:)/.test(path);\r\n};\r\n\r\nvar normalize =\r\n/**\r\n * Normalizes the specified path.\r\n * @param {string} path Path to normalize\r\n * @returns {string} Normalized path\r\n */\r\npath.normalize = function normalize(path) {\r\n    path = path.replace(/\\\\/g, \"/\")\r\n               .replace(/\\/{2,}/g, \"/\");\r\n    var parts    = path.split(\"/\"),\r\n        absolute = isAbsolute(path),\r\n        prefix   = \"\";\r\n    if (absolute)\r\n        prefix = parts.shift() + \"/\";\r\n    for (var i = 0; i < parts.length;) {\r\n        if (parts[i] === \"..\") {\r\n            if (i > 0 && parts[i - 1] !== \"..\")\r\n                parts.splice(--i, 2);\r\n            else if (absolute)\r\n                parts.splice(i, 1);\r\n            else\r\n                ++i;\r\n        } else if (parts[i] === \".\")\r\n            parts.splice(i, 1);\r\n        else\r\n            ++i;\r\n    }\r\n    return prefix + parts.join(\"/\");\r\n};\r\n\r\n/**\r\n * Resolves the specified include path against the specified origin path.\r\n * @param {string} originPath Path to the origin file\r\n * @param {string} includePath Include path relative to origin path\r\n * @param {boolean} [alreadyNormalized=false] `true` if both paths are already known to be normalized\r\n * @returns {string} Path to the include file\r\n */\r\npath.resolve = function resolve(originPath, includePath, alreadyNormalized) {\r\n    if (!alreadyNormalized)\r\n        includePath = normalize(includePath);\r\n    if (isAbsolute(includePath))\r\n        return includePath;\r\n    if (!alreadyNormalized)\r\n        originPath = normalize(originPath);\r\n    return (originPath = originPath.replace(/(?:\\/|^)[^/]+$/, \"\")).length ? normalize(originPath + \"/\" + includePath) : includePath;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = pool;\r\n\r\n/**\r\n * An allocator as used by {@link util.pool}.\r\n * @typedef PoolAllocator\r\n * @type {function}\r\n * @param {number} size Buffer size\r\n * @returns {Uint8Array} Buffer\r\n */\r\n\r\n/**\r\n * A slicer as used by {@link util.pool}.\r\n * @typedef PoolSlicer\r\n * @type {function}\r\n * @param {number} start Start offset\r\n * @param {number} end End offset\r\n * @returns {Uint8Array} Buffer slice\r\n * @this {Uint8Array}\r\n */\r\n\r\n/**\r\n * A general purpose buffer pool.\r\n * @memberof util\r\n * @function\r\n * @param {PoolAllocator} alloc Allocator\r\n * @param {PoolSlicer} slice Slicer\r\n * @param {number} [size=8192] Slab size\r\n * @returns {PoolAllocator} Pooled allocator\r\n */\r\nfunction pool(alloc, slice, size) {\r\n    var SIZE   = size || 8192;\r\n    var MAX    = SIZE >>> 1;\r\n    var slab   = null;\r\n    var offset = SIZE;\r\n    return function pool_alloc(size) {\r\n        if (size < 1 || size > MAX)\r\n            return alloc(size);\r\n        if (offset + size > SIZE) {\r\n            slab = alloc(SIZE);\r\n            offset = 0;\r\n        }\r\n        var buf = slice.call(slab, offset, offset += size);\r\n        if (offset & 7) // align to 32 bit\r\n            offset = (offset | 7) + 1;\r\n        return buf;\r\n    };\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal UTF8 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar utf8 = exports;\r\n\r\n/**\r\n * Calculates the UTF8 byte length of a string.\r\n * @param {string} string String\r\n * @returns {number} Byte length\r\n */\r\nutf8.length = function utf8_length(string) {\r\n    var len = 0,\r\n        c = 0;\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c = string.charCodeAt(i);\r\n        if (c < 128)\r\n            len += 1;\r\n        else if (c < 2048)\r\n            len += 2;\r\n        else if ((c & 0xFC00) === 0xD800 && (string.charCodeAt(i + 1) & 0xFC00) === 0xDC00) {\r\n            ++i;\r\n            len += 4;\r\n        } else\r\n            len += 3;\r\n    }\r\n    return len;\r\n};\r\n\r\n/**\r\n * Reads UTF8 bytes as a string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} String read\r\n */\r\nutf8.read = function utf8_read(buffer, start, end) {\r\n    var len = end - start;\r\n    if (len < 1)\r\n        return \"\";\r\n    var parts = null,\r\n        chunk = [],\r\n        i = 0, // char offset\r\n        t;     // temporary\r\n    while (start < end) {\r\n        t = buffer[start++];\r\n        if (t < 128)\r\n            chunk[i++] = t;\r\n        else if (t > 191 && t < 224)\r\n            chunk[i++] = (t & 31) << 6 | buffer[start++] & 63;\r\n        else if (t > 239 && t < 365) {\r\n            t = ((t & 7) << 18 | (buffer[start++] & 63) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63) - 0x10000;\r\n            chunk[i++] = 0xD800 + (t >> 10);\r\n            chunk[i++] = 0xDC00 + (t & 1023);\r\n        } else\r\n            chunk[i++] = (t & 15) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63;\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\n/**\r\n * Writes a string as UTF8 bytes.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Bytes written\r\n */\r\nutf8.write = function utf8_write(string, buffer, offset) {\r\n    var start = offset,\r\n        c1, // character 1\r\n        c2; // character 2\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c1 = string.charCodeAt(i);\r\n        if (c1 < 128) {\r\n            buffer[offset++] = c1;\r\n        } else if (c1 < 2048) {\r\n            buffer[offset++] = c1 >> 6       | 192;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else if ((c1 & 0xFC00) === 0xD800 && ((c2 = string.charCodeAt(i + 1)) & 0xFC00) === 0xDC00) {\r\n            c1 = 0x10000 + ((c1 & 0x03FF) << 10) + (c2 & 0x03FF);\r\n            ++i;\r\n            buffer[offset++] = c1 >> 18      | 240;\r\n            buffer[offset++] = c1 >> 12 & 63 | 128;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else {\r\n            buffer[offset++] = c1 >> 12      | 224;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        }\r\n    }\r\n    return offset - start;\r\n};\r\n", "\"use strict\";\n/**\n * Runtime message from/to plain object converters.\n * @namespace\n */\nvar converter = exports;\n\nvar Enum = require(14),\n    util = require(33);\n\n/**\n * Generates a partial value fromObject conveter.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} prop Property reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genValuePartial_fromObject(gen, field, fieldIndex, prop) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) { gen\n            (\"switch(d%s){\", prop);\n            for (var values = field.resolvedType.values, keys = Object.keys(values), i = 0; i < keys.length; ++i) {\n                if (field.repeated && values[keys[i]] === field.typeDefault) gen\n                (\"default:\");\n                gen\n                (\"case%j:\", keys[i])\n                (\"case %i:\", values[keys[i]])\n                    (\"m%s=%j\", prop, values[keys[i]])\n                    (\"break\");\n            } gen\n            (\"}\");\n        } else gen\n            (\"if(typeof d%s!==\\\"object\\\")\", prop)\n                (\"throw TypeError(%j)\", field.fullName + \": object expected\")\n            (\"m%s=types[%i].fromObject(d%s)\", prop, fieldIndex, prop);\n    } else {\n        var isUnsigned = false;\n        switch (field.type) {\n            case \"double\":\n            case \"float\": gen\n                (\"m%s=Number(d%s)\", prop, prop); // also catches \"NaN\", \"Infinity\"\n                break;\n            case \"uint32\":\n            case \"fixed32\": gen\n                (\"m%s=d%s>>>0\", prop, prop);\n                break;\n            case \"int32\":\n            case \"sint32\":\n            case \"sfixed32\": gen\n                (\"m%s=d%s|0\", prop, prop);\n                break;\n            case \"uint64\":\n                isUnsigned = true;\n                // eslint-disable-line no-fallthrough\n            case \"int64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n                (\"if(util.Long)\")\n                    (\"(m%s=util.Long.fromValue(d%s)).unsigned=%j\", prop, prop, isUnsigned)\n                (\"else if(typeof d%s===\\\"string\\\")\", prop)\n                    (\"m%s=parseInt(d%s,10)\", prop, prop)\n                (\"else if(typeof d%s===\\\"number\\\")\", prop)\n                    (\"m%s=d%s\", prop, prop)\n                (\"else if(typeof d%s===\\\"object\\\")\", prop)\n                    (\"m%s=new util.LongBits(d%s.low>>>0,d%s.high>>>0).toNumber(%s)\", prop, prop, prop, isUnsigned ? \"true\" : \"\");\n                break;\n            case \"bytes\": gen\n                (\"if(typeof d%s===\\\"string\\\")\", prop)\n                    (\"util.base64.decode(d%s,m%s=util.newBuffer(util.base64.length(d%s)),0)\", prop, prop, prop)\n                (\"else if(d%s.length)\", prop)\n                    (\"m%s=d%s\", prop, prop);\n                break;\n            case \"string\": gen\n                (\"m%s=String(d%s)\", prop, prop);\n                break;\n            case \"bool\": gen\n                (\"m%s=Boolean(d%s)\", prop, prop);\n                break;\n            /* default: gen\n                (\"m%s=d%s\", prop, prop);\n                break; */\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n\n/**\n * Generates a plain object to runtime message converter specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nconverter.fromObject = function fromObject(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var fields = mtype.fieldsArray;\n    var gen = util.codegen([\"d\"], mtype.name + \"$fromObject\")\n    (\"if(d instanceof this.ctor)\")\n        (\"return d\");\n    if (!fields.length) return gen\n    (\"return new this.ctor\");\n    gen\n    (\"var m=new this.ctor\");\n    for (var i = 0; i < fields.length; ++i) {\n        var field  = fields[i].resolve(),\n            prop   = util.safeProp(field.name);\n\n        // Map fields\n        if (field.map) { gen\n    (\"if(d%s){\", prop)\n        (\"if(typeof d%s!==\\\"object\\\")\", prop)\n            (\"throw TypeError(%j)\", field.fullName + \": object expected\")\n        (\"m%s={}\", prop)\n        (\"for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){\", prop);\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[ks[i]]\")\n        (\"}\")\n    (\"}\");\n\n        // Repeated fields\n        } else if (field.repeated) { gen\n    (\"if(d%s){\", prop)\n        (\"if(!Array.isArray(d%s))\", prop)\n            (\"throw TypeError(%j)\", field.fullName + \": array expected\")\n        (\"m%s=[]\", prop)\n        (\"for(var i=0;i<d%s.length;++i){\", prop);\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[i]\")\n        (\"}\")\n    (\"}\");\n\n        // Non-repeated fields\n        } else {\n            if (!(field.resolvedType instanceof Enum)) gen // no need to test for null/undefined if an enum (uses switch)\n    (\"if(d%s!=null){\", prop); // !== undefined && !== null\n        genValuePartial_fromObject(gen, field, /* not sorted */ i, prop);\n            if (!(field.resolvedType instanceof Enum)) gen\n    (\"}\");\n        }\n    } return gen\n    (\"return m\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n};\n\n/**\n * Generates a partial value toObject converter.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} prop Property reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genValuePartial_toObject(gen, field, fieldIndex, prop) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) gen\n            (\"d%s=o.enums===String?types[%i].values[m%s]:m%s\", prop, fieldIndex, prop, prop);\n        else gen\n            (\"d%s=types[%i].toObject(m%s,o)\", prop, fieldIndex, prop);\n    } else {\n        var isUnsigned = false;\n        switch (field.type) {\n            case \"double\":\n            case \"float\": gen\n            (\"d%s=o.json&&!isFinite(m%s)?String(m%s):m%s\", prop, prop, prop, prop);\n                break;\n            case \"uint64\":\n                isUnsigned = true;\n                // eslint-disable-line no-fallthrough\n            case \"int64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n            (\"if(typeof m%s===\\\"number\\\")\", prop)\n                (\"d%s=o.longs===String?String(m%s):m%s\", prop, prop, prop)\n            (\"else\") // Long-like\n                (\"d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s\", prop, prop, prop, prop, isUnsigned ? \"true\": \"\", prop);\n                break;\n            case \"bytes\": gen\n            (\"d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s\", prop, prop, prop, prop, prop);\n                break;\n            default: gen\n            (\"d%s=m%s\", prop, prop);\n                break;\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n\n/**\n * Generates a runtime message to plain object converter specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nconverter.toObject = function toObject(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var fields = mtype.fieldsArray.slice().sort(util.compareFieldsById);\n    if (!fields.length)\n        return util.codegen()(\"return {}\");\n    var gen = util.codegen([\"m\", \"o\"], mtype.name + \"$toObject\")\n    (\"if(!o)\")\n        (\"o={}\")\n    (\"var d={}\");\n\n    var repeatedFields = [],\n        mapFields = [],\n        normalFields = [],\n        i = 0;\n    for (; i < fields.length; ++i)\n        if (!fields[i].partOf)\n            ( fields[i].resolve().repeated ? repeatedFields\n            : fields[i].map ? mapFields\n            : normalFields).push(fields[i]);\n\n    if (repeatedFields.length) { gen\n    (\"if(o.arrays||o.defaults){\");\n        for (i = 0; i < repeatedFields.length; ++i) gen\n        (\"d%s=[]\", util.safeProp(repeatedFields[i].name));\n        gen\n    (\"}\");\n    }\n\n    if (mapFields.length) { gen\n    (\"if(o.objects||o.defaults){\");\n        for (i = 0; i < mapFields.length; ++i) gen\n        (\"d%s={}\", util.safeProp(mapFields[i].name));\n        gen\n    (\"}\");\n    }\n\n    if (normalFields.length) { gen\n    (\"if(o.defaults){\");\n        for (i = 0; i < normalFields.length; ++i) {\n            var field = normalFields[i],\n                prop  = util.safeProp(field.name);\n            if (field.resolvedType instanceof Enum) gen\n        (\"d%s=o.enums===String?%j:%j\", prop, field.resolvedType.valuesById[field.typeDefault], field.typeDefault);\n            else if (field.long) gen\n        (\"if(util.Long){\")\n            (\"var n=new util.Long(%i,%i,%j)\", field.typeDefault.low, field.typeDefault.high, field.typeDefault.unsigned)\n            (\"d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n\", prop)\n        (\"}else\")\n            (\"d%s=o.longs===String?%j:%i\", prop, field.typeDefault.toString(), field.typeDefault.toNumber());\n            else if (field.bytes) {\n                var arrayDefault = \"[\" + Array.prototype.slice.call(field.typeDefault).join(\",\") + \"]\";\n                gen\n        (\"if(o.bytes===String)d%s=%j\", prop, String.fromCharCode.apply(String, field.typeDefault))\n        (\"else{\")\n            (\"d%s=%s\", prop, arrayDefault)\n            (\"if(o.bytes!==Array)d%s=util.newBuffer(d%s)\", prop, prop)\n        (\"}\");\n            } else gen\n        (\"d%s=%j\", prop, field.typeDefault); // also messages (=null)\n        } gen\n    (\"}\");\n    }\n    var hasKs2 = false;\n    for (i = 0; i < fields.length; ++i) {\n        var field = fields[i],\n            index = mtype._fieldsArray.indexOf(field),\n            prop  = util.safeProp(field.name);\n        if (field.map) {\n            if (!hasKs2) { hasKs2 = true; gen\n    (\"var ks2\");\n            } gen\n    (\"if(m%s&&(ks2=Object.keys(m%s)).length){\", prop, prop)\n        (\"d%s={}\", prop)\n        (\"for(var j=0;j<ks2.length;++j){\");\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[ks2[j]]\")\n        (\"}\");\n        } else if (field.repeated) { gen\n    (\"if(m%s&&m%s.length){\", prop, prop)\n        (\"d%s=[]\", prop)\n        (\"for(var j=0;j<m%s.length;++j){\", prop);\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[j]\")\n        (\"}\");\n        } else { gen\n    (\"if(m%s!=null&&m.hasOwnProperty(%j)){\", prop, field.name); // !== undefined && !== null\n        genValuePartial_toObject(gen, field, /* sorted */ index, prop);\n        if (field.partOf) gen\n        (\"if(o.oneofs)\")\n            (\"d%s=%j\", util.safeProp(field.partOf.name), field.name);\n        }\n        gen\n    (\"}\");\n    }\n    return gen\n    (\"return d\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n};\n", "\"use strict\";\nmodule.exports = decoder;\n\nvar Enum    = require(14),\n    types   = require(32),\n    util    = require(33);\n\nfunction missing(field) {\n    return \"missing required '\" + field.name + \"'\";\n}\n\n/**\n * Generates a decoder specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction decoder(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n    var gen = util.codegen([\"r\", \"l\"], mtype.name + \"$decode\")\n    (\"if(!(r instanceof Reader))\")\n        (\"r=Reader.create(r)\")\n    (\"var c=l===undefined?r.len:r.pos+l,m=new this.ctor\" + (mtype.fieldsArray.filter(function(field) { return field.map; }).length ? \",k,value\" : \"\"))\n    (\"while(r.pos<c){\")\n        (\"var t=r.uint32()\");\n    if (mtype.group) gen\n        (\"if((t&7)===4)\")\n            (\"break\");\n    gen\n        (\"switch(t>>>3){\");\n\n    var i = 0;\n    for (; i < /* initializes */ mtype.fieldsArray.length; ++i) {\n        var field = mtype._fieldsArray[i].resolve(),\n            type  = field.resolvedType instanceof Enum ? \"int32\" : field.type,\n            ref   = \"m\" + util.safeProp(field.name); gen\n            (\"case %i:\", field.id);\n\n        // Map fields\n        if (field.map) { gen\n                (\"if(%s===util.emptyObject)\", ref)\n                    (\"%s={}\", ref)\n                (\"var c2 = r.uint32()+r.pos\");\n\n            if (types.defaults[field.keyType] !== undefined) gen\n                (\"k=%j\", types.defaults[field.keyType]);\n            else gen\n                (\"k=null\");\n\n            if (types.defaults[type] !== undefined) gen\n                (\"value=%j\", types.defaults[type]);\n            else gen\n                (\"value=null\");\n\n            gen\n                (\"while(r.pos<c2){\")\n                    (\"var tag2=r.uint32()\")\n                    (\"switch(tag2>>>3){\")\n                        (\"case 1: k=r.%s(); break\", field.keyType)\n                        (\"case 2:\");\n\n            if (types.basic[type] === undefined) gen\n                            (\"value=types[%i].decode(r,r.uint32())\", i); // can't be groups\n            else gen\n                            (\"value=r.%s()\", type);\n\n            gen\n                            (\"break\")\n                        (\"default:\")\n                            (\"r.skipType(tag2&7)\")\n                            (\"break\")\n                    (\"}\")\n                (\"}\");\n\n            if (types.long[field.keyType] !== undefined) gen\n                (\"%s[typeof k===\\\"object\\\"?util.longToHash(k):k]=value\", ref);\n            else gen\n                (\"%s[k]=value\", ref);\n\n        // Repeated fields\n        } else if (field.repeated) { gen\n\n                (\"if(!(%s&&%s.length))\", ref, ref)\n                    (\"%s=[]\", ref);\n\n            // Packable (always check for forward and backward compatiblity)\n            if (types.packed[type] !== undefined) gen\n                (\"if((t&7)===2){\")\n                    (\"var c2=r.uint32()+r.pos\")\n                    (\"while(r.pos<c2)\")\n                        (\"%s.push(r.%s())\", ref, type)\n                (\"}else\");\n\n            // Non-packed\n            if (types.basic[type] === undefined) gen(field.resolvedType.group\n                    ? \"%s.push(types[%i].decode(r))\"\n                    : \"%s.push(types[%i].decode(r,r.uint32()))\", ref, i);\n            else gen\n                    (\"%s.push(r.%s())\", ref, type);\n\n        // Non-repeated\n        } else if (types.basic[type] === undefined) gen(field.resolvedType.group\n                ? \"%s=types[%i].decode(r)\"\n                : \"%s=types[%i].decode(r,r.uint32())\", ref, i);\n        else gen\n                (\"%s=r.%s()\", ref, type);\n        gen\n                (\"break\");\n    // Unknown fields\n    } gen\n            (\"default:\")\n                (\"r.skipType(t&7)\")\n                (\"break\")\n\n        (\"}\")\n    (\"}\");\n\n    // Field presence\n    for (i = 0; i < mtype._fieldsArray.length; ++i) {\n        var rfield = mtype._fieldsArray[i];\n        if (rfield.required) gen\n    (\"if(!m.hasOwnProperty(%j))\", rfield.name)\n        (\"throw util.ProtocolError(%j,{instance:m})\", missing(rfield));\n    }\n\n    return gen\n    (\"return m\");\n    /* eslint-enable no-unexpected-multiline */\n}\n", "\"use strict\";\nmodule.exports = encoder;\n\nvar Enum     = require(14),\n    types    = require(32),\n    util     = require(33);\n\n/**\n * Generates a partial message type encoder.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genTypePartial(gen, field, fieldIndex, ref) {\n    return field.resolvedType.group\n        ? gen(\"types[%i].encode(%s,w.uint32(%i)).uint32(%i)\", fieldIndex, ref, (field.id << 3 | 3) >>> 0, (field.id << 3 | 4) >>> 0)\n        : gen(\"types[%i].encode(%s,w.uint32(%i).fork()).ldelim()\", fieldIndex, ref, (field.id << 3 | 2) >>> 0);\n}\n\n/**\n * Generates an encoder specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction encoder(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var gen = util.codegen([\"m\", \"w\"], mtype.name + \"$encode\")\n    (\"if(!w)\")\n        (\"w=Writer.create()\");\n\n    var i, ref;\n\n    // \"when a message is serialized its known fields should be written sequentially by field number\"\n    var fields = /* initializes */ mtype.fieldsArray.slice().sort(util.compareFieldsById);\n\n    for (var i = 0; i < fields.length; ++i) {\n        var field    = fields[i].resolve(),\n            index    = mtype._fieldsArray.indexOf(field),\n            type     = field.resolvedType instanceof Enum ? \"int32\" : field.type,\n            wireType = types.basic[type];\n            ref      = \"m\" + util.safeProp(field.name);\n\n        // Map fields\n        if (field.map) {\n            gen\n    (\"if(%s!=null&&Object.hasOwnProperty.call(m,%j)){\", ref, field.name) // !== undefined && !== null\n        (\"for(var ks=Object.keys(%s),i=0;i<ks.length;++i){\", ref)\n            (\"w.uint32(%i).fork().uint32(%i).%s(ks[i])\", (field.id << 3 | 2) >>> 0, 8 | types.mapKey[field.keyType], field.keyType);\n            if (wireType === undefined) gen\n            (\"types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()\", index, ref); // can't be groups\n            else gen\n            (\".uint32(%i).%s(%s[ks[i]]).ldelim()\", 16 | wireType, type, ref);\n            gen\n        (\"}\")\n    (\"}\");\n\n            // Repeated fields\n        } else if (field.repeated) { gen\n    (\"if(%s!=null&&%s.length){\", ref, ref); // !== undefined && !== null\n\n            // Packed repeated\n            if (field.packed && types.packed[type] !== undefined) { gen\n\n        (\"w.uint32(%i).fork()\", (field.id << 3 | 2) >>> 0)\n        (\"for(var i=0;i<%s.length;++i)\", ref)\n            (\"w.%s(%s[i])\", type, ref)\n        (\"w.ldelim()\");\n\n            // Non-packed\n            } else { gen\n\n        (\"for(var i=0;i<%s.length;++i)\", ref);\n                if (wireType === undefined)\n            genTypePartial(gen, field, index, ref + \"[i]\");\n                else gen\n            (\"w.uint32(%i).%s(%s[i])\", (field.id << 3 | wireType) >>> 0, type, ref);\n\n            } gen\n    (\"}\");\n\n        // Non-repeated\n        } else {\n            if (field.optional) gen\n    (\"if(%s!=null&&Object.hasOwnProperty.call(m,%j))\", ref, field.name); // !== undefined && !== null\n\n            if (wireType === undefined)\n        genTypePartial(gen, field, index, ref);\n            else gen\n        (\"w.uint32(%i).%s(%s)\", (field.id << 3 | wireType) >>> 0, type, ref);\n\n        }\n    }\n\n    return gen\n    (\"return w\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n", "\"use strict\";\nmodule.exports = Enum;\n\n// extends ReflectionObject\nvar ReflectionObject = require(22);\n((Enum.prototype = Object.create(ReflectionObject.prototype)).constructor = Enum).className = \"Enum\";\n\nvar Namespace = require(21),\n    util = require(33);\n\n/**\n * Constructs a new enum instance.\n * @classdesc Reflected enum.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {Object.<string,number>} [values] Enum values as an object, by name\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] The comment for this enum\n * @param {Object.<string,string>} [comments] The value comments for this enum\n */\nfunction Enum(name, values, options, comment, comments) {\n    ReflectionObject.call(this, name, options);\n\n    if (values && typeof values !== \"object\")\n        throw TypeError(\"values must be an object\");\n\n    /**\n     * Enum values by id.\n     * @type {Object.<number,string>}\n     */\n    this.valuesById = {};\n\n    /**\n     * Enum values by name.\n     * @type {Object.<string,number>}\n     */\n    this.values = Object.create(this.valuesById); // toJSON, marker\n\n    /**\n     * Enum comment text.\n     * @type {string|null}\n     */\n    this.comment = comment;\n\n    /**\n     * Value comment texts, if any.\n     * @type {Object.<string,string>}\n     */\n    this.comments = comments || {};\n\n    /**\n     * Reserved ranges, if any.\n     * @type {Array.<number[]|string>}\n     */\n    this.reserved = undefined; // toJSON\n\n    // Note that values inherit valuesById on their prototype which makes them a TypeScript-\n    // compatible enum. This is used by pbts to write actual enum definitions that work for\n    // static and reflection code alike instead of emitting generic object definitions.\n\n    if (values)\n        for (var keys = Object.keys(values), i = 0; i < keys.length; ++i)\n            if (typeof values[keys[i]] === \"number\") // use forward entries only\n                this.valuesById[ this.values[keys[i]] = values[keys[i]] ] = keys[i];\n}\n\n/**\n * Enum descriptor.\n * @interface IEnum\n * @property {Object.<string,number>} values Enum values\n * @property {Object.<string,*>} [options] Enum options\n */\n\n/**\n * Constructs an enum from an enum descriptor.\n * @param {string} name Enum name\n * @param {IEnum} json Enum descriptor\n * @returns {Enum} Created enum\n * @throws {TypeError} If arguments are invalid\n */\nEnum.fromJSON = function fromJSON(name, json) {\n    var enm = new Enum(name, json.values, json.options, json.comment, json.comments);\n    enm.reserved = json.reserved;\n    return enm;\n};\n\n/**\n * Converts this enum to an enum descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IEnum} Enum descriptor\n */\nEnum.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\"  , this.options,\n        \"values\"   , this.values,\n        \"reserved\" , this.reserved && this.reserved.length ? this.reserved : undefined,\n        \"comment\"  , keepComments ? this.comment : undefined,\n        \"comments\" , keepComments ? this.comments : undefined\n    ]);\n};\n\n/**\n * Adds a value to this enum.\n * @param {string} name Value name\n * @param {number} id Value id\n * @param {string} [comment] Comment, if any\n * @returns {Enum} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a value with this name or id\n */\nEnum.prototype.add = function add(name, id, comment) {\n    // utilized by the parser but not by .fromJSON\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    if (!util.isInteger(id))\n        throw TypeError(\"id must be an integer\");\n\n    if (this.values[name] !== undefined)\n        throw Error(\"duplicate name '\" + name + \"' in \" + this);\n\n    if (this.isReservedId(id))\n        throw Error(\"id \" + id + \" is reserved in \" + this);\n\n    if (this.isReservedName(name))\n        throw Error(\"name '\" + name + \"' is reserved in \" + this);\n\n    if (this.valuesById[id] !== undefined) {\n        if (!(this.options && this.options.allow_alias))\n            throw Error(\"duplicate id \" + id + \" in \" + this);\n        this.values[name] = id;\n    } else\n        this.valuesById[this.values[name] = id] = name;\n\n    this.comments[name] = comment || null;\n    return this;\n};\n\n/**\n * Removes a value from this enum\n * @param {string} name Value name\n * @returns {Enum} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `name` is not a name of this enum\n */\nEnum.prototype.remove = function remove(name) {\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    var val = this.values[name];\n    if (val == null)\n        throw Error(\"name '\" + name + \"' does not exist in \" + this);\n\n    delete this.valuesById[val];\n    delete this.values[name];\n    delete this.comments[name];\n\n    return this;\n};\n\n/**\n * Tests if the specified id is reserved.\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nEnum.prototype.isReservedId = function isReservedId(id) {\n    return Namespace.isReservedId(this.reserved, id);\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nEnum.prototype.isReservedName = function isReservedName(name) {\n    return Namespace.isReservedName(this.reserved, name);\n};\n", "\"use strict\";\nmodule.exports = Field;\n\n// extends ReflectionObject\nvar ReflectionObject = require(22);\n((Field.prototype = Object.create(ReflectionObject.prototype)).constructor = Field).className = \"Field\";\n\nvar Enum  = require(14),\n    types = require(32),\n    util  = require(33);\n\nvar Type; // cyclic\n\nvar ruleRe = /^required|optional|repeated$/;\n\n/**\n * Constructs a new message field instance. Note that {@link MapField|map fields} have their own class.\n * @name Field\n * @classdesc Reflected message field.\n * @extends FieldBase\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} type Value type\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\n * @param {Object.<string,*>} [options] Declared options\n */\n\n/**\n * Constructs a field from a field descriptor.\n * @param {string} name Field name\n * @param {IField} json Field descriptor\n * @returns {Field} Created field\n * @throws {TypeError} If arguments are invalid\n */\nField.fromJSON = function fromJSON(name, json) {\n    return new Field(name, json.id, json.type, json.rule, json.extend, json.options, json.comment);\n};\n\n/**\n * Not an actual constructor. Use {@link Field} instead.\n * @classdesc Base class of all reflected message fields. This is not an actual class but here for the sake of having consistent type definitions.\n * @exports FieldBase\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} type Value type\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction Field(name, id, type, rule, extend, options, comment) {\n\n    if (util.isObject(rule)) {\n        comment = extend;\n        options = rule;\n        rule = extend = undefined;\n    } else if (util.isObject(extend)) {\n        comment = options;\n        options = extend;\n        extend = undefined;\n    }\n\n    ReflectionObject.call(this, name, options);\n\n    if (!util.isInteger(id) || id < 0)\n        throw TypeError(\"id must be a non-negative integer\");\n\n    if (!util.isString(type))\n        throw TypeError(\"type must be a string\");\n\n    if (rule !== undefined && !ruleRe.test(rule = rule.toString().toLowerCase()))\n        throw TypeError(\"rule must be a string rule\");\n\n    if (extend !== undefined && !util.isString(extend))\n        throw TypeError(\"extend must be a string\");\n\n    if (rule === \"proto3_optional\") {\n        rule = \"optional\";\n    }\n    /**\n     * Field rule, if any.\n     * @type {string|undefined}\n     */\n    this.rule = rule && rule !== \"optional\" ? rule : undefined; // toJSON\n\n    /**\n     * Field type.\n     * @type {string}\n     */\n    this.type = type; // toJSON\n\n    /**\n     * Unique field id.\n     * @type {number}\n     */\n    this.id = id; // toJSON, marker\n\n    /**\n     * Extended type if different from parent.\n     * @type {string|undefined}\n     */\n    this.extend = extend || undefined; // toJSON\n\n    /**\n     * Whether this field is required.\n     * @type {boolean}\n     */\n    this.required = rule === \"required\";\n\n    /**\n     * Whether this field is optional.\n     * @type {boolean}\n     */\n    this.optional = !this.required;\n\n    /**\n     * Whether this field is repeated.\n     * @type {boolean}\n     */\n    this.repeated = rule === \"repeated\";\n\n    /**\n     * Whether this field is a map or not.\n     * @type {boolean}\n     */\n    this.map = false;\n\n    /**\n     * Message this field belongs to.\n     * @type {Type|null}\n     */\n    this.message = null;\n\n    /**\n     * OneOf this field belongs to, if any,\n     * @type {OneOf|null}\n     */\n    this.partOf = null;\n\n    /**\n     * The field type's default value.\n     * @type {*}\n     */\n    this.typeDefault = null;\n\n    /**\n     * The field's default value on prototypes.\n     * @type {*}\n     */\n    this.defaultValue = null;\n\n    /**\n     * Whether this field's value should be treated as a long.\n     * @type {boolean}\n     */\n    this.long = util.Long ? types.long[type] !== undefined : /* istanbul ignore next */ false;\n\n    /**\n     * Whether this field's value is a buffer.\n     * @type {boolean}\n     */\n    this.bytes = type === \"bytes\";\n\n    /**\n     * Resolved type if not a basic type.\n     * @type {Type|Enum|null}\n     */\n    this.resolvedType = null;\n\n    /**\n     * Sister-field within the extended type if a declaring extension field.\n     * @type {Field|null}\n     */\n    this.extensionField = null;\n\n    /**\n     * Sister-field within the declaring namespace if an extended field.\n     * @type {Field|null}\n     */\n    this.declaringField = null;\n\n    /**\n     * Internally remembers whether this field is packed.\n     * @type {boolean|null}\n     * @private\n     */\n    this._packed = null;\n\n    /**\n     * Comment for this field.\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Determines whether this field is packed. Only relevant when repeated and working with proto2.\n * @name Field#packed\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(Field.prototype, \"packed\", {\n    get: function() {\n        // defaults to packed=true if not explicity set to false\n        if (this._packed === null)\n            this._packed = this.getOption(\"packed\") !== false;\n        return this._packed;\n    }\n});\n\n/**\n * @override\n */\nField.prototype.setOption = function setOption(name, value, ifNotSet) {\n    if (name === \"packed\") // clear cached before setting\n        this._packed = null;\n    return ReflectionObject.prototype.setOption.call(this, name, value, ifNotSet);\n};\n\n/**\n * Field descriptor.\n * @interface IField\n * @property {string} [rule=\"optional\"] Field rule\n * @property {string} type Field type\n * @property {number} id Field id\n * @property {Object.<string,*>} [options] Field options\n */\n\n/**\n * Extension field descriptor.\n * @interface IExtensionField\n * @extends IField\n * @property {string} extend Extended type\n */\n\n/**\n * Converts this field to a field descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IField} Field descriptor\n */\nField.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"rule\"    , this.rule !== \"optional\" && this.rule || undefined,\n        \"type\"    , this.type,\n        \"id\"      , this.id,\n        \"extend\"  , this.extend,\n        \"options\" , this.options,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Resolves this field's type references.\n * @returns {Field} `this`\n * @throws {Error} If any reference cannot be resolved\n */\nField.prototype.resolve = function resolve() {\n\n    if (this.resolved)\n        return this;\n\n    if ((this.typeDefault = types.defaults[this.type]) === undefined) { // if not a basic type, resolve it\n        this.resolvedType = (this.declaringField ? this.declaringField.parent : this.parent).lookupTypeOrEnum(this.type);\n        if (this.resolvedType instanceof Type)\n            this.typeDefault = null;\n        else // instanceof Enum\n            this.typeDefault = this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]; // first defined\n    }\n\n    // use explicitly set default value if present\n    if (this.options && this.options[\"default\"] != null) {\n        this.typeDefault = this.options[\"default\"];\n        if (this.resolvedType instanceof Enum && typeof this.typeDefault === \"string\")\n            this.typeDefault = this.resolvedType.values[this.typeDefault];\n    }\n\n    // remove unnecessary options\n    if (this.options) {\n        if (this.options.packed === true || this.options.packed !== undefined && this.resolvedType && !(this.resolvedType instanceof Enum))\n            delete this.options.packed;\n        if (!Object.keys(this.options).length)\n            this.options = undefined;\n    }\n\n    // convert to internal data type if necesssary\n    if (this.long) {\n        this.typeDefault = util.Long.fromNumber(this.typeDefault, this.type.charAt(0) === \"u\");\n\n        /* istanbul ignore else */\n        if (Object.freeze)\n            Object.freeze(this.typeDefault); // long instances are meant to be immutable anyway (i.e. use small int cache that even requires it)\n\n    } else if (this.bytes && typeof this.typeDefault === \"string\") {\n        var buf;\n        if (util.base64.test(this.typeDefault))\n            util.base64.decode(this.typeDefault, buf = util.newBuffer(util.base64.length(this.typeDefault)), 0);\n        else\n            util.utf8.write(this.typeDefault, buf = util.newBuffer(util.utf8.length(this.typeDefault)), 0);\n        this.typeDefault = buf;\n    }\n\n    // take special care of maps and repeated fields\n    if (this.map)\n        this.defaultValue = util.emptyObject;\n    else if (this.repeated)\n        this.defaultValue = util.emptyArray;\n    else\n        this.defaultValue = this.typeDefault;\n\n    // ensure proper value on prototype\n    if (this.parent instanceof Type)\n        this.parent.ctor.prototype[this.name] = this.defaultValue;\n\n    return ReflectionObject.prototype.resolve.call(this);\n};\n\n/**\n * Decorator function as returned by {@link Field.d} and {@link MapField.d} (TypeScript).\n * @typedef FieldDecorator\n * @type {function}\n * @param {Object} prototype Target prototype\n * @param {string} fieldName Field name\n * @returns {undefined}\n */\n\n/**\n * Field decorator (TypeScript).\n * @name Field.d\n * @function\n * @param {number} fieldId Field id\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"string\"|\"bool\"|\"bytes\"|Object} fieldType Field type\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\n * @param {T} [defaultValue] Default value\n * @returns {FieldDecorator} Decorator function\n * @template T extends number | number[] | Long | Long[] | string | string[] | boolean | boolean[] | Uint8Array | Uint8Array[] | Buffer | Buffer[]\n */\nField.d = function decorateField(fieldId, fieldType, fieldRule, defaultValue) {\n\n    // submessage: decorate the submessage and use its name as the type\n    if (typeof fieldType === \"function\")\n        fieldType = util.decorateType(fieldType).name;\n\n    // enum reference: create a reflected copy of the enum and keep reuseing it\n    else if (fieldType && typeof fieldType === \"object\")\n        fieldType = util.decorateEnum(fieldType).name;\n\n    return function fieldDecorator(prototype, fieldName) {\n        util.decorateType(prototype.constructor)\n            .add(new Field(fieldName, fieldId, fieldType, fieldRule, { \"default\": defaultValue }));\n    };\n};\n\n/**\n * Field decorator (TypeScript).\n * @name Field.d\n * @function\n * @param {number} fieldId Field id\n * @param {Constructor<T>|string} fieldType Field type\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\n * @returns {FieldDecorator} Decorator function\n * @template T extends Message<T>\n * @variation 2\n */\n// like Field.d but without a default value\n\n// Sets up cyclic dependencies (called in index-light)\nField._configure = function configure(Type_) {\n    Type = Type_;\n};\n", "\"use strict\";\nvar protobuf = module.exports = require(17);\n\nprotobuf.build = \"light\";\n\n/**\n * A node-style callback as used by {@link load} and {@link Root#load}.\n * @typedef LoadCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Root} [root] Root, if there hasn't been an error\n * @returns {undefined}\n */\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} root Root namespace, defaults to create a new one if omitted.\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @see {@link Root#load}\n */\nfunction load(filename, root, callback) {\n    if (typeof root === \"function\") {\n        callback = root;\n        root = new protobuf.Root();\n    } else if (!root)\n        root = new protobuf.Root();\n    return root.load(filename, callback);\n}\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\n * @name load\n * @function\n * @param {string|string[]} filename One or multiple files to load\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @see {@link Root#load}\n * @variation 2\n */\n// function load(filename:string, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and returns a promise.\n * @name load\n * @function\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\n * @returns {Promise<Root>} Promise\n * @see {@link Root#load}\n * @variation 3\n */\n// function load(filename:string, [root:Root]):Promise<Root>\n\nprotobuf.load = load;\n\n/**\n * Synchronously loads one or multiple .proto or preprocessed .json files into a common root namespace (node only).\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\n * @returns {Root} Root namespace\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\n * @see {@link Root#loadSync}\n */\nfunction loadSync(filename, root) {\n    if (!root)\n        root = new protobuf.Root();\n    return root.loadSync(filename);\n}\n\nprotobuf.loadSync = loadSync;\n\n// Serialization\nprotobuf.encoder          = require(13);\nprotobuf.decoder          = require(12);\nprotobuf.verifier         = require(36);\nprotobuf.converter        = require(11);\n\n// Reflection\nprotobuf.ReflectionObject = require(22);\nprotobuf.Namespace        = require(21);\nprotobuf.Root             = require(26);\nprotobuf.Enum             = require(14);\nprotobuf.Type             = require(31);\nprotobuf.Field            = require(15);\nprotobuf.OneOf            = require(23);\nprotobuf.MapField         = require(18);\nprotobuf.Service          = require(30);\nprotobuf.Method           = require(20);\n\n// Runtime\nprotobuf.Message          = require(19);\nprotobuf.wrappers         = require(37);\n\n// Utility\nprotobuf.types            = require(32);\nprotobuf.util             = require(33);\n\n// Set up possibly cyclic reflection dependencies\nprotobuf.ReflectionObject._configure(protobuf.Root);\nprotobuf.Namespace._configure(protobuf.Type, protobuf.Service, protobuf.Enum);\nprotobuf.Root._configure(protobuf.Type);\nprotobuf.Field._configure(protobuf.Type);\n", "\"use strict\";\nvar protobuf = exports;\n\n/**\n * Build type, one of `\"full\"`, `\"light\"` or `\"minimal\"`.\n * @name build\n * @type {string}\n * @const\n */\nprotobuf.build = \"minimal\";\n\n// Serialization\nprotobuf.Writer       = require(38);\nprotobuf.BufferWriter = require(39);\nprotobuf.Reader       = require(24);\nprotobuf.BufferReader = require(25);\n\n// Utility\nprotobuf.util         = require(35);\nprotobuf.rpc          = require(28);\nprotobuf.roots        = require(27);\nprotobuf.configure    = configure;\n\n/* istanbul ignore next */\n/**\n * Reconfigures the library according to the environment.\n * @returns {undefined}\n */\nfunction configure() {\n    protobuf.util._configure();\n    protobuf.Writer._configure(protobuf.BufferWriter);\n    protobuf.Reader._configure(protobuf.BufferReader);\n}\n\n// Set up buffer utility according to the environment\nconfigure();\n", "\"use strict\";\nmodule.exports = MapField;\n\n// extends Field\nvar Field = require(15);\n((MapField.prototype = Object.create(Field.prototype)).constructor = MapField).className = \"MapField\";\n\nvar types   = require(32),\n    util    = require(33);\n\n/**\n * Constructs a new map field instance.\n * @classdesc Reflected map field.\n * @extends FieldBase\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} keyType Key type\n * @param {string} type Value type\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction MapField(name, id, keyType, type, options, comment) {\n    Field.call(this, name, id, type, undefined, undefined, options, comment);\n\n    /* istanbul ignore if */\n    if (!util.isString(keyType))\n        throw TypeError(\"keyType must be a string\");\n\n    /**\n     * Key type.\n     * @type {string}\n     */\n    this.keyType = keyType; // toJSON, marker\n\n    /**\n     * Resolved key type if not a basic type.\n     * @type {ReflectionObject|null}\n     */\n    this.resolvedKeyType = null;\n\n    // Overrides Field#map\n    this.map = true;\n}\n\n/**\n * Map field descriptor.\n * @interface IMapField\n * @extends {IField}\n * @property {string} keyType Key type\n */\n\n/**\n * Extension map field descriptor.\n * @interface IExtensionMapField\n * @extends IMapField\n * @property {string} extend Extended type\n */\n\n/**\n * Constructs a map field from a map field descriptor.\n * @param {string} name Field name\n * @param {IMapField} json Map field descriptor\n * @returns {MapField} Created map field\n * @throws {TypeError} If arguments are invalid\n */\nMapField.fromJSON = function fromJSON(name, json) {\n    return new MapField(name, json.id, json.keyType, json.type, json.options, json.comment);\n};\n\n/**\n * Converts this map field to a map field descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IMapField} Map field descriptor\n */\nMapField.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"keyType\" , this.keyType,\n        \"type\"    , this.type,\n        \"id\"      , this.id,\n        \"extend\"  , this.extend,\n        \"options\" , this.options,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nMapField.prototype.resolve = function resolve() {\n    if (this.resolved)\n        return this;\n\n    // Besides a value type, map fields have a key type that may be \"any scalar type except for floating point types and bytes\"\n    if (types.mapKey[this.keyType] === undefined)\n        throw Error(\"invalid key type: \" + this.keyType);\n\n    return Field.prototype.resolve.call(this);\n};\n\n/**\n * Map field decorator (TypeScript).\n * @name MapField.d\n * @function\n * @param {number} fieldId Field id\n * @param {\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"} fieldKeyType Field key type\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"|\"bytes\"|Object|Constructor<{}>} fieldValueType Field value type\n * @returns {FieldDecorator} Decorator function\n * @template T extends { [key: string]: number | Long | string | boolean | Uint8Array | Buffer | number[] | Message<{}> }\n */\nMapField.d = function decorateMapField(fieldId, fieldKeyType, fieldValueType) {\n\n    // submessage value: decorate the submessage and use its name as the type\n    if (typeof fieldValueType === \"function\")\n        fieldValueType = util.decorateType(fieldValueType).name;\n\n    // enum reference value: create a reflected copy of the enum and keep reuseing it\n    else if (fieldValueType && typeof fieldValueType === \"object\")\n        fieldValueType = util.decorateEnum(fieldValueType).name;\n\n    return function mapFieldDecorator(prototype, fieldName) {\n        util.decorateType(prototype.constructor)\n            .add(new MapField(fieldName, fieldId, fieldKeyType, fieldValueType));\n    };\n};\n", "\"use strict\";\nmodule.exports = Message;\n\nvar util = require(35);\n\n/**\n * Constructs a new message instance.\n * @classdesc Abstract runtime message.\n * @constructor\n * @param {Properties<T>} [properties] Properties to set\n * @template T extends object = object\n */\nfunction Message(properties) {\n    // not used internally\n    if (properties)\n        for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n            this[keys[i]] = properties[keys[i]];\n}\n\n/**\n * Reference to the reflected type.\n * @name Message.$type\n * @type {Type}\n * @readonly\n */\n\n/**\n * Reference to the reflected type.\n * @name Message#$type\n * @type {Type}\n * @readonly\n */\n\n/*eslint-disable valid-jsdoc*/\n\n/**\n * Creates a new message of this type using the specified properties.\n * @param {Object.<string,*>} [properties] Properties to set\n * @returns {Message<T>} Message instance\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.create = function create(properties) {\n    return this.$type.create(properties);\n};\n\n/**\n * Encodes a message of this type.\n * @param {T|Object.<string,*>} message Message to encode\n * @param {Writer} [writer] Writer to use\n * @returns {Writer} Writer\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.encode = function encode(message, writer) {\n    return this.$type.encode(message, writer);\n};\n\n/**\n * Encodes a message of this type preceeded by its length as a varint.\n * @param {T|Object.<string,*>} message Message to encode\n * @param {Writer} [writer] Writer to use\n * @returns {Writer} Writer\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.encodeDelimited = function encodeDelimited(message, writer) {\n    return this.$type.encodeDelimited(message, writer);\n};\n\n/**\n * Decodes a message of this type.\n * @name Message.decode\n * @function\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\n * @returns {T} Decoded message\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.decode = function decode(reader) {\n    return this.$type.decode(reader);\n};\n\n/**\n * Decodes a message of this type preceeded by its length as a varint.\n * @name Message.decodeDelimited\n * @function\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\n * @returns {T} Decoded message\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.decodeDelimited = function decodeDelimited(reader) {\n    return this.$type.decodeDelimited(reader);\n};\n\n/**\n * Verifies a message of this type.\n * @name Message.verify\n * @function\n * @param {Object.<string,*>} message Plain object to verify\n * @returns {string|null} `null` if valid, otherwise the reason why it is not\n */\nMessage.verify = function verify(message) {\n    return this.$type.verify(message);\n};\n\n/**\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\n * @param {Object.<string,*>} object Plain object\n * @returns {T} Message instance\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.fromObject = function fromObject(object) {\n    return this.$type.fromObject(object);\n};\n\n/**\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\n * @param {T} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.toObject = function toObject(message, options) {\n    return this.$type.toObject(message, options);\n};\n\n/**\n * Converts this message to JSON.\n * @returns {Object.<string,*>} JSON object\n */\nMessage.prototype.toJSON = function toJSON() {\n    return this.$type.toObject(this, util.toJSONOptions);\n};\n\n/*eslint-enable valid-jsdoc*/", "\"use strict\";\nmodule.exports = Method;\n\n// extends ReflectionObject\nvar ReflectionObject = require(22);\n((Method.prototype = Object.create(ReflectionObject.prototype)).constructor = Method).className = \"Method\";\n\nvar util = require(33);\n\n/**\n * Constructs a new service method instance.\n * @classdesc Reflected service method.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Method name\n * @param {string|undefined} type Method type, usually `\"rpc\"`\n * @param {string} requestType Request message type\n * @param {string} responseType Response message type\n * @param {boolean|Object.<string,*>} [requestStream] Whether the request is streamed\n * @param {boolean|Object.<string,*>} [responseStream] Whether the response is streamed\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] The comment for this method\n * @param {Object.<string,*>} [parsedOptions] Declared options, properly parsed into an object\n */\nfunction Method(name, type, requestType, responseType, requestStream, responseStream, options, comment, parsedOptions) {\n\n    /* istanbul ignore next */\n    if (util.isObject(requestStream)) {\n        options = requestStream;\n        requestStream = responseStream = undefined;\n    } else if (util.isObject(responseStream)) {\n        options = responseStream;\n        responseStream = undefined;\n    }\n\n    /* istanbul ignore if */\n    if (!(type === undefined || util.isString(type)))\n        throw TypeError(\"type must be a string\");\n\n    /* istanbul ignore if */\n    if (!util.isString(requestType))\n        throw TypeError(\"requestType must be a string\");\n\n    /* istanbul ignore if */\n    if (!util.isString(responseType))\n        throw TypeError(\"responseType must be a string\");\n\n    ReflectionObject.call(this, name, options);\n\n    /**\n     * Method type.\n     * @type {string}\n     */\n    this.type = type || \"rpc\"; // toJSON\n\n    /**\n     * Request type.\n     * @type {string}\n     */\n    this.requestType = requestType; // toJSON, marker\n\n    /**\n     * Whether requests are streamed or not.\n     * @type {boolean|undefined}\n     */\n    this.requestStream = requestStream ? true : undefined; // toJSON\n\n    /**\n     * Response type.\n     * @type {string}\n     */\n    this.responseType = responseType; // toJSON\n\n    /**\n     * Whether responses are streamed or not.\n     * @type {boolean|undefined}\n     */\n    this.responseStream = responseStream ? true : undefined; // toJSON\n\n    /**\n     * Resolved request type.\n     * @type {Type|null}\n     */\n    this.resolvedRequestType = null;\n\n    /**\n     * Resolved response type.\n     * @type {Type|null}\n     */\n    this.resolvedResponseType = null;\n\n    /**\n     * Comment for this method\n     * @type {string|null}\n     */\n    this.comment = comment;\n\n    /**\n     * Options properly parsed into an object\n     */\n    this.parsedOptions = parsedOptions;\n}\n\n/**\n * Method descriptor.\n * @interface IMethod\n * @property {string} [type=\"rpc\"] Method type\n * @property {string} requestType Request type\n * @property {string} responseType Response type\n * @property {boolean} [requestStream=false] Whether requests are streamed\n * @property {boolean} [responseStream=false] Whether responses are streamed\n * @property {Object.<string,*>} [options] Method options\n * @property {string} comment Method comments\n * @property {Object.<string,*>} [parsedOptions] Method options properly parsed into an object\n */\n\n/**\n * Constructs a method from a method descriptor.\n * @param {string} name Method name\n * @param {IMethod} json Method descriptor\n * @returns {Method} Created method\n * @throws {TypeError} If arguments are invalid\n */\nMethod.fromJSON = function fromJSON(name, json) {\n    return new Method(name, json.type, json.requestType, json.responseType, json.requestStream, json.responseStream, json.options, json.comment, json.parsedOptions);\n};\n\n/**\n * Converts this method to a method descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IMethod} Method descriptor\n */\nMethod.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"type\"           , this.type !== \"rpc\" && /* istanbul ignore next */ this.type || undefined,\n        \"requestType\"    , this.requestType,\n        \"requestStream\"  , this.requestStream,\n        \"responseType\"   , this.responseType,\n        \"responseStream\" , this.responseStream,\n        \"options\"        , this.options,\n        \"comment\"        , keepComments ? this.comment : undefined,\n        \"parsedOptions\"  , this.parsedOptions,\n    ]);\n};\n\n/**\n * @override\n */\nMethod.prototype.resolve = function resolve() {\n\n    /* istanbul ignore if */\n    if (this.resolved)\n        return this;\n\n    this.resolvedRequestType = this.parent.lookupType(this.requestType);\n    this.resolvedResponseType = this.parent.lookupType(this.responseType);\n\n    return ReflectionObject.prototype.resolve.call(this);\n};\n", "\"use strict\";\nmodule.exports = Namespace;\n\n// extends ReflectionObject\nvar ReflectionObject = require(22);\n((Namespace.prototype = Object.create(ReflectionObject.prototype)).constructor = Namespace).className = \"Namespace\";\n\nvar Field    = require(15),\n    OneOf    = require(23),\n    util     = require(33);\n\nvar Type,    // cyclic\n    Service,\n    Enum;\n\n/**\n * Constructs a new namespace instance.\n * @name Namespace\n * @classdesc Reflected namespace.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Namespace name\n * @param {Object.<string,*>} [options] Declared options\n */\n\n/**\n * Constructs a namespace from JSON.\n * @memberof Namespace\n * @function\n * @param {string} name Namespace name\n * @param {Object.<string,*>} json JSON object\n * @returns {Namespace} Created namespace\n * @throws {TypeError} If arguments are invalid\n */\nNamespace.fromJSON = function fromJSON(name, json) {\n    return new Namespace(name, json.options).addJSON(json.nested);\n};\n\n/**\n * Converts an array of reflection objects to JSON.\n * @memberof Namespace\n * @param {ReflectionObject[]} array Object array\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {Object.<string,*>|undefined} JSON object or `undefined` when array is empty\n */\nfunction arrayToJSON(array, toJSONOptions) {\n    if (!(array && array.length))\n        return undefined;\n    var obj = {};\n    for (var i = 0; i < array.length; ++i)\n        obj[array[i].name] = array[i].toJSON(toJSONOptions);\n    return obj;\n}\n\nNamespace.arrayToJSON = arrayToJSON;\n\n/**\n * Tests if the specified id is reserved.\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nNamespace.isReservedId = function isReservedId(reserved, id) {\n    if (reserved)\n        for (var i = 0; i < reserved.length; ++i)\n            if (typeof reserved[i] !== \"string\" && reserved[i][0] <= id && reserved[i][1] > id)\n                return true;\n    return false;\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nNamespace.isReservedName = function isReservedName(reserved, name) {\n    if (reserved)\n        for (var i = 0; i < reserved.length; ++i)\n            if (reserved[i] === name)\n                return true;\n    return false;\n};\n\n/**\n * Not an actual constructor. Use {@link Namespace} instead.\n * @classdesc Base class of all reflection objects containing nested objects. This is not an actual class but here for the sake of having consistent type definitions.\n * @exports NamespaceBase\n * @extends ReflectionObject\n * @abstract\n * @constructor\n * @param {string} name Namespace name\n * @param {Object.<string,*>} [options] Declared options\n * @see {@link Namespace}\n */\nfunction Namespace(name, options) {\n    ReflectionObject.call(this, name, options);\n\n    /**\n     * Nested objects by name.\n     * @type {Object.<string,ReflectionObject>|undefined}\n     */\n    this.nested = undefined; // toJSON\n\n    /**\n     * Cached nested objects as an array.\n     * @type {ReflectionObject[]|null}\n     * @private\n     */\n    this._nestedArray = null;\n}\n\nfunction clearCache(namespace) {\n    namespace._nestedArray = null;\n    return namespace;\n}\n\n/**\n * Nested objects of this namespace as an array for iteration.\n * @name NamespaceBase#nestedArray\n * @type {ReflectionObject[]}\n * @readonly\n */\nObject.defineProperty(Namespace.prototype, \"nestedArray\", {\n    get: function() {\n        return this._nestedArray || (this._nestedArray = util.toArray(this.nested));\n    }\n});\n\n/**\n * Namespace descriptor.\n * @interface INamespace\n * @property {Object.<string,*>} [options] Namespace options\n * @property {Object.<string,AnyNestedObject>} [nested] Nested object descriptors\n */\n\n/**\n * Any extension field descriptor.\n * @typedef AnyExtensionField\n * @type {IExtensionField|IExtensionMapField}\n */\n\n/**\n * Any nested object descriptor.\n * @typedef AnyNestedObject\n * @type {IEnum|IType|IService|AnyExtensionField|INamespace}\n */\n// ^ BEWARE: VSCode hangs forever when using more than 5 types (that's why AnyExtensionField exists in the first place)\n\n/**\n * Converts this namespace to a namespace descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {INamespace} Namespace descriptor\n */\nNamespace.prototype.toJSON = function toJSON(toJSONOptions) {\n    return util.toObject([\n        \"options\" , this.options,\n        \"nested\"  , arrayToJSON(this.nestedArray, toJSONOptions)\n    ]);\n};\n\n/**\n * Adds nested objects to this namespace from nested object descriptors.\n * @param {Object.<string,AnyNestedObject>} nestedJson Any nested object descriptors\n * @returns {Namespace} `this`\n */\nNamespace.prototype.addJSON = function addJSON(nestedJson) {\n    var ns = this;\n    /* istanbul ignore else */\n    if (nestedJson) {\n        for (var names = Object.keys(nestedJson), i = 0, nested; i < names.length; ++i) {\n            nested = nestedJson[names[i]];\n            ns.add( // most to least likely\n                ( nested.fields !== undefined\n                ? Type.fromJSON\n                : nested.values !== undefined\n                ? Enum.fromJSON\n                : nested.methods !== undefined\n                ? Service.fromJSON\n                : nested.id !== undefined\n                ? Field.fromJSON\n                : Namespace.fromJSON )(names[i], nested)\n            );\n        }\n    }\n    return this;\n};\n\n/**\n * Gets the nested object of the specified name.\n * @param {string} name Nested object name\n * @returns {ReflectionObject|null} The reflection object or `null` if it doesn't exist\n */\nNamespace.prototype.get = function get(name) {\n    return this.nested && this.nested[name]\n        || null;\n};\n\n/**\n * Gets the values of the nested {@link Enum|enum} of the specified name.\n * This methods differs from {@link Namespace#get|get} in that it returns an enum's values directly and throws instead of returning `null`.\n * @param {string} name Nested enum name\n * @returns {Object.<string,number>} Enum values\n * @throws {Error} If there is no such enum\n */\nNamespace.prototype.getEnum = function getEnum(name) {\n    if (this.nested && this.nested[name] instanceof Enum)\n        return this.nested[name].values;\n    throw Error(\"no such enum: \" + name);\n};\n\n/**\n * Adds a nested object to this namespace.\n * @param {ReflectionObject} object Nested object to add\n * @returns {Namespace} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a nested object with this name\n */\nNamespace.prototype.add = function add(object) {\n\n    if (!(object instanceof Field && object.extend !== undefined || object instanceof Type || object instanceof Enum || object instanceof Service || object instanceof Namespace || object instanceof OneOf))\n        throw TypeError(\"object must be a valid nested object\");\n\n    if (!this.nested)\n        this.nested = {};\n    else {\n        var prev = this.get(object.name);\n        if (prev) {\n            if (prev instanceof Namespace && object instanceof Namespace && !(prev instanceof Type || prev instanceof Service)) {\n                // replace plain namespace but keep existing nested elements and options\n                var nested = prev.nestedArray;\n                for (var i = 0; i < nested.length; ++i)\n                    object.add(nested[i]);\n                this.remove(prev);\n                if (!this.nested)\n                    this.nested = {};\n                object.setOptions(prev.options, true);\n\n            } else\n                throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n        }\n    }\n    this.nested[object.name] = object;\n    object.onAdd(this);\n    return clearCache(this);\n};\n\n/**\n * Removes a nested object from this namespace.\n * @param {ReflectionObject} object Nested object to remove\n * @returns {Namespace} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `object` is not a member of this namespace\n */\nNamespace.prototype.remove = function remove(object) {\n\n    if (!(object instanceof ReflectionObject))\n        throw TypeError(\"object must be a ReflectionObject\");\n    if (object.parent !== this)\n        throw Error(object + \" is not a member of \" + this);\n\n    delete this.nested[object.name];\n    if (!Object.keys(this.nested).length)\n        this.nested = undefined;\n\n    object.onRemove(this);\n    return clearCache(this);\n};\n\n/**\n * Defines additial namespaces within this one if not yet existing.\n * @param {string|string[]} path Path to create\n * @param {*} [json] Nested types to create from JSON\n * @returns {Namespace} Pointer to the last namespace created or `this` if path is empty\n */\nNamespace.prototype.define = function define(path, json) {\n\n    if (util.isString(path))\n        path = path.split(\".\");\n    else if (!Array.isArray(path))\n        throw TypeError(\"illegal path\");\n    if (path && path.length && path[0] === \"\")\n        throw Error(\"path must be relative\");\n\n    var ptr = this;\n    while (path.length > 0) {\n        var part = path.shift();\n        if (ptr.nested && ptr.nested[part]) {\n            ptr = ptr.nested[part];\n            if (!(ptr instanceof Namespace))\n                throw Error(\"path conflicts with non-namespace objects\");\n        } else\n            ptr.add(ptr = new Namespace(part));\n    }\n    if (json)\n        ptr.addJSON(json);\n    return ptr;\n};\n\n/**\n * Resolves this namespace's and all its nested objects' type references. Useful to validate a reflection tree, but comes at a cost.\n * @returns {Namespace} `this`\n */\nNamespace.prototype.resolveAll = function resolveAll() {\n    var nested = this.nestedArray, i = 0;\n    while (i < nested.length)\n        if (nested[i] instanceof Namespace)\n            nested[i++].resolveAll();\n        else\n            nested[i++].resolve();\n    return this.resolve();\n};\n\n/**\n * Recursively looks up the reflection object matching the specified path in the scope of this namespace.\n * @param {string|string[]} path Path to look up\n * @param {*|Array.<*>} filterTypes Filter types, any combination of the constructors of `protobuf.Type`, `protobuf.Enum`, `protobuf.Service` etc.\n * @param {boolean} [parentAlreadyChecked=false] If known, whether the parent has already been checked\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n */\nNamespace.prototype.lookup = function lookup(path, filterTypes, parentAlreadyChecked) {\n\n    /* istanbul ignore next */\n    if (typeof filterTypes === \"boolean\") {\n        parentAlreadyChecked = filterTypes;\n        filterTypes = undefined;\n    } else if (filterTypes && !Array.isArray(filterTypes))\n        filterTypes = [ filterTypes ];\n\n    if (util.isString(path) && path.length) {\n        if (path === \".\")\n            return this.root;\n        path = path.split(\".\");\n    } else if (!path.length)\n        return this;\n\n    // Start at root if path is absolute\n    if (path[0] === \"\")\n        return this.root.lookup(path.slice(1), filterTypes);\n\n    // Test if the first part matches any nested object, and if so, traverse if path contains more\n    var found = this.get(path[0]);\n    if (found) {\n        if (path.length === 1) {\n            if (!filterTypes || filterTypes.indexOf(found.constructor) > -1)\n                return found;\n        } else if (found instanceof Namespace && (found = found.lookup(path.slice(1), filterTypes, true)))\n            return found;\n\n    // Otherwise try each nested namespace\n    } else\n        for (var i = 0; i < this.nestedArray.length; ++i)\n            if (this._nestedArray[i] instanceof Namespace && (found = this._nestedArray[i].lookup(path, filterTypes, true)))\n                return found;\n\n    // If there hasn't been a match, try again at the parent\n    if (this.parent === null || parentAlreadyChecked)\n        return null;\n    return this.parent.lookup(path, filterTypes);\n};\n\n/**\n * Looks up the reflection object at the specified path, relative to this namespace.\n * @name NamespaceBase#lookup\n * @function\n * @param {string|string[]} path Path to look up\n * @param {boolean} [parentAlreadyChecked=false] Whether the parent has already been checked\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n * @variation 2\n */\n// lookup(path: string, [parentAlreadyChecked: boolean])\n\n/**\n * Looks up the {@link Type|type} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Type} Looked up type\n * @throws {Error} If `path` does not point to a type\n */\nNamespace.prototype.lookupType = function lookupType(path) {\n    var found = this.lookup(path, [ Type ]);\n    if (!found)\n        throw Error(\"no such type: \" + path);\n    return found;\n};\n\n/**\n * Looks up the values of the {@link Enum|enum} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Enum} Looked up enum\n * @throws {Error} If `path` does not point to an enum\n */\nNamespace.prototype.lookupEnum = function lookupEnum(path) {\n    var found = this.lookup(path, [ Enum ]);\n    if (!found)\n        throw Error(\"no such Enum '\" + path + \"' in \" + this);\n    return found;\n};\n\n/**\n * Looks up the {@link Type|type} or {@link Enum|enum} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Type} Looked up type or enum\n * @throws {Error} If `path` does not point to a type or enum\n */\nNamespace.prototype.lookupTypeOrEnum = function lookupTypeOrEnum(path) {\n    var found = this.lookup(path, [ Type, Enum ]);\n    if (!found)\n        throw Error(\"no such Type or Enum '\" + path + \"' in \" + this);\n    return found;\n};\n\n/**\n * Looks up the {@link Service|service} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Service} Looked up service\n * @throws {Error} If `path` does not point to a service\n */\nNamespace.prototype.lookupService = function lookupService(path) {\n    var found = this.lookup(path, [ Service ]);\n    if (!found)\n        throw Error(\"no such Service '\" + path + \"' in \" + this);\n    return found;\n};\n\n// Sets up cyclic dependencies (called in index-light)\nNamespace._configure = function(Type_, Service_, Enum_) {\n    Type    = Type_;\n    Service = Service_;\n    Enum    = Enum_;\n};\n", "\"use strict\";\nmodule.exports = ReflectionObject;\n\nReflectionObject.className = \"ReflectionObject\";\n\nvar util = require(33);\n\nvar Root; // cyclic\n\n/**\n * Constructs a new reflection object instance.\n * @classdesc Base class of all reflection objects.\n * @constructor\n * @param {string} name Object name\n * @param {Object.<string,*>} [options] Declared options\n * @abstract\n */\nfunction ReflectionObject(name, options) {\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    if (options && !util.isObject(options))\n        throw TypeError(\"options must be an object\");\n\n    /**\n     * Options.\n     * @type {Object.<string,*>|undefined}\n     */\n    this.options = options; // toJSON\n\n    /**\n     * Parsed Options.\n     * @type {Array.<Object.<string,*>>|undefined}\n     */\n    this.parsedOptions = null;\n\n    /**\n     * Unique name within its namespace.\n     * @type {string}\n     */\n    this.name = name;\n\n    /**\n     * Parent namespace.\n     * @type {Namespace|null}\n     */\n    this.parent = null;\n\n    /**\n     * Whether already resolved or not.\n     * @type {boolean}\n     */\n    this.resolved = false;\n\n    /**\n     * Comment text, if any.\n     * @type {string|null}\n     */\n    this.comment = null;\n\n    /**\n     * Defining file name.\n     * @type {string|null}\n     */\n    this.filename = null;\n}\n\nObject.defineProperties(ReflectionObject.prototype, {\n\n    /**\n     * Reference to the root namespace.\n     * @name ReflectionObject#root\n     * @type {Root}\n     * @readonly\n     */\n    root: {\n        get: function() {\n            var ptr = this;\n            while (ptr.parent !== null)\n                ptr = ptr.parent;\n            return ptr;\n        }\n    },\n\n    /**\n     * Full name including leading dot.\n     * @name ReflectionObject#fullName\n     * @type {string}\n     * @readonly\n     */\n    fullName: {\n        get: function() {\n            var path = [ this.name ],\n                ptr = this.parent;\n            while (ptr) {\n                path.unshift(ptr.name);\n                ptr = ptr.parent;\n            }\n            return path.join(\".\");\n        }\n    }\n});\n\n/**\n * Converts this reflection object to its descriptor representation.\n * @returns {Object.<string,*>} Descriptor\n * @abstract\n */\nReflectionObject.prototype.toJSON = /* istanbul ignore next */ function toJSON() {\n    throw Error(); // not implemented, shouldn't happen\n};\n\n/**\n * Called when this object is added to a parent.\n * @param {ReflectionObject} parent Parent added to\n * @returns {undefined}\n */\nReflectionObject.prototype.onAdd = function onAdd(parent) {\n    if (this.parent && this.parent !== parent)\n        this.parent.remove(this);\n    this.parent = parent;\n    this.resolved = false;\n    var root = parent.root;\n    if (root instanceof Root)\n        root._handleAdd(this);\n};\n\n/**\n * Called when this object is removed from a parent.\n * @param {ReflectionObject} parent Parent removed from\n * @returns {undefined}\n */\nReflectionObject.prototype.onRemove = function onRemove(parent) {\n    var root = parent.root;\n    if (root instanceof Root)\n        root._handleRemove(this);\n    this.parent = null;\n    this.resolved = false;\n};\n\n/**\n * Resolves this objects type references.\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.resolve = function resolve() {\n    if (this.resolved)\n        return this;\n    if (this.root instanceof Root)\n        this.resolved = true; // only if part of a root\n    return this;\n};\n\n/**\n * Gets an option value.\n * @param {string} name Option name\n * @returns {*} Option value or `undefined` if not set\n */\nReflectionObject.prototype.getOption = function getOption(name) {\n    if (this.options)\n        return this.options[name];\n    return undefined;\n};\n\n/**\n * Sets an option.\n * @param {string} name Option name\n * @param {*} value Option value\n * @param {boolean} [ifNotSet] Sets the option only if it isn't currently set\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setOption = function setOption(name, value, ifNotSet) {\n    if (!ifNotSet || !this.options || this.options[name] === undefined)\n        (this.options || (this.options = {}))[name] = value;\n    return this;\n};\n\n/**\n * Sets a parsed option.\n * @param {string} name parsed Option name\n * @param {*} value Option value\n * @param {string} propName dot '.' delimited full path of property within the option to set. if undefined\\empty, will add a new option with that value\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setParsedOption = function setParsedOption(name, value, propName) {\n    if (!this.parsedOptions) {\n        this.parsedOptions = [];\n    }\n    var parsedOptions = this.parsedOptions;\n    if (propName) {\n        // If setting a sub property of an option then try to merge it\n        // with an existing option\n        var opt = parsedOptions.find(function (opt) {\n            return Object.prototype.hasOwnProperty.call(opt, name);\n        });\n        if (opt) {\n            // If we found an existing option - just merge the property value\n            var newValue = opt[name];\n            util.setProperty(newValue, propName, value);\n        } else {\n            // otherwise, create a new option, set it's property and add it to the list\n            opt = {};\n            opt[name] = util.setProperty({}, propName, value);\n            parsedOptions.push(opt);\n        }\n    } else {\n        // Always create a new option when setting the value of the option itself\n        var newOpt = {};\n        newOpt[name] = value;\n        parsedOptions.push(newOpt);\n    }\n    return this;\n};\n\n/**\n * Sets multiple options.\n * @param {Object.<string,*>} options Options to set\n * @param {boolean} [ifNotSet] Sets an option only if it isn't currently set\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setOptions = function setOptions(options, ifNotSet) {\n    if (options)\n        for (var keys = Object.keys(options), i = 0; i < keys.length; ++i)\n            this.setOption(keys[i], options[keys[i]], ifNotSet);\n    return this;\n};\n\n/**\n * Converts this instance to its string representation.\n * @returns {string} Class name[, space, full name]\n */\nReflectionObject.prototype.toString = function toString() {\n    var className = this.constructor.className,\n        fullName  = this.fullName;\n    if (fullName.length)\n        return className + \" \" + fullName;\n    return className;\n};\n\n// Sets up cyclic dependencies (called in index-light)\nReflectionObject._configure = function(Root_) {\n    Root = Root_;\n};\n", "\"use strict\";\nmodule.exports = OneOf;\n\n// extends ReflectionObject\nvar ReflectionObject = require(22);\n((OneOf.prototype = Object.create(ReflectionObject.prototype)).constructor = OneOf).className = \"OneOf\";\n\nvar Field = require(15),\n    util  = require(33);\n\n/**\n * Constructs a new oneof instance.\n * @classdesc Reflected oneof.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Oneof name\n * @param {string[]|Object.<string,*>} [fieldNames] Field names\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction OneOf(name, fieldNames, options, comment) {\n    if (!Array.isArray(fieldNames)) {\n        options = fieldNames;\n        fieldNames = undefined;\n    }\n    ReflectionObject.call(this, name, options);\n\n    /* istanbul ignore if */\n    if (!(fieldNames === undefined || Array.isArray(fieldNames)))\n        throw TypeError(\"fieldNames must be an Array\");\n\n    /**\n     * Field names that belong to this oneof.\n     * @type {string[]}\n     */\n    this.oneof = fieldNames || []; // toJSON, marker\n\n    /**\n     * Fields that belong to this oneof as an array for iteration.\n     * @type {Field[]}\n     * @readonly\n     */\n    this.fieldsArray = []; // declared readonly for conformance, possibly not yet added to parent\n\n    /**\n     * Comment for this field.\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Oneof descriptor.\n * @interface IOneOf\n * @property {Array.<string>} oneof Oneof field names\n * @property {Object.<string,*>} [options] Oneof options\n */\n\n/**\n * Constructs a oneof from a oneof descriptor.\n * @param {string} name Oneof name\n * @param {IOneOf} json Oneof descriptor\n * @returns {OneOf} Created oneof\n * @throws {TypeError} If arguments are invalid\n */\nOneOf.fromJSON = function fromJSON(name, json) {\n    return new OneOf(name, json.oneof, json.options, json.comment);\n};\n\n/**\n * Converts this oneof to a oneof descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IOneOf} Oneof descriptor\n */\nOneOf.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\" , this.options,\n        \"oneof\"   , this.oneof,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Adds the fields of the specified oneof to the parent if not already done so.\n * @param {OneOf} oneof The oneof\n * @returns {undefined}\n * @inner\n * @ignore\n */\nfunction addFieldsToParent(oneof) {\n    if (oneof.parent)\n        for (var i = 0; i < oneof.fieldsArray.length; ++i)\n            if (!oneof.fieldsArray[i].parent)\n                oneof.parent.add(oneof.fieldsArray[i]);\n}\n\n/**\n * Adds a field to this oneof and removes it from its current parent, if any.\n * @param {Field} field Field to add\n * @returns {OneOf} `this`\n */\nOneOf.prototype.add = function add(field) {\n\n    /* istanbul ignore if */\n    if (!(field instanceof Field))\n        throw TypeError(\"field must be a Field\");\n\n    if (field.parent && field.parent !== this.parent)\n        field.parent.remove(field);\n    this.oneof.push(field.name);\n    this.fieldsArray.push(field);\n    field.partOf = this; // field.parent remains null\n    addFieldsToParent(this);\n    return this;\n};\n\n/**\n * Removes a field from this oneof and puts it back to the oneof's parent.\n * @param {Field} field Field to remove\n * @returns {OneOf} `this`\n */\nOneOf.prototype.remove = function remove(field) {\n\n    /* istanbul ignore if */\n    if (!(field instanceof Field))\n        throw TypeError(\"field must be a Field\");\n\n    var index = this.fieldsArray.indexOf(field);\n\n    /* istanbul ignore if */\n    if (index < 0)\n        throw Error(field + \" is not a member of \" + this);\n\n    this.fieldsArray.splice(index, 1);\n    index = this.oneof.indexOf(field.name);\n\n    /* istanbul ignore else */\n    if (index > -1) // theoretical\n        this.oneof.splice(index, 1);\n\n    field.partOf = null;\n    return this;\n};\n\n/**\n * @override\n */\nOneOf.prototype.onAdd = function onAdd(parent) {\n    ReflectionObject.prototype.onAdd.call(this, parent);\n    var self = this;\n    // Collect present fields\n    for (var i = 0; i < this.oneof.length; ++i) {\n        var field = parent.get(this.oneof[i]);\n        if (field && !field.partOf) {\n            field.partOf = self;\n            self.fieldsArray.push(field);\n        }\n    }\n    // Add not yet present fields\n    addFieldsToParent(this);\n};\n\n/**\n * @override\n */\nOneOf.prototype.onRemove = function onRemove(parent) {\n    for (var i = 0, field; i < this.fieldsArray.length; ++i)\n        if ((field = this.fieldsArray[i]).parent)\n            field.parent.remove(field);\n    ReflectionObject.prototype.onRemove.call(this, parent);\n};\n\n/**\n * Decorator function as returned by {@link OneOf.d} (TypeScript).\n * @typedef OneOfDecorator\n * @type {function}\n * @param {Object} prototype Target prototype\n * @param {string} oneofName OneOf name\n * @returns {undefined}\n */\n\n/**\n * OneOf decorator (TypeScript).\n * @function\n * @param {...string} fieldNames Field names\n * @returns {OneOfDecorator} Decorator function\n * @template T extends string\n */\nOneOf.d = function decorateOneOf() {\n    var fieldNames = new Array(arguments.length),\n        index = 0;\n    while (index < arguments.length)\n        fieldNames[index] = arguments[index++];\n    return function oneOfDecorator(prototype, oneofName) {\n        util.decorateType(prototype.constructor)\n            .add(new OneOf(oneofName, fieldNames));\n        Object.defineProperty(prototype, oneofName, {\n            get: util.oneOfGetter(fieldNames),\n            set: util.oneOfSetter(fieldNames)\n        });\n    };\n};\n", "\"use strict\";\nmodule.exports = Reader;\n\nvar util      = require(35);\n\nvar BufferReader; // cyclic\n\nvar LongBits  = util.LongBits,\n    utf8      = util.utf8;\n\n/* istanbul ignore next */\nfunction indexOutOfRange(reader, writeLength) {\n    return RangeError(\"index out of range: \" + reader.pos + \" + \" + (writeLength || 1) + \" > \" + reader.len);\n}\n\n/**\n * Constructs a new reader instance using the specified buffer.\n * @classdesc Wire format reader using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n * @param {Uint8Array} buffer Buffer to read from\n */\nfunction Reader(buffer) {\n\n    /**\n     * Read buffer.\n     * @type {Uint8Array}\n     */\n    this.buf = buffer;\n\n    /**\n     * Read buffer position.\n     * @type {number}\n     */\n    this.pos = 0;\n\n    /**\n     * Read buffer length.\n     * @type {number}\n     */\n    this.len = buffer.length;\n}\n\nvar create_array = typeof Uint8Array !== \"undefined\"\n    ? function create_typed_array(buffer) {\n        if (buffer instanceof Uint8Array || Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    }\n    /* istanbul ignore next */\n    : function create_array(buffer) {\n        if (Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    };\n\nvar create = function create() {\n    return util.Buffer\n        ? function create_buffer_setup(buffer) {\n            return (Reader.create = function create_buffer(buffer) {\n                return util.Buffer.isBuffer(buffer)\n                    ? new BufferReader(buffer)\n                    /* istanbul ignore next */\n                    : create_array(buffer);\n            })(buffer);\n        }\n        /* istanbul ignore next */\n        : create_array;\n};\n\n/**\n * Creates a new reader using the specified buffer.\n * @function\n * @param {Uint8Array|Buffer} buffer Buffer to read from\n * @returns {Reader|BufferReader} A {@link BufferReader} if `buffer` is a Buffer, otherwise a {@link Reader}\n * @throws {Error} If `buffer` is not a valid buffer\n */\nReader.create = create();\n\nReader.prototype._slice = util.Array.prototype.subarray || /* istanbul ignore next */ util.Array.prototype.slice;\n\n/**\n * Reads a varint as an unsigned 32 bit value.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.uint32 = (function read_uint32_setup() {\n    var value = 4294967295; // optimizer type-hint, tends to deopt otherwise (?!)\n    return function read_uint32() {\n        value = (         this.buf[this.pos] & 127       ) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) <<  7) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 14) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 21) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] &  15) << 28) >>> 0; if (this.buf[this.pos++] < 128) return value;\n\n        /* istanbul ignore if */\n        if ((this.pos += 5) > this.len) {\n            this.pos = this.len;\n            throw indexOutOfRange(this, 10);\n        }\n        return value;\n    };\n})();\n\n/**\n * Reads a varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.int32 = function read_int32() {\n    return this.uint32() | 0;\n};\n\n/**\n * Reads a zig-zag encoded varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.sint32 = function read_sint32() {\n    var value = this.uint32();\n    return value >>> 1 ^ -(value & 1) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readLongVarint() {\n    // tends to deopt with local vars for octet etc.\n    var bits = new LongBits(0, 0);\n    var i = 0;\n    if (this.len - this.pos > 4) { // fast route (lo)\n        for (; i < 4; ++i) {\n            // 1st..4th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 5th\n        bits.lo = (bits.lo | (this.buf[this.pos] & 127) << 28) >>> 0;\n        bits.hi = (bits.hi | (this.buf[this.pos] & 127) >>  4) >>> 0;\n        if (this.buf[this.pos++] < 128)\n            return bits;\n        i = 0;\n    } else {\n        for (; i < 3; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 1st..3th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 4th\n        bits.lo = (bits.lo | (this.buf[this.pos++] & 127) << i * 7) >>> 0;\n        return bits;\n    }\n    if (this.len - this.pos > 4) { // fast route (hi)\n        for (; i < 5; ++i) {\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    } else {\n        for (; i < 5; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    }\n    /* istanbul ignore next */\n    throw Error(\"invalid varint encoding\");\n}\n\n/* eslint-enable no-invalid-this */\n\n/**\n * Reads a varint as a signed 64 bit value.\n * @name Reader#int64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as an unsigned 64 bit value.\n * @name Reader#uint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a zig-zag encoded varint as a signed 64 bit value.\n * @name Reader#sint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as a boolean.\n * @returns {boolean} Value read\n */\nReader.prototype.bool = function read_bool() {\n    return this.uint32() !== 0;\n};\n\nfunction readFixed32_end(buf, end) { // note that this uses `end`, not `pos`\n    return (buf[end - 4]\n          | buf[end - 3] << 8\n          | buf[end - 2] << 16\n          | buf[end - 1] << 24) >>> 0;\n}\n\n/**\n * Reads fixed 32 bits as an unsigned 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.fixed32 = function read_fixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4);\n};\n\n/**\n * Reads fixed 32 bits as a signed 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.sfixed32 = function read_sfixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readFixed64(/* this: Reader */) {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 8);\n\n    return new LongBits(readFixed32_end(this.buf, this.pos += 4), readFixed32_end(this.buf, this.pos += 4));\n}\n\n/* eslint-enable no-invalid-this */\n\n/**\n * Reads fixed 64 bits.\n * @name Reader#fixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads zig-zag encoded fixed 64 bits.\n * @name Reader#sfixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a float (32 bit) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.float = function read_float() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readFloatLE(this.buf, this.pos);\n    this.pos += 4;\n    return value;\n};\n\n/**\n * Reads a double (64 bit float) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.double = function read_double() {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readDoubleLE(this.buf, this.pos);\n    this.pos += 8;\n    return value;\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @returns {Uint8Array} Value read\n */\nReader.prototype.bytes = function read_bytes() {\n    var length = this.uint32(),\n        start  = this.pos,\n        end    = this.pos + length;\n\n    /* istanbul ignore if */\n    if (end > this.len)\n        throw indexOutOfRange(this, length);\n\n    this.pos += length;\n    if (Array.isArray(this.buf)) // plain array\n        return this.buf.slice(start, end);\n    return start === end // fix for IE 10/Win8 and others' subarray returning array of size 1\n        ? new this.buf.constructor(0)\n        : this._slice.call(this.buf, start, end);\n};\n\n/**\n * Reads a string preceeded by its byte length as a varint.\n * @returns {string} Value read\n */\nReader.prototype.string = function read_string() {\n    var bytes = this.bytes();\n    return utf8.read(bytes, 0, bytes.length);\n};\n\n/**\n * Skips the specified number of bytes if specified, otherwise skips a varint.\n * @param {number} [length] Length if known, otherwise a varint is assumed\n * @returns {Reader} `this`\n */\nReader.prototype.skip = function skip(length) {\n    if (typeof length === \"number\") {\n        /* istanbul ignore if */\n        if (this.pos + length > this.len)\n            throw indexOutOfRange(this, length);\n        this.pos += length;\n    } else {\n        do {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n        } while (this.buf[this.pos++] & 128);\n    }\n    return this;\n};\n\n/**\n * Skips the next element of the specified wire type.\n * @param {number} wireType Wire type received\n * @returns {Reader} `this`\n */\nReader.prototype.skipType = function(wireType) {\n    switch (wireType) {\n        case 0:\n            this.skip();\n            break;\n        case 1:\n            this.skip(8);\n            break;\n        case 2:\n            this.skip(this.uint32());\n            break;\n        case 3:\n            while ((wireType = this.uint32() & 7) !== 4) {\n                this.skipType(wireType);\n            }\n            break;\n        case 5:\n            this.skip(4);\n            break;\n\n        /* istanbul ignore next */\n        default:\n            throw Error(\"invalid wire type \" + wireType + \" at offset \" + this.pos);\n    }\n    return this;\n};\n\nReader._configure = function(BufferReader_) {\n    BufferReader = BufferReader_;\n    Reader.create = create();\n    BufferReader._configure();\n\n    var fn = util.Long ? \"toLong\" : /* istanbul ignore next */ \"toNumber\";\n    util.merge(Reader.prototype, {\n\n        int64: function read_int64() {\n            return readLongVarint.call(this)[fn](false);\n        },\n\n        uint64: function read_uint64() {\n            return readLongVarint.call(this)[fn](true);\n        },\n\n        sint64: function read_sint64() {\n            return readLongVarint.call(this).zzDecode()[fn](false);\n        },\n\n        fixed64: function read_fixed64() {\n            return readFixed64.call(this)[fn](true);\n        },\n\n        sfixed64: function read_sfixed64() {\n            return readFixed64.call(this)[fn](false);\n        }\n\n    });\n};\n", "\"use strict\";\nmodule.exports = <PERSON><PERSON>erReader;\n\n// extends Reader\nvar Reader = require(24);\n(BufferReader.prototype = Object.create(Reader.prototype)).constructor = BufferReader;\n\nvar util = require(35);\n\n/**\n * Constructs a new buffer reader instance.\n * @classdesc Wire format reader using node buffers.\n * @extends Reader\n * @constructor\n * @param {Buffer} buffer Buffer to read from\n */\nfunction BufferReader(buffer) {\n    Reader.call(this, buffer);\n\n    /**\n     * Read buffer.\n     * @name BufferReader#buf\n     * @type {Buffer}\n     */\n}\n\nBufferReader._configure = function () {\n    /* istanbul ignore else */\n    if (util.Buffer)\n        BufferReader.prototype._slice = util.Buffer.prototype.slice;\n};\n\n\n/**\n * @override\n */\nBufferReader.prototype.string = function read_string_buffer() {\n    var len = this.uint32(); // modifies pos\n    return this.buf.utf8Slice\n        ? this.buf.utf8Slice(this.pos, this.pos = Math.min(this.pos + len, this.len))\n        : this.buf.toString(\"utf-8\", this.pos, this.pos = Math.min(this.pos + len, this.len));\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @name BufferReader#bytes\n * @function\n * @returns {Buffer} Value read\n */\n\nBufferReader._configure();\n", "\"use strict\";\nmodule.exports = Root;\n\n// extends Namespace\nvar Namespace = require(21);\n((Root.prototype = Object.create(Namespace.prototype)).constructor = Root).className = \"Root\";\n\nvar Field   = require(15),\n    Enum    = require(14),\n    OneOf   = require(23),\n    util    = require(33);\n\nvar Type,   // cyclic\n    parse,  // might be excluded\n    common; // \"\n\n/**\n * Constructs a new root namespace instance.\n * @classdesc Root namespace wrapping all types, enums, services, sub-namespaces etc. that belong together.\n * @extends NamespaceBase\n * @constructor\n * @param {Object.<string,*>} [options] Top level options\n */\nfunction Root(options) {\n    Namespace.call(this, \"\", options);\n\n    /**\n     * Deferred extension fields.\n     * @type {Field[]}\n     */\n    this.deferred = [];\n\n    /**\n     * Resolved file names of loaded files.\n     * @type {string[]}\n     */\n    this.files = [];\n}\n\n/**\n * Loads a namespace descriptor into a root namespace.\n * @param {INamespace} json Nameespace descriptor\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted\n * @returns {Root} Root namespace\n */\nRoot.fromJSON = function fromJSON(json, root) {\n    if (!root)\n        root = new Root();\n    if (json.options)\n        root.setOptions(json.options);\n    return root.addJSON(json.nested);\n};\n\n/**\n * Resolves the path of an imported file, relative to the importing origin.\n * This method exists so you can override it with your own logic in case your imports are scattered over multiple directories.\n * @function\n * @param {string} origin The file name of the importing file\n * @param {string} target The file name being imported\n * @returns {string|null} Resolved path to `target` or `null` to skip the file\n */\nRoot.prototype.resolvePath = util.path.resolve;\n\n/**\n * Fetch content from file path or url\n * This method exists so you can override it with your own logic.\n * @function\n * @param {string} path File path or url\n * @param {FetchCallback} callback Callback function\n * @returns {undefined}\n */\nRoot.prototype.fetch = util.fetch;\n\n// A symbol-like function to safely signal synchronous loading\n/* istanbul ignore next */\nfunction SYNC() {} // eslint-disable-line no-empty-function\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} options Parse options\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n */\nRoot.prototype.load = function load(filename, options, callback) {\n    if (typeof options === \"function\") {\n        callback = options;\n        options = undefined;\n    }\n    var self = this;\n    if (!callback)\n        return util.asPromise(load, self, filename, options);\n\n    var sync = callback === SYNC; // undocumented\n\n    // Finishes loading by calling the callback (exactly once)\n    function finish(err, root) {\n        /* istanbul ignore if */\n        if (!callback)\n            return;\n        var cb = callback;\n        callback = null;\n        if (sync)\n            throw err;\n        cb(err, root);\n    }\n\n    // Bundled definition existence checking\n    function getBundledFileName(filename) {\n        var idx = filename.lastIndexOf(\"google/protobuf/\");\n        if (idx > -1) {\n            var altname = filename.substring(idx);\n            if (altname in common) return altname;\n        }\n        return null;\n    }\n\n    // Processes a single file\n    function process(filename, source) {\n        try {\n            if (util.isString(source) && source.charAt(0) === \"{\")\n                source = JSON.parse(source);\n            if (!util.isString(source))\n                self.setOptions(source.options).addJSON(source.nested);\n            else {\n                parse.filename = filename;\n                var parsed = parse(source, self, options),\n                    resolved,\n                    i = 0;\n                if (parsed.imports)\n                    for (; i < parsed.imports.length; ++i)\n                        if (resolved = getBundledFileName(parsed.imports[i]) || self.resolvePath(filename, parsed.imports[i]))\n                            fetch(resolved);\n                if (parsed.weakImports)\n                    for (i = 0; i < parsed.weakImports.length; ++i)\n                        if (resolved = getBundledFileName(parsed.weakImports[i]) || self.resolvePath(filename, parsed.weakImports[i]))\n                            fetch(resolved, true);\n            }\n        } catch (err) {\n            finish(err);\n        }\n        if (!sync && !queued)\n            finish(null, self); // only once anyway\n    }\n\n    // Fetches a single file\n    function fetch(filename, weak) {\n\n        // Skip if already loaded / attempted\n        if (self.files.indexOf(filename) > -1)\n            return;\n        self.files.push(filename);\n\n        // Shortcut bundled definitions\n        if (filename in common) {\n            if (sync)\n                process(filename, common[filename]);\n            else {\n                ++queued;\n                setTimeout(function() {\n                    --queued;\n                    process(filename, common[filename]);\n                });\n            }\n            return;\n        }\n\n        // Otherwise fetch from disk or network\n        if (sync) {\n            var source;\n            try {\n                source = util.fs.readFileSync(filename).toString(\"utf8\");\n            } catch (err) {\n                if (!weak)\n                    finish(err);\n                return;\n            }\n            process(filename, source);\n        } else {\n            ++queued;\n            self.fetch(filename, function(err, source) {\n                --queued;\n                /* istanbul ignore if */\n                if (!callback)\n                    return; // terminated meanwhile\n                if (err) {\n                    /* istanbul ignore else */\n                    if (!weak)\n                        finish(err);\n                    else if (!queued) // can't be covered reliably\n                        finish(null, self);\n                    return;\n                }\n                process(filename, source);\n            });\n        }\n    }\n    var queued = 0;\n\n    // Assembling the root namespace doesn't require working type\n    // references anymore, so we can load everything in parallel\n    if (util.isString(filename))\n        filename = [ filename ];\n    for (var i = 0, resolved; i < filename.length; ++i)\n        if (resolved = self.resolvePath(\"\", filename[i]))\n            fetch(resolved);\n\n    if (sync)\n        return self;\n    if (!queued)\n        finish(null, self);\n    return undefined;\n};\n// function load(filename:string, options:IParseOptions, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\n * @function Root#load\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @variation 2\n */\n// function load(filename:string, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and returns a promise.\n * @function Root#load\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {Promise<Root>} Promise\n * @variation 3\n */\n// function load(filename:string, [options:IParseOptions]):Promise<Root>\n\n/**\n * Synchronously loads one or multiple .proto or preprocessed .json files into this root namespace (node only).\n * @function Root#loadSync\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {Root} Root namespace\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\n */\nRoot.prototype.loadSync = function loadSync(filename, options) {\n    if (!util.isNode)\n        throw Error(\"not supported\");\n    return this.load(filename, options, SYNC);\n};\n\n/**\n * @override\n */\nRoot.prototype.resolveAll = function resolveAll() {\n    if (this.deferred.length)\n        throw Error(\"unresolvable extensions: \" + this.deferred.map(function(field) {\n            return \"'extend \" + field.extend + \"' in \" + field.parent.fullName;\n        }).join(\", \"));\n    return Namespace.prototype.resolveAll.call(this);\n};\n\n// only uppercased (and thus conflict-free) children are exposed, see below\nvar exposeRe = /^[A-Z]/;\n\n/**\n * Handles a deferred declaring extension field by creating a sister field to represent it within its extended type.\n * @param {Root} root Root instance\n * @param {Field} field Declaring extension field witin the declaring type\n * @returns {boolean} `true` if successfully added to the extended type, `false` otherwise\n * @inner\n * @ignore\n */\nfunction tryHandleExtension(root, field) {\n    var extendedType = field.parent.lookup(field.extend);\n    if (extendedType) {\n        var sisterField = new Field(field.fullName, field.id, field.type, field.rule, undefined, field.options);\n        sisterField.declaringField = field;\n        field.extensionField = sisterField;\n        extendedType.add(sisterField);\n        return true;\n    }\n    return false;\n}\n\n/**\n * Called when any object is added to this root or its sub-namespaces.\n * @param {ReflectionObject} object Object added\n * @returns {undefined}\n * @private\n */\nRoot.prototype._handleAdd = function _handleAdd(object) {\n    if (object instanceof Field) {\n\n        if (/* an extension field (implies not part of a oneof) */ object.extend !== undefined && /* not already handled */ !object.extensionField)\n            if (!tryHandleExtension(this, object))\n                this.deferred.push(object);\n\n    } else if (object instanceof Enum) {\n\n        if (exposeRe.test(object.name))\n            object.parent[object.name] = object.values; // expose enum values as property of its parent\n\n    } else if (!(object instanceof OneOf)) /* everything else is a namespace */ {\n\n        if (object instanceof Type) // Try to handle any deferred extensions\n            for (var i = 0; i < this.deferred.length;)\n                if (tryHandleExtension(this, this.deferred[i]))\n                    this.deferred.splice(i, 1);\n                else\n                    ++i;\n        for (var j = 0; j < /* initializes */ object.nestedArray.length; ++j) // recurse into the namespace\n            this._handleAdd(object._nestedArray[j]);\n        if (exposeRe.test(object.name))\n            object.parent[object.name] = object; // expose namespace as property of its parent\n    }\n\n    // The above also adds uppercased (and thus conflict-free) nested types, services and enums as\n    // properties of namespaces just like static code does. This allows using a .d.ts generated for\n    // a static module with reflection-based solutions where the condition is met.\n};\n\n/**\n * Called when any object is removed from this root or its sub-namespaces.\n * @param {ReflectionObject} object Object removed\n * @returns {undefined}\n * @private\n */\nRoot.prototype._handleRemove = function _handleRemove(object) {\n    if (object instanceof Field) {\n\n        if (/* an extension field */ object.extend !== undefined) {\n            if (/* already handled */ object.extensionField) { // remove its sister field\n                object.extensionField.parent.remove(object.extensionField);\n                object.extensionField = null;\n            } else { // cancel the extension\n                var index = this.deferred.indexOf(object);\n                /* istanbul ignore else */\n                if (index > -1)\n                    this.deferred.splice(index, 1);\n            }\n        }\n\n    } else if (object instanceof Enum) {\n\n        if (exposeRe.test(object.name))\n            delete object.parent[object.name]; // unexpose enum values\n\n    } else if (object instanceof Namespace) {\n\n        for (var i = 0; i < /* initializes */ object.nestedArray.length; ++i) // recurse into the namespace\n            this._handleRemove(object._nestedArray[i]);\n\n        if (exposeRe.test(object.name))\n            delete object.parent[object.name]; // unexpose namespaces\n\n    }\n};\n\n// Sets up cyclic dependencies (called in index-light)\nRoot._configure = function(Type_, parse_, common_) {\n    Type   = Type_;\n    parse  = parse_;\n    common = common_;\n};\n", "\"use strict\";\nmodule.exports = {};\n\n/**\n * Named roots.\n * This is where pbjs stores generated structures (the option `-r, --root` specifies a name).\n * Can also be used manually to make roots available accross modules.\n * @name roots\n * @type {Object.<string,Root>}\n * @example\n * // pbjs -r myroot -o compiled.js ...\n *\n * // in another module:\n * require(\"./compiled.js\");\n *\n * // in any subsequent module:\n * var root = protobuf.roots[\"myroot\"];\n */\n", "\"use strict\";\n\n/**\n * Streaming RPC helpers.\n * @namespace\n */\nvar rpc = exports;\n\n/**\n * RPC implementation passed to {@link Service#create} performing a service request on network level, i.e. by utilizing http requests or websockets.\n * @typedef RPCImpl\n * @type {function}\n * @param {Method|rpc.ServiceMethod<Message<{}>,Message<{}>>} method Reflected or static method being called\n * @param {Uint8Array} requestData Request data\n * @param {RPCImplCallback} callback Callback function\n * @returns {undefined}\n * @example\n * function rpcImpl(method, requestData, callback) {\n *     if (protobuf.util.lcFirst(method.name) !== \"myMethod\") // compatible with static code\n *         throw Error(\"no such method\");\n *     asynchronouslyObtainAResponse(requestData, function(err, responseData) {\n *         callback(err, responseData);\n *     });\n * }\n */\n\n/**\n * Node-style callback as used by {@link RPCImpl}.\n * @typedef RPCImplCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Uint8Array|null} [response] Response data or `null` to signal end of stream, if there hasn't been an error\n * @returns {undefined}\n */\n\nrpc.Service = require(29);\n", "\"use strict\";\nmodule.exports = Service;\n\nvar util = require(35);\n\n// Extends EventEmitter\n(Service.prototype = Object.create(util.EventEmitter.prototype)).constructor = Service;\n\n/**\n * A service method callback as used by {@link rpc.ServiceMethod|ServiceMethod}.\n *\n * Differs from {@link RPCImplCallback} in that it is an actual callback of a service method which may not return `response = null`.\n * @typedef rpc.ServiceMethodCallback\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {Error|null} error Error, if any\n * @param {TRes} [response] Response message\n * @returns {undefined}\n */\n\n/**\n * A service method part of a {@link rpc.Service} as created by {@link Service.create}.\n * @typedef rpc.ServiceMethod\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} [callback] Node-style callback called with the error, if any, and the response message\n * @returns {Promise<Message<TRes>>} Promise if `callback` has been omitted, otherwise `undefined`\n */\n\n/**\n * Constructs a new RPC service instance.\n * @classdesc An RPC service as returned by {@link Service#create}.\n * @exports rpc.Service\n * @extends util.EventEmitter\n * @constructor\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n */\nfunction Service(rpcImpl, requestDelimited, responseDelimited) {\n\n    if (typeof rpcImpl !== \"function\")\n        throw TypeError(\"rpcImpl must be a function\");\n\n    util.EventEmitter.call(this);\n\n    /**\n     * RPC implementation. Becomes `null` once the service is ended.\n     * @type {RPCImpl|null}\n     */\n    this.rpcImpl = rpcImpl;\n\n    /**\n     * Whether requests are length-delimited.\n     * @type {boolean}\n     */\n    this.requestDelimited = Boolean(requestDelimited);\n\n    /**\n     * Whether responses are length-delimited.\n     * @type {boolean}\n     */\n    this.responseDelimited = Boolean(responseDelimited);\n}\n\n/**\n * Calls a service method through {@link rpc.Service#rpcImpl|rpcImpl}.\n * @param {Method|rpc.ServiceMethod<TReq,TRes>} method Reflected or static method\n * @param {Constructor<TReq>} requestCtor Request constructor\n * @param {Constructor<TRes>} responseCtor Response constructor\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} callback Service callback\n * @returns {undefined}\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n */\nService.prototype.rpcCall = function rpcCall(method, requestCtor, responseCtor, request, callback) {\n\n    if (!request)\n        throw TypeError(\"request must be specified\");\n\n    var self = this;\n    if (!callback)\n        return util.asPromise(rpcCall, self, method, requestCtor, responseCtor, request);\n\n    if (!self.rpcImpl) {\n        setTimeout(function() { callback(Error(\"already ended\")); }, 0);\n        return undefined;\n    }\n\n    try {\n        return self.rpcImpl(\n            method,\n            requestCtor[self.requestDelimited ? \"encodeDelimited\" : \"encode\"](request).finish(),\n            function rpcCallback(err, response) {\n\n                if (err) {\n                    self.emit(\"error\", err, method);\n                    return callback(err);\n                }\n\n                if (response === null) {\n                    self.end(/* endedByRPC */ true);\n                    return undefined;\n                }\n\n                if (!(response instanceof responseCtor)) {\n                    try {\n                        response = responseCtor[self.responseDelimited ? \"decodeDelimited\" : \"decode\"](response);\n                    } catch (err) {\n                        self.emit(\"error\", err, method);\n                        return callback(err);\n                    }\n                }\n\n                self.emit(\"data\", response, method);\n                return callback(null, response);\n            }\n        );\n    } catch (err) {\n        self.emit(\"error\", err, method);\n        setTimeout(function() { callback(err); }, 0);\n        return undefined;\n    }\n};\n\n/**\n * Ends this service and emits the `end` event.\n * @param {boolean} [endedByRPC=false] Whether the service has been ended by the RPC implementation.\n * @returns {rpc.Service} `this`\n */\nService.prototype.end = function end(endedByRPC) {\n    if (this.rpcImpl) {\n        if (!endedByRPC) // signal end to rpcImpl\n            this.rpcImpl(null, null, null);\n        this.rpcImpl = null;\n        this.emit(\"end\").off();\n    }\n    return this;\n};\n", "\"use strict\";\nmodule.exports = Service;\n\n// extends Namespace\nvar Namespace = require(21);\n((Service.prototype = Object.create(Namespace.prototype)).constructor = Service).className = \"Service\";\n\nvar Method = require(20),\n    util   = require(33),\n    rpc    = require(28);\n\n/**\n * Constructs a new service instance.\n * @classdesc Reflected service.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Service name\n * @param {Object.<string,*>} [options] Service options\n * @throws {TypeError} If arguments are invalid\n */\nfunction Service(name, options) {\n    Namespace.call(this, name, options);\n\n    /**\n     * Service methods.\n     * @type {Object.<string,Method>}\n     */\n    this.methods = {}; // toJSON, marker\n\n    /**\n     * Cached methods as an array.\n     * @type {Method[]|null}\n     * @private\n     */\n    this._methodsArray = null;\n}\n\n/**\n * Service descriptor.\n * @interface IService\n * @extends INamespace\n * @property {Object.<string,IMethod>} methods Method descriptors\n */\n\n/**\n * Constructs a service from a service descriptor.\n * @param {string} name Service name\n * @param {IService} json Service descriptor\n * @returns {Service} Created service\n * @throws {TypeError} If arguments are invalid\n */\nService.fromJSON = function fromJSON(name, json) {\n    var service = new Service(name, json.options);\n    /* istanbul ignore else */\n    if (json.methods)\n        for (var names = Object.keys(json.methods), i = 0; i < names.length; ++i)\n            service.add(Method.fromJSON(names[i], json.methods[names[i]]));\n    if (json.nested)\n        service.addJSON(json.nested);\n    service.comment = json.comment;\n    return service;\n};\n\n/**\n * Converts this service to a service descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IService} Service descriptor\n */\nService.prototype.toJSON = function toJSON(toJSONOptions) {\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\" , inherited && inherited.options || undefined,\n        \"methods\" , Namespace.arrayToJSON(this.methodsArray, toJSONOptions) || /* istanbul ignore next */ {},\n        \"nested\"  , inherited && inherited.nested || undefined,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Methods of this service as an array for iteration.\n * @name Service#methodsArray\n * @type {Method[]}\n * @readonly\n */\nObject.defineProperty(Service.prototype, \"methodsArray\", {\n    get: function() {\n        return this._methodsArray || (this._methodsArray = util.toArray(this.methods));\n    }\n});\n\nfunction clearCache(service) {\n    service._methodsArray = null;\n    return service;\n}\n\n/**\n * @override\n */\nService.prototype.get = function get(name) {\n    return this.methods[name]\n        || Namespace.prototype.get.call(this, name);\n};\n\n/**\n * @override\n */\nService.prototype.resolveAll = function resolveAll() {\n    var methods = this.methodsArray;\n    for (var i = 0; i < methods.length; ++i)\n        methods[i].resolve();\n    return Namespace.prototype.resolve.call(this);\n};\n\n/**\n * @override\n */\nService.prototype.add = function add(object) {\n\n    /* istanbul ignore if */\n    if (this.get(object.name))\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n\n    if (object instanceof Method) {\n        this.methods[object.name] = object;\n        object.parent = this;\n        return clearCache(this);\n    }\n    return Namespace.prototype.add.call(this, object);\n};\n\n/**\n * @override\n */\nService.prototype.remove = function remove(object) {\n    if (object instanceof Method) {\n\n        /* istanbul ignore if */\n        if (this.methods[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.methods[object.name];\n        object.parent = null;\n        return clearCache(this);\n    }\n    return Namespace.prototype.remove.call(this, object);\n};\n\n/**\n * Creates a runtime service using the specified rpc implementation.\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n * @returns {rpc.Service} RPC service. Useful where requests and/or responses are streamed.\n */\nService.prototype.create = function create(rpcImpl, requestDelimited, responseDelimited) {\n    var rpcService = new rpc.Service(rpcImpl, requestDelimited, responseDelimited);\n    for (var i = 0, method; i < /* initializes */ this.methodsArray.length; ++i) {\n        var methodName = util.lcFirst((method = this._methodsArray[i]).resolve().name).replace(/[^$\\w_]/g, \"\");\n        rpcService[methodName] = util.codegen([\"r\",\"c\"], util.isReserved(methodName) ? methodName + \"_\" : methodName)(\"return this.rpcCall(m,q,s,r,c)\")({\n            m: method,\n            q: method.resolvedRequestType.ctor,\n            s: method.resolvedResponseType.ctor\n        });\n    }\n    return rpcService;\n};\n", "\"use strict\";\nmodule.exports = Type;\n\n// extends Namespace\nvar Namespace = require(21);\n((Type.prototype = Object.create(Namespace.prototype)).constructor = Type).className = \"Type\";\n\nvar Enum      = require(14),\n    OneOf     = require(23),\n    Field     = require(15),\n    MapField  = require(18),\n    Service   = require(30),\n    Message   = require(19),\n    Reader    = require(24),\n    Writer    = require(38),\n    util      = require(33),\n    encoder   = require(13),\n    decoder   = require(12),\n    verifier  = require(36),\n    converter = require(11),\n    wrappers  = require(37);\n\n/**\n * Constructs a new reflected message type instance.\n * @classdesc Reflected message type.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Message name\n * @param {Object.<string,*>} [options] Declared options\n */\nfunction Type(name, options) {\n    Namespace.call(this, name, options);\n\n    /**\n     * Message fields.\n     * @type {Object.<string,Field>}\n     */\n    this.fields = {};  // toJSON, marker\n\n    /**\n     * Oneofs declared within this namespace, if any.\n     * @type {Object.<string,OneOf>}\n     */\n    this.oneofs = undefined; // toJSON\n\n    /**\n     * Extension ranges, if any.\n     * @type {number[][]}\n     */\n    this.extensions = undefined; // toJSON\n\n    /**\n     * Reserved ranges, if any.\n     * @type {Array.<number[]|string>}\n     */\n    this.reserved = undefined; // toJSON\n\n    /*?\n     * Whether this type is a legacy group.\n     * @type {boolean|undefined}\n     */\n    this.group = undefined; // toJSON\n\n    /**\n     * Cached fields by id.\n     * @type {Object.<number,Field>|null}\n     * @private\n     */\n    this._fieldsById = null;\n\n    /**\n     * Cached fields as an array.\n     * @type {Field[]|null}\n     * @private\n     */\n    this._fieldsArray = null;\n\n    /**\n     * Cached oneofs as an array.\n     * @type {OneOf[]|null}\n     * @private\n     */\n    this._oneofsArray = null;\n\n    /**\n     * Cached constructor.\n     * @type {Constructor<{}>}\n     * @private\n     */\n    this._ctor = null;\n}\n\nObject.defineProperties(Type.prototype, {\n\n    /**\n     * Message fields by id.\n     * @name Type#fieldsById\n     * @type {Object.<number,Field>}\n     * @readonly\n     */\n    fieldsById: {\n        get: function() {\n\n            /* istanbul ignore if */\n            if (this._fieldsById)\n                return this._fieldsById;\n\n            this._fieldsById = {};\n            for (var names = Object.keys(this.fields), i = 0; i < names.length; ++i) {\n                var field = this.fields[names[i]],\n                    id = field.id;\n\n                /* istanbul ignore if */\n                if (this._fieldsById[id])\n                    throw Error(\"duplicate id \" + id + \" in \" + this);\n\n                this._fieldsById[id] = field;\n            }\n            return this._fieldsById;\n        }\n    },\n\n    /**\n     * Fields of this message as an array for iteration.\n     * @name Type#fieldsArray\n     * @type {Field[]}\n     * @readonly\n     */\n    fieldsArray: {\n        get: function() {\n            return this._fieldsArray || (this._fieldsArray = util.toArray(this.fields));\n        }\n    },\n\n    /**\n     * Oneofs of this message as an array for iteration.\n     * @name Type#oneofsArray\n     * @type {OneOf[]}\n     * @readonly\n     */\n    oneofsArray: {\n        get: function() {\n            return this._oneofsArray || (this._oneofsArray = util.toArray(this.oneofs));\n        }\n    },\n\n    /**\n     * The registered constructor, if any registered, otherwise a generic constructor.\n     * Assigning a function replaces the internal constructor. If the function does not extend {@link Message} yet, its prototype will be setup accordingly and static methods will be populated. If it already extends {@link Message}, it will just replace the internal constructor.\n     * @name Type#ctor\n     * @type {Constructor<{}>}\n     */\n    ctor: {\n        get: function() {\n            return this._ctor || (this.ctor = Type.generateConstructor(this)());\n        },\n        set: function(ctor) {\n\n            // Ensure proper prototype\n            var prototype = ctor.prototype;\n            if (!(prototype instanceof Message)) {\n                (ctor.prototype = new Message()).constructor = ctor;\n                util.merge(ctor.prototype, prototype);\n            }\n\n            // Classes and messages reference their reflected type\n            ctor.$type = ctor.prototype.$type = this;\n\n            // Mix in static methods\n            util.merge(ctor, Message, true);\n\n            this._ctor = ctor;\n\n            // Messages have non-enumerable default values on their prototype\n            var i = 0;\n            for (; i < /* initializes */ this.fieldsArray.length; ++i)\n                this._fieldsArray[i].resolve(); // ensures a proper value\n\n            // Messages have non-enumerable getters and setters for each virtual oneof field\n            var ctorProperties = {};\n            for (i = 0; i < /* initializes */ this.oneofsArray.length; ++i)\n                ctorProperties[this._oneofsArray[i].resolve().name] = {\n                    get: util.oneOfGetter(this._oneofsArray[i].oneof),\n                    set: util.oneOfSetter(this._oneofsArray[i].oneof)\n                };\n            if (i)\n                Object.defineProperties(ctor.prototype, ctorProperties);\n        }\n    }\n});\n\n/**\n * Generates a constructor function for the specified type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nType.generateConstructor = function generateConstructor(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n    var gen = util.codegen([\"p\"], mtype.name);\n    // explicitly initialize mutable object/array fields so that these aren't just inherited from the prototype\n    for (var i = 0, field; i < mtype.fieldsArray.length; ++i)\n        if ((field = mtype._fieldsArray[i]).map) gen\n            (\"this%s={}\", util.safeProp(field.name));\n        else if (field.repeated) gen\n            (\"this%s=[]\", util.safeProp(field.name));\n    return gen\n    (\"if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)\") // omit undefined or null\n        (\"this[ks[i]]=p[ks[i]]\");\n    /* eslint-enable no-unexpected-multiline */\n};\n\nfunction clearCache(type) {\n    type._fieldsById = type._fieldsArray = type._oneofsArray = null;\n    delete type.encode;\n    delete type.decode;\n    delete type.verify;\n    return type;\n}\n\n/**\n * Message type descriptor.\n * @interface IType\n * @extends INamespace\n * @property {Object.<string,IOneOf>} [oneofs] Oneof descriptors\n * @property {Object.<string,IField>} fields Field descriptors\n * @property {number[][]} [extensions] Extension ranges\n * @property {number[][]} [reserved] Reserved ranges\n * @property {boolean} [group=false] Whether a legacy group or not\n */\n\n/**\n * Creates a message type from a message type descriptor.\n * @param {string} name Message name\n * @param {IType} json Message type descriptor\n * @returns {Type} Created message type\n */\nType.fromJSON = function fromJSON(name, json) {\n    var type = new Type(name, json.options);\n    type.extensions = json.extensions;\n    type.reserved = json.reserved;\n    var names = Object.keys(json.fields),\n        i = 0;\n    for (; i < names.length; ++i)\n        type.add(\n            ( typeof json.fields[names[i]].keyType !== \"undefined\"\n            ? MapField.fromJSON\n            : Field.fromJSON )(names[i], json.fields[names[i]])\n        );\n    if (json.oneofs)\n        for (names = Object.keys(json.oneofs), i = 0; i < names.length; ++i)\n            type.add(OneOf.fromJSON(names[i], json.oneofs[names[i]]));\n    if (json.nested)\n        for (names = Object.keys(json.nested), i = 0; i < names.length; ++i) {\n            var nested = json.nested[names[i]];\n            type.add( // most to least likely\n                ( nested.id !== undefined\n                ? Field.fromJSON\n                : nested.fields !== undefined\n                ? Type.fromJSON\n                : nested.values !== undefined\n                ? Enum.fromJSON\n                : nested.methods !== undefined\n                ? Service.fromJSON\n                : Namespace.fromJSON )(names[i], nested)\n            );\n        }\n    if (json.extensions && json.extensions.length)\n        type.extensions = json.extensions;\n    if (json.reserved && json.reserved.length)\n        type.reserved = json.reserved;\n    if (json.group)\n        type.group = true;\n    if (json.comment)\n        type.comment = json.comment;\n    return type;\n};\n\n/**\n * Converts this message type to a message type descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IType} Message type descriptor\n */\nType.prototype.toJSON = function toJSON(toJSONOptions) {\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\"    , inherited && inherited.options || undefined,\n        \"oneofs\"     , Namespace.arrayToJSON(this.oneofsArray, toJSONOptions),\n        \"fields\"     , Namespace.arrayToJSON(this.fieldsArray.filter(function(obj) { return !obj.declaringField; }), toJSONOptions) || {},\n        \"extensions\" , this.extensions && this.extensions.length ? this.extensions : undefined,\n        \"reserved\"   , this.reserved && this.reserved.length ? this.reserved : undefined,\n        \"group\"      , this.group || undefined,\n        \"nested\"     , inherited && inherited.nested || undefined,\n        \"comment\"    , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nType.prototype.resolveAll = function resolveAll() {\n    var fields = this.fieldsArray, i = 0;\n    while (i < fields.length)\n        fields[i++].resolve();\n    var oneofs = this.oneofsArray; i = 0;\n    while (i < oneofs.length)\n        oneofs[i++].resolve();\n    return Namespace.prototype.resolveAll.call(this);\n};\n\n/**\n * @override\n */\nType.prototype.get = function get(name) {\n    return this.fields[name]\n        || this.oneofs && this.oneofs[name]\n        || this.nested && this.nested[name]\n        || null;\n};\n\n/**\n * Adds a nested object to this type.\n * @param {ReflectionObject} object Nested object to add\n * @returns {Type} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a nested object with this name or, if a field, when there is already a field with this id\n */\nType.prototype.add = function add(object) {\n\n    if (this.get(object.name))\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n\n    if (object instanceof Field && object.extend === undefined) {\n        // NOTE: Extension fields aren't actual fields on the declaring type, but nested objects.\n        // The root object takes care of adding distinct sister-fields to the respective extended\n        // type instead.\n\n        // avoids calling the getter if not absolutely necessary because it's called quite frequently\n        if (this._fieldsById ? /* istanbul ignore next */ this._fieldsById[object.id] : this.fieldsById[object.id])\n            throw Error(\"duplicate id \" + object.id + \" in \" + this);\n        if (this.isReservedId(object.id))\n            throw Error(\"id \" + object.id + \" is reserved in \" + this);\n        if (this.isReservedName(object.name))\n            throw Error(\"name '\" + object.name + \"' is reserved in \" + this);\n\n        if (object.parent)\n            object.parent.remove(object);\n        this.fields[object.name] = object;\n        object.message = this;\n        object.onAdd(this);\n        return clearCache(this);\n    }\n    if (object instanceof OneOf) {\n        if (!this.oneofs)\n            this.oneofs = {};\n        this.oneofs[object.name] = object;\n        object.onAdd(this);\n        return clearCache(this);\n    }\n    return Namespace.prototype.add.call(this, object);\n};\n\n/**\n * Removes a nested object from this type.\n * @param {ReflectionObject} object Nested object to remove\n * @returns {Type} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `object` is not a member of this type\n */\nType.prototype.remove = function remove(object) {\n    if (object instanceof Field && object.extend === undefined) {\n        // See Type#add for the reason why extension fields are excluded here.\n\n        /* istanbul ignore if */\n        if (!this.fields || this.fields[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.fields[object.name];\n        object.parent = null;\n        object.onRemove(this);\n        return clearCache(this);\n    }\n    if (object instanceof OneOf) {\n\n        /* istanbul ignore if */\n        if (!this.oneofs || this.oneofs[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.oneofs[object.name];\n        object.parent = null;\n        object.onRemove(this);\n        return clearCache(this);\n    }\n    return Namespace.prototype.remove.call(this, object);\n};\n\n/**\n * Tests if the specified id is reserved.\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nType.prototype.isReservedId = function isReservedId(id) {\n    return Namespace.isReservedId(this.reserved, id);\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nType.prototype.isReservedName = function isReservedName(name) {\n    return Namespace.isReservedName(this.reserved, name);\n};\n\n/**\n * Creates a new message of this type using the specified properties.\n * @param {Object.<string,*>} [properties] Properties to set\n * @returns {Message<{}>} Message instance\n */\nType.prototype.create = function create(properties) {\n    return new this.ctor(properties);\n};\n\n/**\n * Sets up {@link Type#encode|encode}, {@link Type#decode|decode} and {@link Type#verify|verify}.\n * @returns {Type} `this`\n */\nType.prototype.setup = function setup() {\n    // Sets up everything at once so that the prototype chain does not have to be re-evaluated\n    // multiple times (V8, soft-deopt prototype-check).\n\n    var fullName = this.fullName,\n        types    = [];\n    for (var i = 0; i < /* initializes */ this.fieldsArray.length; ++i)\n        types.push(this._fieldsArray[i].resolve().resolvedType);\n\n    // Replace setup methods with type-specific generated functions\n    this.encode = encoder(this)({\n        Writer : Writer,\n        types  : types,\n        util   : util\n    });\n    this.decode = decoder(this)({\n        Reader : Reader,\n        types  : types,\n        util   : util\n    });\n    this.verify = verifier(this)({\n        types : types,\n        util  : util\n    });\n    this.fromObject = converter.fromObject(this)({\n        types : types,\n        util  : util\n    });\n    this.toObject = converter.toObject(this)({\n        types : types,\n        util  : util\n    });\n\n    // Inject custom wrappers for common types\n    var wrapper = wrappers[fullName];\n    if (wrapper) {\n        var originalThis = Object.create(this);\n        // if (wrapper.fromObject) {\n            originalThis.fromObject = this.fromObject;\n            this.fromObject = wrapper.fromObject.bind(originalThis);\n        // }\n        // if (wrapper.toObject) {\n            originalThis.toObject = this.toObject;\n            this.toObject = wrapper.toObject.bind(originalThis);\n        // }\n    }\n\n    return this;\n};\n\n/**\n * Encodes a message of this type. Does not implicitly {@link Type#verify|verify} messages.\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\n * @param {Writer} [writer] Writer to encode to\n * @returns {Writer} writer\n */\nType.prototype.encode = function encode_setup(message, writer) {\n    return this.setup().encode(message, writer); // overrides this method\n};\n\n/**\n * Encodes a message of this type preceeded by its byte length as a varint. Does not implicitly {@link Type#verify|verify} messages.\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\n * @param {Writer} [writer] Writer to encode to\n * @returns {Writer} writer\n */\nType.prototype.encodeDelimited = function encodeDelimited(message, writer) {\n    return this.encode(message, writer && writer.len ? writer.fork() : writer).ldelim();\n};\n\n/**\n * Decodes a message of this type.\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\n * @param {number} [length] Length of the message, if known beforehand\n * @returns {Message<{}>} Decoded message\n * @throws {Error} If the payload is not a reader or valid buffer\n * @throws {util.ProtocolError<{}>} If required fields are missing\n */\nType.prototype.decode = function decode_setup(reader, length) {\n    return this.setup().decode(reader, length); // overrides this method\n};\n\n/**\n * Decodes a message of this type preceeded by its byte length as a varint.\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\n * @returns {Message<{}>} Decoded message\n * @throws {Error} If the payload is not a reader or valid buffer\n * @throws {util.ProtocolError} If required fields are missing\n */\nType.prototype.decodeDelimited = function decodeDelimited(reader) {\n    if (!(reader instanceof Reader))\n        reader = Reader.create(reader);\n    return this.decode(reader, reader.uint32());\n};\n\n/**\n * Verifies that field values are valid and that required fields are present.\n * @param {Object.<string,*>} message Plain object to verify\n * @returns {null|string} `null` if valid, otherwise the reason why it is not\n */\nType.prototype.verify = function verify_setup(message) {\n    return this.setup().verify(message); // overrides this method\n};\n\n/**\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\n * @param {Object.<string,*>} object Plain object to convert\n * @returns {Message<{}>} Message instance\n */\nType.prototype.fromObject = function fromObject(object) {\n    return this.setup().fromObject(object);\n};\n\n/**\n * Conversion options as used by {@link Type#toObject} and {@link Message.toObject}.\n * @interface IConversionOptions\n * @property {Function} [longs] Long conversion type.\n * Valid values are `String` and `Number` (the global types).\n * Defaults to copy the present value, which is a possibly unsafe number without and a {@link Long} with a long library.\n * @property {Function} [enums] Enum value conversion type.\n * Only valid value is `String` (the global type).\n * Defaults to copy the present value, which is the numeric id.\n * @property {Function} [bytes] Bytes value conversion type.\n * Valid values are `Array` and (a base64 encoded) `String` (the global types).\n * Defaults to copy the present value, which usually is a Buffer under node and an Uint8Array in the browser.\n * @property {boolean} [defaults=false] Also sets default values on the resulting object\n * @property {boolean} [arrays=false] Sets empty arrays for missing repeated fields even if `defaults=false`\n * @property {boolean} [objects=false] Sets empty objects for missing map fields even if `defaults=false`\n * @property {boolean} [oneofs=false] Includes virtual oneof properties set to the present field's name, if any\n * @property {boolean} [json=false] Performs additional JSON compatibility conversions, i.e. NaN and Infinity to strings\n */\n\n/**\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\n * @param {Message<{}>} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n */\nType.prototype.toObject = function toObject(message, options) {\n    return this.setup().toObject(message, options);\n};\n\n/**\n * Decorator function as returned by {@link Type.d} (TypeScript).\n * @typedef TypeDecorator\n * @type {function}\n * @param {Constructor<T>} target Target constructor\n * @returns {undefined}\n * @template T extends Message<T>\n */\n\n/**\n * Type decorator (TypeScript).\n * @param {string} [typeName] Type name, defaults to the constructor's name\n * @returns {TypeDecorator<T>} Decorator function\n * @template T extends Message<T>\n */\nType.d = function decorateType(typeName) {\n    return function typeDecorator(target) {\n        util.decorateType(target, typeName);\n    };\n};\n", "\"use strict\";\n\n/**\n * Common type constants.\n * @namespace\n */\nvar types = exports;\n\nvar util = require(33);\n\nvar s = [\n    \"double\",   // 0\n    \"float\",    // 1\n    \"int32\",    // 2\n    \"uint32\",   // 3\n    \"sint32\",   // 4\n    \"fixed32\",  // 5\n    \"sfixed32\", // 6\n    \"int64\",    // 7\n    \"uint64\",   // 8\n    \"sint64\",   // 9\n    \"fixed64\",  // 10\n    \"sfixed64\", // 11\n    \"bool\",     // 12\n    \"string\",   // 13\n    \"bytes\"     // 14\n];\n\nfunction bake(values, offset) {\n    var i = 0, o = {};\n    offset |= 0;\n    while (i < values.length) o[s[i + offset]] = values[i++];\n    return o;\n}\n\n/**\n * Basic type wire types.\n * @type {Object.<string,number>}\n * @const\n * @property {number} double=1 Fixed64 wire type\n * @property {number} float=5 Fixed32 wire type\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n * @property {number} string=2 Ldelim wire type\n * @property {number} bytes=2 Ldelim wire type\n */\ntypes.basic = bake([\n    /* double   */ 1,\n    /* float    */ 5,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0,\n    /* string   */ 2,\n    /* bytes    */ 2\n]);\n\n/**\n * Basic type defaults.\n * @type {Object.<string,*>}\n * @const\n * @property {number} double=0 Double default\n * @property {number} float=0 Float default\n * @property {number} int32=0 Int32 default\n * @property {number} uint32=0 Uint32 default\n * @property {number} sint32=0 Sint32 default\n * @property {number} fixed32=0 Fixed32 default\n * @property {number} sfixed32=0 Sfixed32 default\n * @property {number} int64=0 Int64 default\n * @property {number} uint64=0 Uint64 default\n * @property {number} sint64=0 Sint32 default\n * @property {number} fixed64=0 Fixed64 default\n * @property {number} sfixed64=0 Sfixed64 default\n * @property {boolean} bool=false Bool default\n * @property {string} string=\"\" String default\n * @property {Array.<number>} bytes=Array(0) Bytes default\n * @property {null} message=null Message default\n */\ntypes.defaults = bake([\n    /* double   */ 0,\n    /* float    */ 0,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 0,\n    /* sfixed32 */ 0,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 0,\n    /* sfixed64 */ 0,\n    /* bool     */ false,\n    /* string   */ \"\",\n    /* bytes    */ util.emptyArray,\n    /* message  */ null\n]);\n\n/**\n * Basic long type wire types.\n * @type {Object.<string,number>}\n * @const\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n */\ntypes.long = bake([\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1\n], 7);\n\n/**\n * Allowed types for map keys with their associated wire type.\n * @type {Object.<string,number>}\n * @const\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n * @property {number} string=2 Ldelim wire type\n */\ntypes.mapKey = bake([\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0,\n    /* string   */ 2\n], 2);\n\n/**\n * Allowed types for packed repeated fields with their associated wire type.\n * @type {Object.<string,number>}\n * @const\n * @property {number} double=1 Fixed64 wire type\n * @property {number} float=5 Fixed32 wire type\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n */\ntypes.packed = bake([\n    /* double   */ 1,\n    /* float    */ 5,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0\n]);\n", "\"use strict\";\n\n/**\n * Various utility functions.\n * @namespace\n */\nvar util = module.exports = require(35);\n\nvar roots = require(27);\n\nvar Type, // cyclic\n    Enum;\n\nutil.codegen = require(3);\nutil.fetch   = require(5);\nutil.path    = require(8);\n\n/**\n * Node's fs module if available.\n * @type {Object.<string,*>}\n */\nutil.fs = util.inquire(\"fs\");\n\n/**\n * Converts an object's values to an array.\n * @param {Object.<string,*>} object Object to convert\n * @returns {Array.<*>} Converted array\n */\nutil.toArray = function toArray(object) {\n    if (object) {\n        var keys  = Object.keys(object),\n            array = new Array(keys.length),\n            index = 0;\n        while (index < keys.length)\n            array[index] = object[keys[index++]];\n        return array;\n    }\n    return [];\n};\n\n/**\n * Converts an array of keys immediately followed by their respective value to an object, omitting undefined values.\n * @param {Array.<*>} array Array to convert\n * @returns {Object.<string,*>} Converted object\n */\nutil.toObject = function toObject(array) {\n    var object = {},\n        index  = 0;\n    while (index < array.length) {\n        var key = array[index++],\n            val = array[index++];\n        if (val !== undefined)\n            object[key] = val;\n    }\n    return object;\n};\n\nvar safePropBackslashRe = /\\\\/g,\n    safePropQuoteRe     = /\"/g;\n\n/**\n * Tests whether the specified name is a reserved word in JS.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nutil.isReserved = function isReserved(name) {\n    return /^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(name);\n};\n\n/**\n * Returns a safe property accessor for the specified property name.\n * @param {string} prop Property name\n * @returns {string} Safe accessor\n */\nutil.safeProp = function safeProp(prop) {\n    if (!/^[$\\w_]+$/.test(prop) || util.isReserved(prop))\n        return \"[\\\"\" + prop.replace(safePropBackslashRe, \"\\\\\\\\\").replace(safePropQuoteRe, \"\\\\\\\"\") + \"\\\"]\";\n    return \".\" + prop;\n};\n\n/**\n * Converts the first character of a string to upper case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.ucFirst = function ucFirst(str) {\n    return str.charAt(0).toUpperCase() + str.substring(1);\n};\n\nvar camelCaseRe = /_([a-z])/g;\n\n/**\n * Converts a string to camel case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.camelCase = function camelCase(str) {\n    return str.substring(0, 1)\n         + str.substring(1)\n               .replace(camelCaseRe, function($0, $1) { return $1.toUpperCase(); });\n};\n\n/**\n * Compares reflected fields by id.\n * @param {Field} a First field\n * @param {Field} b Second field\n * @returns {number} Comparison value\n */\nutil.compareFieldsById = function compareFieldsById(a, b) {\n    return a.id - b.id;\n};\n\n/**\n * Decorator helper for types (TypeScript).\n * @param {Constructor<T>} ctor Constructor function\n * @param {string} [typeName] Type name, defaults to the constructor's name\n * @returns {Type} Reflected type\n * @template T extends Message<T>\n * @property {Root} root Decorators root\n */\nutil.decorateType = function decorateType(ctor, typeName) {\n\n    /* istanbul ignore if */\n    if (ctor.$type) {\n        if (typeName && ctor.$type.name !== typeName) {\n            util.decorateRoot.remove(ctor.$type);\n            ctor.$type.name = typeName;\n            util.decorateRoot.add(ctor.$type);\n        }\n        return ctor.$type;\n    }\n\n    /* istanbul ignore next */\n    if (!Type)\n        Type = require(31);\n\n    var type = new Type(typeName || ctor.name);\n    util.decorateRoot.add(type);\n    type.ctor = ctor; // sets up .encode, .decode etc.\n    Object.defineProperty(ctor, \"$type\", { value: type, enumerable: false });\n    Object.defineProperty(ctor.prototype, \"$type\", { value: type, enumerable: false });\n    return type;\n};\n\nvar decorateEnumIndex = 0;\n\n/**\n * Decorator helper for enums (TypeScript).\n * @param {Object} object Enum object\n * @returns {Enum} Reflected enum\n */\nutil.decorateEnum = function decorateEnum(object) {\n\n    /* istanbul ignore if */\n    if (object.$type)\n        return object.$type;\n\n    /* istanbul ignore next */\n    if (!Enum)\n        Enum = require(14);\n\n    var enm = new Enum(\"Enum\" + decorateEnumIndex++, object);\n    util.decorateRoot.add(enm);\n    Object.defineProperty(object, \"$type\", { value: enm, enumerable: false });\n    return enm;\n};\n\n\n/**\n * Sets the value of a property by property path. If a value already exists, it is turned to an array\n * @param {Object.<string,*>} dst Destination object\n * @param {string} path dot '.' delimited path of the property to set\n * @param {Object} value the value to set\n * @returns {Object.<string,*>} Destination object\n */\nutil.setProperty = function setProperty(dst, path, value) {\n    function setProp(dst, path, value) {\n        var part = path.shift();\n        if (part === \"__proto__\" || part === \"prototype\") {\n          return dst;\n        }\n        if (path.length > 0) {\n            dst[part] = setProp(dst[part] || {}, path, value);\n        } else {\n            var prevValue = dst[part];\n            if (prevValue)\n                value = [].concat(prevValue).concat(value);\n            dst[part] = value;\n        }\n        return dst;\n    }\n\n    if (typeof dst !== \"object\")\n        throw TypeError(\"dst must be an object\");\n    if (!path)\n        throw TypeError(\"path must be specified\");\n\n    path = path.split(\".\");\n    return setProp(dst, path, value);\n};\n\n/**\n * Decorator root (TypeScript).\n * @name util.decorateRoot\n * @type {Root}\n * @readonly\n */\nObject.defineProperty(util, \"decorateRoot\", {\n    get: function() {\n        return roots[\"decorated\"] || (roots[\"decorated\"] = new (require(26))());\n    }\n});\n", "\"use strict\";\nmodule.exports = LongBits;\n\nvar util = require(35);\n\n/**\n * Constructs new long bits.\n * @classdesc Helper class for working with the low and high bits of a 64 bit value.\n * @memberof util\n * @constructor\n * @param {number} lo Low 32 bits, unsigned\n * @param {number} hi High 32 bits, unsigned\n */\nfunction LongBits(lo, hi) {\n\n    // note that the casts below are theoretically unnecessary as of today, but older statically\n    // generated converter code might still call the ctor with signed 32bits. kept for compat.\n\n    /**\n     * Low bits.\n     * @type {number}\n     */\n    this.lo = lo >>> 0;\n\n    /**\n     * High bits.\n     * @type {number}\n     */\n    this.hi = hi >>> 0;\n}\n\n/**\n * Zero bits.\n * @memberof util.LongBits\n * @type {util.LongBits}\n */\nvar zero = LongBits.zero = new LongBits(0, 0);\n\nzero.toNumber = function() { return 0; };\nzero.zzEncode = zero.zzDecode = function() { return this; };\nzero.length = function() { return 1; };\n\n/**\n * Zero hash.\n * @memberof util.LongBits\n * @type {string}\n */\nvar zeroHash = LongBits.zeroHash = \"\\0\\0\\0\\0\\0\\0\\0\\0\";\n\n/**\n * Constructs new long bits from the specified number.\n * @param {number} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.fromNumber = function fromNumber(value) {\n    if (value === 0)\n        return zero;\n    var sign = value < 0;\n    if (sign)\n        value = -value;\n    var lo = value >>> 0,\n        hi = (value - lo) / 4294967296 >>> 0;\n    if (sign) {\n        hi = ~hi >>> 0;\n        lo = ~lo >>> 0;\n        if (++lo > 4294967295) {\n            lo = 0;\n            if (++hi > 4294967295)\n                hi = 0;\n        }\n    }\n    return new LongBits(lo, hi);\n};\n\n/**\n * Constructs new long bits from a number, long or string.\n * @param {Long|number|string} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.from = function from(value) {\n    if (typeof value === \"number\")\n        return LongBits.fromNumber(value);\n    if (util.isString(value)) {\n        /* istanbul ignore else */\n        if (util.Long)\n            value = util.Long.fromString(value);\n        else\n            return LongBits.fromNumber(parseInt(value, 10));\n    }\n    return value.low || value.high ? new LongBits(value.low >>> 0, value.high >>> 0) : zero;\n};\n\n/**\n * Converts this long bits to a possibly unsafe JavaScript number.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {number} Possibly unsafe number\n */\nLongBits.prototype.toNumber = function toNumber(unsigned) {\n    if (!unsigned && this.hi >>> 31) {\n        var lo = ~this.lo + 1 >>> 0,\n            hi = ~this.hi     >>> 0;\n        if (!lo)\n            hi = hi + 1 >>> 0;\n        return -(lo + hi * 4294967296);\n    }\n    return this.lo + this.hi * 4294967296;\n};\n\n/**\n * Converts this long bits to a long.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long} Long\n */\nLongBits.prototype.toLong = function toLong(unsigned) {\n    return util.Long\n        ? new util.Long(this.lo | 0, this.hi | 0, Boolean(unsigned))\n        /* istanbul ignore next */\n        : { low: this.lo | 0, high: this.hi | 0, unsigned: Boolean(unsigned) };\n};\n\nvar charCodeAt = String.prototype.charCodeAt;\n\n/**\n * Constructs new long bits from the specified 8 characters long hash.\n * @param {string} hash Hash\n * @returns {util.LongBits} Bits\n */\nLongBits.fromHash = function fromHash(hash) {\n    if (hash === zeroHash)\n        return zero;\n    return new LongBits(\n        ( charCodeAt.call(hash, 0)\n        | charCodeAt.call(hash, 1) << 8\n        | charCodeAt.call(hash, 2) << 16\n        | charCodeAt.call(hash, 3) << 24) >>> 0\n    ,\n        ( charCodeAt.call(hash, 4)\n        | charCodeAt.call(hash, 5) << 8\n        | charCodeAt.call(hash, 6) << 16\n        | charCodeAt.call(hash, 7) << 24) >>> 0\n    );\n};\n\n/**\n * Converts this long bits to a 8 characters long hash.\n * @returns {string} Hash\n */\nLongBits.prototype.toHash = function toHash() {\n    return String.fromCharCode(\n        this.lo        & 255,\n        this.lo >>> 8  & 255,\n        this.lo >>> 16 & 255,\n        this.lo >>> 24      ,\n        this.hi        & 255,\n        this.hi >>> 8  & 255,\n        this.hi >>> 16 & 255,\n        this.hi >>> 24\n    );\n};\n\n/**\n * Zig-zag encodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzEncode = function zzEncode() {\n    var mask =   this.hi >> 31;\n    this.hi  = ((this.hi << 1 | this.lo >>> 31) ^ mask) >>> 0;\n    this.lo  = ( this.lo << 1                   ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Zig-zag decodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzDecode = function zzDecode() {\n    var mask = -(this.lo & 1);\n    this.lo  = ((this.lo >>> 1 | this.hi << 31) ^ mask) >>> 0;\n    this.hi  = ( this.hi >>> 1                  ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Calculates the length of this longbits when encoded as a varint.\n * @returns {number} Length\n */\nLongBits.prototype.length = function length() {\n    var part0 =  this.lo,\n        part1 = (this.lo >>> 28 | this.hi << 4) >>> 0,\n        part2 =  this.hi >>> 24;\n    return part2 === 0\n         ? part1 === 0\n           ? part0 < 16384\n             ? part0 < 128 ? 1 : 2\n             : part0 < 2097152 ? 3 : 4\n           : part1 < 16384\n             ? part1 < 128 ? 5 : 6\n             : part1 < 2097152 ? 7 : 8\n         : part2 < 128 ? 9 : 10;\n};\n", "\"use strict\";\nvar util = exports;\n\n// used to return a Promise where callback is omitted\nutil.asPromise = require(1);\n\n// converts to / from base64 encoded strings\nutil.base64 = require(2);\n\n// base class of rpc.Service\nutil.EventEmitter = require(4);\n\n// float handling accross browsers\nutil.float = require(6);\n\n// requires modules optionally and hides the call from bundlers\nutil.inquire = require(7);\n\n// converts to / from utf8 encoded strings\nutil.utf8 = require(10);\n\n// provides a node-like buffer pool in the browser\nutil.pool = require(9);\n\n// utility to work with the low and high bits of a 64 bit value\nutil.LongBits = require(34);\n\n/**\n * Whether running within node or not.\n * @memberof util\n * @type {boolean}\n */\nutil.isNode = Boolean(typeof global !== \"undefined\"\n                   && global\n                   && global.process\n                   && global.process.versions\n                   && global.process.versions.node);\n\n/**\n * Global object reference.\n * @memberof util\n * @type {Object}\n */\nutil.global = util.isNode && global\n           || typeof window !== \"undefined\" && window\n           || typeof self   !== \"undefined\" && self\n           || this; // eslint-disable-line no-invalid-this\n\n/**\n * An immuable empty array.\n * @memberof util\n * @type {Array.<*>}\n * @const\n */\nutil.emptyArray = Object.freeze ? Object.freeze([]) : /* istanbul ignore next */ []; // used on prototypes\n\n/**\n * An immutable empty object.\n * @type {Object}\n * @const\n */\nutil.emptyObject = Object.freeze ? Object.freeze({}) : /* istanbul ignore next */ {}; // used on prototypes\n\n/**\n * Tests if the specified value is an integer.\n * @function\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is an integer\n */\nutil.isInteger = Number.isInteger || /* istanbul ignore next */ function isInteger(value) {\n    return typeof value === \"number\" && isFinite(value) && Math.floor(value) === value;\n};\n\n/**\n * Tests if the specified value is a string.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a string\n */\nutil.isString = function isString(value) {\n    return typeof value === \"string\" || value instanceof String;\n};\n\n/**\n * Tests if the specified value is a non-null object.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a non-null object\n */\nutil.isObject = function isObject(value) {\n    return value && typeof value === \"object\";\n};\n\n/**\n * Checks if a property on a message is considered to be present.\n * This is an alias of {@link util.isSet}.\n * @function\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isset =\n\n/**\n * Checks if a property on a message is considered to be present.\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isSet = function isSet(obj, prop) {\n    var value = obj[prop];\n    if (value != null && obj.hasOwnProperty(prop)) // eslint-disable-line eqeqeq, no-prototype-builtins\n        return typeof value !== \"object\" || (Array.isArray(value) ? value.length : Object.keys(value).length) > 0;\n    return false;\n};\n\n/**\n * Any compatible Buffer instance.\n * This is a minimal stand-alone definition of a Buffer instance. The actual type is that exported by node's typings.\n * @interface Buffer\n * @extends Uint8Array\n */\n\n/**\n * Node's Buffer class if available.\n * @type {Constructor<Buffer>}\n */\nutil.Buffer = (function() {\n    try {\n        var Buffer = util.inquire(\"buffer\").Buffer;\n        // refuse to use non-node buffers if not explicitly assigned (perf reasons):\n        return Buffer.prototype.utf8Write ? Buffer : /* istanbul ignore next */ null;\n    } catch (e) {\n        /* istanbul ignore next */\n        return null;\n    }\n})();\n\n// Internal alias of or polyfull for Buffer.from.\nutil._Buffer_from = null;\n\n// Internal alias of or polyfill for Buffer.allocUnsafe.\nutil._Buffer_allocUnsafe = null;\n\n/**\n * Creates a new buffer of whatever type supported by the environment.\n * @param {number|number[]} [sizeOrArray=0] Buffer size or number array\n * @returns {Uint8Array|Buffer} Buffer\n */\nutil.newBuffer = function newBuffer(sizeOrArray) {\n    /* istanbul ignore next */\n    return typeof sizeOrArray === \"number\"\n        ? util.Buffer\n            ? util._Buffer_allocUnsafe(sizeOrArray)\n            : new util.Array(sizeOrArray)\n        : util.Buffer\n            ? util._Buffer_from(sizeOrArray)\n            : typeof Uint8Array === \"undefined\"\n                ? sizeOrArray\n                : new Uint8Array(sizeOrArray);\n};\n\n/**\n * Array implementation used in the browser. `Uint8Array` if supported, otherwise `Array`.\n * @type {Constructor<Uint8Array>}\n */\nutil.Array = typeof Uint8Array !== \"undefined\" ? Uint8Array /* istanbul ignore next */ : Array;\n\n/**\n * Any compatible Long instance.\n * This is a minimal stand-alone definition of a Long instance. The actual type is that exported by long.js.\n * @interface Long\n * @property {number} low Low bits\n * @property {number} high High bits\n * @property {boolean} unsigned Whether unsigned or not\n */\n\n/**\n * Long.js's Long class if available.\n * @type {Constructor<Long>}\n */\nutil.Long = /* istanbul ignore next */ util.global.dcodeIO && /* istanbul ignore next */ util.global.dcodeIO.Long\n         || /* istanbul ignore next */ util.global.Long\n         || util.inquire(\"long\");\n\n/**\n * Regular expression used to verify 2 bit (`bool`) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key2Re = /^true|false|0|1$/;\n\n/**\n * Regular expression used to verify 32 bit (`int32` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key32Re = /^-?(?:0|[1-9][0-9]*)$/;\n\n/**\n * Regular expression used to verify 64 bit (`int64` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key64Re = /^(?:[\\\\x00-\\\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;\n\n/**\n * Converts a number or long to an 8 characters long hash string.\n * @param {Long|number} value Value to convert\n * @returns {string} Hash\n */\nutil.longToHash = function longToHash(value) {\n    return value\n        ? util.LongBits.from(value).toHash()\n        : util.LongBits.zeroHash;\n};\n\n/**\n * Converts an 8 characters long hash string to a long or number.\n * @param {string} hash Hash\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long|number} Original value\n */\nutil.longFromHash = function longFromHash(hash, unsigned) {\n    var bits = util.LongBits.fromHash(hash);\n    if (util.Long)\n        return util.Long.fromBits(bits.lo, bits.hi, unsigned);\n    return bits.toNumber(Boolean(unsigned));\n};\n\n/**\n * Merges the properties of the source object into the destination object.\n * @memberof util\n * @param {Object.<string,*>} dst Destination object\n * @param {Object.<string,*>} src Source object\n * @param {boolean} [ifNotSet=false] Merges only if the key is not already set\n * @returns {Object.<string,*>} Destination object\n */\nfunction merge(dst, src, ifNotSet) { // used by converters\n    for (var keys = Object.keys(src), i = 0; i < keys.length; ++i)\n        if (dst[keys[i]] === undefined || !ifNotSet)\n            dst[keys[i]] = src[keys[i]];\n    return dst;\n}\n\nutil.merge = merge;\n\n/**\n * Converts the first character of a string to lower case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.lcFirst = function lcFirst(str) {\n    return str.charAt(0).toLowerCase() + str.substring(1);\n};\n\n/**\n * Creates a custom error constructor.\n * @memberof util\n * @param {string} name Error name\n * @returns {Constructor<Error>} Custom error constructor\n */\nfunction newError(name) {\n\n    function CustomError(message, properties) {\n\n        if (!(this instanceof CustomError))\n            return new CustomError(message, properties);\n\n        // Error.call(this, message);\n        // ^ just returns a new error instance because the ctor can be called as a function\n\n        Object.defineProperty(this, \"message\", { get: function() { return message; } });\n\n        /* istanbul ignore next */\n        if (Error.captureStackTrace) // node\n            Error.captureStackTrace(this, CustomError);\n        else\n            Object.defineProperty(this, \"stack\", { value: new Error().stack || \"\" });\n\n        if (properties)\n            merge(this, properties);\n    }\n\n    (CustomError.prototype = Object.create(Error.prototype)).constructor = CustomError;\n\n    Object.defineProperty(CustomError.prototype, \"name\", { get: function() { return name; } });\n\n    CustomError.prototype.toString = function toString() {\n        return this.name + \": \" + this.message;\n    };\n\n    return CustomError;\n}\n\nutil.newError = newError;\n\n/**\n * Constructs a new protocol error.\n * @classdesc Error subclass indicating a protocol specifc error.\n * @memberof util\n * @extends Error\n * @template T extends Message<T>\n * @constructor\n * @param {string} message Error message\n * @param {Object.<string,*>} [properties] Additional properties\n * @example\n * try {\n *     MyMessage.decode(someBuffer); // throws if required fields are missing\n * } catch (e) {\n *     if (e instanceof ProtocolError && e.instance)\n *         console.log(\"decoded so far: \" + JSON.stringify(e.instance));\n * }\n */\nutil.ProtocolError = newError(\"ProtocolError\");\n\n/**\n * So far decoded message instance.\n * @name util.ProtocolError#instance\n * @type {Message<T>}\n */\n\n/**\n * A OneOf getter as returned by {@link util.oneOfGetter}.\n * @typedef OneOfGetter\n * @type {function}\n * @returns {string|undefined} Set field name, if any\n */\n\n/**\n * Builds a getter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfGetter} Unbound getter\n */\nutil.oneOfGetter = function getOneOf(fieldNames) {\n    var fieldMap = {};\n    for (var i = 0; i < fieldNames.length; ++i)\n        fieldMap[fieldNames[i]] = 1;\n\n    /**\n     * @returns {string|undefined} Set field name, if any\n     * @this Object\n     * @ignore\n     */\n    return function() { // eslint-disable-line consistent-return\n        for (var keys = Object.keys(this), i = keys.length - 1; i > -1; --i)\n            if (fieldMap[keys[i]] === 1 && this[keys[i]] !== undefined && this[keys[i]] !== null)\n                return keys[i];\n    };\n};\n\n/**\n * A OneOf setter as returned by {@link util.oneOfSetter}.\n * @typedef OneOfSetter\n * @type {function}\n * @param {string|undefined} value Field name\n * @returns {undefined}\n */\n\n/**\n * Builds a setter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfSetter} Unbound setter\n */\nutil.oneOfSetter = function setOneOf(fieldNames) {\n\n    /**\n     * @param {string} name Field name\n     * @returns {undefined}\n     * @this Object\n     * @ignore\n     */\n    return function(name) {\n        for (var i = 0; i < fieldNames.length; ++i)\n            if (fieldNames[i] !== name)\n                delete this[fieldNames[i]];\n    };\n};\n\n/**\n * Default conversion options used for {@link Message#toJSON} implementations.\n *\n * These options are close to proto3's JSON mapping with the exception that internal types like Any are handled just like messages. More precisely:\n *\n * - Longs become strings\n * - Enums become string keys\n * - Bytes become base64 encoded strings\n * - (Sub-)Messages become plain objects\n * - Maps become plain objects with all string keys\n * - Repeated fields become arrays\n * - NaN and Infinity for float and double fields become strings\n *\n * @type {IConversionOptions}\n * @see https://developers.google.com/protocol-buffers/docs/proto3?hl=en#json\n */\nutil.toJSONOptions = {\n    longs: String,\n    enums: String,\n    bytes: String,\n    json: true\n};\n\n// Sets up buffer utility according to the environment (called in index-minimal)\nutil._configure = function() {\n    var Buffer = util.Buffer;\n    /* istanbul ignore if */\n    if (!Buffer) {\n        util._Buffer_from = util._Buffer_allocUnsafe = null;\n        return;\n    }\n    // because node 4.x buffers are incompatible & immutable\n    // see: https://github.com/dcodeIO/protobuf.js/pull/665\n    util._Buffer_from = Buffer.from !== Uint8Array.from && Buffer.from ||\n        /* istanbul ignore next */\n        function Buffer_from(value, encoding) {\n            return new Buffer(value, encoding);\n        };\n    util._Buffer_allocUnsafe = Buffer.allocUnsafe ||\n        /* istanbul ignore next */\n        function Buffer_allocUnsafe(size) {\n            return new Buffer(size);\n        };\n};\n", "\"use strict\";\nmodule.exports = verifier;\n\nvar Enum      = require(14),\n    util      = require(33);\n\nfunction invalid(field, expected) {\n    return field.name + \": \" + expected + (field.repeated && expected !== \"array\" ? \"[]\" : field.map && expected !== \"object\" ? \"{k:\"+field.keyType+\"}\" : \"\") + \" expected\";\n}\n\n/**\n * Generates a partial value verifier.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genVerifyValue(gen, field, fieldIndex, ref) {\n    /* eslint-disable no-unexpected-multiline */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) { gen\n            (\"switch(%s){\", ref)\n                (\"default:\")\n                    (\"return%j\", invalid(field, \"enum value\"));\n            for (var keys = Object.keys(field.resolvedType.values), j = 0; j < keys.length; ++j) gen\n                (\"case %i:\", field.resolvedType.values[keys[j]]);\n            gen\n                    (\"break\")\n            (\"}\");\n        } else {\n            gen\n            (\"{\")\n                (\"var e=types[%i].verify(%s);\", fieldIndex, ref)\n                (\"if(e)\")\n                    (\"return%j+e\", field.name + \".\")\n            (\"}\");\n        }\n    } else {\n        switch (field.type) {\n            case \"int32\":\n            case \"uint32\":\n            case \"sint32\":\n            case \"fixed32\":\n            case \"sfixed32\": gen\n                (\"if(!util.isInteger(%s))\", ref)\n                    (\"return%j\", invalid(field, \"integer\"));\n                break;\n            case \"int64\":\n            case \"uint64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n                (\"if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))\", ref, ref, ref, ref)\n                    (\"return%j\", invalid(field, \"integer|Long\"));\n                break;\n            case \"float\":\n            case \"double\": gen\n                (\"if(typeof %s!==\\\"number\\\")\", ref)\n                    (\"return%j\", invalid(field, \"number\"));\n                break;\n            case \"bool\": gen\n                (\"if(typeof %s!==\\\"boolean\\\")\", ref)\n                    (\"return%j\", invalid(field, \"boolean\"));\n                break;\n            case \"string\": gen\n                (\"if(!util.isString(%s))\", ref)\n                    (\"return%j\", invalid(field, \"string\"));\n                break;\n            case \"bytes\": gen\n                (\"if(!(%s&&typeof %s.length===\\\"number\\\"||util.isString(%s)))\", ref, ref, ref)\n                    (\"return%j\", invalid(field, \"buffer\"));\n                break;\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline */\n}\n\n/**\n * Generates a partial key verifier.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genVerifyKey(gen, field, ref) {\n    /* eslint-disable no-unexpected-multiline */\n    switch (field.keyType) {\n        case \"int32\":\n        case \"uint32\":\n        case \"sint32\":\n        case \"fixed32\":\n        case \"sfixed32\": gen\n            (\"if(!util.key32Re.test(%s))\", ref)\n                (\"return%j\", invalid(field, \"integer key\"));\n            break;\n        case \"int64\":\n        case \"uint64\":\n        case \"sint64\":\n        case \"fixed64\":\n        case \"sfixed64\": gen\n            (\"if(!util.key64Re.test(%s))\", ref) // see comment above: x is ok, d is not\n                (\"return%j\", invalid(field, \"integer|Long key\"));\n            break;\n        case \"bool\": gen\n            (\"if(!util.key2Re.test(%s))\", ref)\n                (\"return%j\", invalid(field, \"boolean key\"));\n            break;\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline */\n}\n\n/**\n * Generates a verifier specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction verifier(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n\n    var gen = util.codegen([\"m\"], mtype.name + \"$verify\")\n    (\"if(typeof m!==\\\"object\\\"||m===null)\")\n        (\"return%j\", \"object expected\");\n    var oneofs = mtype.oneofsArray,\n        seenFirstField = {};\n    if (oneofs.length) gen\n    (\"var p={}\");\n\n    for (var i = 0; i < /* initializes */ mtype.fieldsArray.length; ++i) {\n        var field = mtype._fieldsArray[i].resolve(),\n            ref   = \"m\" + util.safeProp(field.name);\n\n        if (field.optional) gen\n        (\"if(%s!=null&&m.hasOwnProperty(%j)){\", ref, field.name); // !== undefined && !== null\n\n        // map fields\n        if (field.map) { gen\n            (\"if(!util.isObject(%s))\", ref)\n                (\"return%j\", invalid(field, \"object\"))\n            (\"var k=Object.keys(%s)\", ref)\n            (\"for(var i=0;i<k.length;++i){\");\n                genVerifyKey(gen, field, \"k[i]\");\n                genVerifyValue(gen, field, i, ref + \"[k[i]]\")\n            (\"}\");\n\n        // repeated fields\n        } else if (field.repeated) { gen\n            (\"if(!Array.isArray(%s))\", ref)\n                (\"return%j\", invalid(field, \"array\"))\n            (\"for(var i=0;i<%s.length;++i){\", ref);\n                genVerifyValue(gen, field, i, ref + \"[i]\")\n            (\"}\");\n\n        // required or present fields\n        } else {\n            if (field.partOf) {\n                var oneofProp = util.safeProp(field.partOf.name);\n                if (seenFirstField[field.partOf.name] === 1) gen\n            (\"if(p%s===1)\", oneofProp)\n                (\"return%j\", field.partOf.name + \": multiple values\");\n                seenFirstField[field.partOf.name] = 1;\n                gen\n            (\"p%s=1\", oneofProp);\n            }\n            genVerifyValue(gen, field, i, ref);\n        }\n        if (field.optional) gen\n        (\"}\");\n    }\n    return gen\n    (\"return null\");\n    /* eslint-enable no-unexpected-multiline */\n}", "\"use strict\";\n\n/**\n * Wrappers for common types.\n * @type {Object.<string,IWrapper>}\n * @const\n */\nvar wrappers = exports;\n\nvar Message = require(19);\n\n/**\n * From object converter part of an {@link IWrapper}.\n * @typedef WrapperFromObjectConverter\n * @type {function}\n * @param {Object.<string,*>} object Plain object\n * @returns {Message<{}>} Message instance\n * @this Type\n */\n\n/**\n * To object converter part of an {@link IWrapper}.\n * @typedef WrapperToObjectConverter\n * @type {function}\n * @param {Message<{}>} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n * @this Type\n */\n\n/**\n * Common type wrapper part of {@link wrappers}.\n * @interface IWrapper\n * @property {WrapperFromObjectConverter} [fromObject] From object converter\n * @property {WrapperToObjectConverter} [toObject] To object converter\n */\n\n// Custom wrapper for Any\nwrappers[\".google.protobuf.Any\"] = {\n\n    fromObject: function(object) {\n\n        // unwrap value type if mapped\n        if (object && object[\"@type\"]) {\n             // Only use fully qualified type name after the last '/'\n            var name = object[\"@type\"].substring(object[\"@type\"].lastIndexOf(\"/\") + 1);\n            var type = this.lookup(name);\n            /* istanbul ignore else */\n            if (type) {\n                // type_url does not accept leading \".\"\n                var type_url = object[\"@type\"].charAt(0) === \".\" ?\n                    object[\"@type\"].substr(1) : object[\"@type\"];\n                // type_url prefix is optional, but path seperator is required\n                if (type_url.indexOf(\"/\") === -1) {\n                    type_url = \"/\" + type_url;\n                }\n                return this.create({\n                    type_url: type_url,\n                    value: type.encode(type.fromObject(object)).finish()\n                });\n            }\n        }\n\n        return this.fromObject(object);\n    },\n\n    toObject: function(message, options) {\n\n        // Default prefix\n        var googleApi = \"type.googleapis.com/\";\n        var prefix = \"\";\n        var name = \"\";\n\n        // decode value if requested and unmapped\n        if (options && options.json && message.type_url && message.value) {\n            // Only use fully qualified type name after the last '/'\n            name = message.type_url.substring(message.type_url.lastIndexOf(\"/\") + 1);\n            // Separate the prefix used\n            prefix = message.type_url.substring(0, message.type_url.lastIndexOf(\"/\") + 1);\n            var type = this.lookup(name);\n            /* istanbul ignore else */\n            if (type)\n                message = type.decode(message.value);\n        }\n\n        // wrap value if unmapped\n        if (!(message instanceof this.ctor) && message instanceof Message) {\n            var object = message.$type.toObject(message, options);\n            var messageName = message.$type.fullName[0] === \".\" ?\n                message.$type.fullName.substr(1) : message.$type.fullName;\n            // Default to type.googleapis.com prefix if no prefix is used\n            if (prefix === \"\") {\n                prefix = googleApi;\n            }\n            name = prefix + messageName;\n            object[\"@type\"] = name;\n            return object;\n        }\n\n        return this.toObject(message, options);\n    }\n};\n", "\"use strict\";\nmodule.exports = Writer;\n\nvar util      = require(35);\n\nvar BufferWriter; // cyclic\n\nvar LongBits  = util.LongBits,\n    base64    = util.base64,\n    utf8      = util.utf8;\n\n/**\n * Constructs a new writer operation instance.\n * @classdesc Scheduled writer operation.\n * @constructor\n * @param {function(*, Uint8Array, number)} fn Function to call\n * @param {number} len Value byte length\n * @param {*} val Value to write\n * @ignore\n */\nfunction Op(fn, len, val) {\n\n    /**\n     * Function to call.\n     * @type {function(Uint8Array, number, *)}\n     */\n    this.fn = fn;\n\n    /**\n     * Value byte length.\n     * @type {number}\n     */\n    this.len = len;\n\n    /**\n     * Next operation.\n     * @type {Writer.Op|undefined}\n     */\n    this.next = undefined;\n\n    /**\n     * Value to write.\n     * @type {*}\n     */\n    this.val = val; // type varies\n}\n\n/* istanbul ignore next */\nfunction noop() {} // eslint-disable-line no-empty-function\n\n/**\n * Constructs a new writer state instance.\n * @classdesc Copied writer state.\n * @memberof Writer\n * @constructor\n * @param {Writer} writer Writer to copy state from\n * @ignore\n */\nfunction State(writer) {\n\n    /**\n     * Current head.\n     * @type {Writer.Op}\n     */\n    this.head = writer.head;\n\n    /**\n     * Current tail.\n     * @type {Writer.Op}\n     */\n    this.tail = writer.tail;\n\n    /**\n     * Current buffer length.\n     * @type {number}\n     */\n    this.len = writer.len;\n\n    /**\n     * Next state.\n     * @type {State|null}\n     */\n    this.next = writer.states;\n}\n\n/**\n * Constructs a new writer instance.\n * @classdesc Wire format writer using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n */\nfunction Writer() {\n\n    /**\n     * Current length.\n     * @type {number}\n     */\n    this.len = 0;\n\n    /**\n     * Operations head.\n     * @type {Object}\n     */\n    this.head = new Op(noop, 0, 0);\n\n    /**\n     * Operations tail\n     * @type {Object}\n     */\n    this.tail = this.head;\n\n    /**\n     * Linked forked states.\n     * @type {Object|null}\n     */\n    this.states = null;\n\n    // When a value is written, the writer calculates its byte length and puts it into a linked\n    // list of operations to perform when finish() is called. This both allows us to allocate\n    // buffers of the exact required size and reduces the amount of work we have to do compared\n    // to first calculating over objects and then encoding over objects. In our case, the encoding\n    // part is just a linked list walk calling operations with already prepared values.\n}\n\nvar create = function create() {\n    return util.Buffer\n        ? function create_buffer_setup() {\n            return (Writer.create = function create_buffer() {\n                return new BufferWriter();\n            })();\n        }\n        /* istanbul ignore next */\n        : function create_array() {\n            return new Writer();\n        };\n};\n\n/**\n * Creates a new writer.\n * @function\n * @returns {BufferWriter|Writer} A {@link BufferWriter} when Buffers are supported, otherwise a {@link Writer}\n */\nWriter.create = create();\n\n/**\n * Allocates a buffer of the specified size.\n * @param {number} size Buffer size\n * @returns {Uint8Array} Buffer\n */\nWriter.alloc = function alloc(size) {\n    return new util.Array(size);\n};\n\n// Use Uint8Array buffer pool in the browser, just like node does with buffers\n/* istanbul ignore else */\nif (util.Array !== Array)\n    Writer.alloc = util.pool(Writer.alloc, util.Array.prototype.subarray);\n\n/**\n * Pushes a new operation to the queue.\n * @param {function(Uint8Array, number, *)} fn Function to call\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @returns {Writer} `this`\n * @private\n */\nWriter.prototype._push = function push(fn, len, val) {\n    this.tail = this.tail.next = new Op(fn, len, val);\n    this.len += len;\n    return this;\n};\n\nfunction writeByte(val, buf, pos) {\n    buf[pos] = val & 255;\n}\n\nfunction writeVarint32(val, buf, pos) {\n    while (val > 127) {\n        buf[pos++] = val & 127 | 128;\n        val >>>= 7;\n    }\n    buf[pos] = val;\n}\n\n/**\n * Constructs a new varint writer operation instance.\n * @classdesc Scheduled varint writer operation.\n * @extends Op\n * @constructor\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @ignore\n */\nfunction VarintOp(len, val) {\n    this.len = len;\n    this.next = undefined;\n    this.val = val;\n}\n\nVarintOp.prototype = Object.create(Op.prototype);\nVarintOp.prototype.fn = writeVarint32;\n\n/**\n * Writes an unsigned 32 bit value as a varint.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.uint32 = function write_uint32(value) {\n    // here, the call to this.push has been inlined and a varint specific Op subclass is used.\n    // uint32 is by far the most frequently used operation and benefits significantly from this.\n    this.len += (this.tail = this.tail.next = new VarintOp(\n        (value = value >>> 0)\n                < 128       ? 1\n        : value < 16384     ? 2\n        : value < 2097152   ? 3\n        : value < 268435456 ? 4\n        :                     5,\n    value)).len;\n    return this;\n};\n\n/**\n * Writes a signed 32 bit value as a varint.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.int32 = function write_int32(value) {\n    return value < 0\n        ? this._push(writeVarint64, 10, LongBits.fromNumber(value)) // 10 bytes per spec\n        : this.uint32(value);\n};\n\n/**\n * Writes a 32 bit value as a varint, zig-zag encoded.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sint32 = function write_sint32(value) {\n    return this.uint32((value << 1 ^ value >> 31) >>> 0);\n};\n\nfunction writeVarint64(val, buf, pos) {\n    while (val.hi) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = (val.lo >>> 7 | val.hi << 25) >>> 0;\n        val.hi >>>= 7;\n    }\n    while (val.lo > 127) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = val.lo >>> 7;\n    }\n    buf[pos++] = val.lo;\n}\n\n/**\n * Writes an unsigned 64 bit value as a varint.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.uint64 = function write_uint64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a signed 64 bit value as a varint.\n * @function\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.int64 = Writer.prototype.uint64;\n\n/**\n * Writes a signed 64 bit value as a varint, zig-zag encoded.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sint64 = function write_sint64(value) {\n    var bits = LongBits.from(value).zzEncode();\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a boolish value as a varint.\n * @param {boolean} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bool = function write_bool(value) {\n    return this._push(writeByte, 1, value ? 1 : 0);\n};\n\nfunction writeFixed32(val, buf, pos) {\n    buf[pos    ] =  val         & 255;\n    buf[pos + 1] =  val >>> 8   & 255;\n    buf[pos + 2] =  val >>> 16  & 255;\n    buf[pos + 3] =  val >>> 24;\n}\n\n/**\n * Writes an unsigned 32 bit value as fixed 32 bits.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.fixed32 = function write_fixed32(value) {\n    return this._push(writeFixed32, 4, value >>> 0);\n};\n\n/**\n * Writes a signed 32 bit value as fixed 32 bits.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sfixed32 = Writer.prototype.fixed32;\n\n/**\n * Writes an unsigned 64 bit value as fixed 64 bits.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.fixed64 = function write_fixed64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeFixed32, 4, bits.lo)._push(writeFixed32, 4, bits.hi);\n};\n\n/**\n * Writes a signed 64 bit value as fixed 64 bits.\n * @function\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sfixed64 = Writer.prototype.fixed64;\n\n/**\n * Writes a float (32 bit).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.float = function write_float(value) {\n    return this._push(util.float.writeFloatLE, 4, value);\n};\n\n/**\n * Writes a double (64 bit float).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.double = function write_double(value) {\n    return this._push(util.float.writeDoubleLE, 8, value);\n};\n\nvar writeBytes = util.Array.prototype.set\n    ? function writeBytes_set(val, buf, pos) {\n        buf.set(val, pos); // also works for plain array values\n    }\n    /* istanbul ignore next */\n    : function writeBytes_for(val, buf, pos) {\n        for (var i = 0; i < val.length; ++i)\n            buf[pos + i] = val[i];\n    };\n\n/**\n * Writes a sequence of bytes.\n * @param {Uint8Array|string} value Buffer or base64 encoded string to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bytes = function write_bytes(value) {\n    var len = value.length >>> 0;\n    if (!len)\n        return this._push(writeByte, 1, 0);\n    if (util.isString(value)) {\n        var buf = Writer.alloc(len = base64.length(value));\n        base64.decode(value, buf, 0);\n        value = buf;\n    }\n    return this.uint32(len)._push(writeBytes, len, value);\n};\n\n/**\n * Writes a string.\n * @param {string} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.string = function write_string(value) {\n    var len = utf8.length(value);\n    return len\n        ? this.uint32(len)._push(utf8.write, len, value)\n        : this._push(writeByte, 1, 0);\n};\n\n/**\n * Forks this writer's state by pushing it to a stack.\n * Calling {@link Writer#reset|reset} or {@link Writer#ldelim|ldelim} resets the writer to the previous state.\n * @returns {Writer} `this`\n */\nWriter.prototype.fork = function fork() {\n    this.states = new State(this);\n    this.head = this.tail = new Op(noop, 0, 0);\n    this.len = 0;\n    return this;\n};\n\n/**\n * Resets this instance to the last state.\n * @returns {Writer} `this`\n */\nWriter.prototype.reset = function reset() {\n    if (this.states) {\n        this.head   = this.states.head;\n        this.tail   = this.states.tail;\n        this.len    = this.states.len;\n        this.states = this.states.next;\n    } else {\n        this.head = this.tail = new Op(noop, 0, 0);\n        this.len  = 0;\n    }\n    return this;\n};\n\n/**\n * Resets to the last state and appends the fork state's current write length as a varint followed by its operations.\n * @returns {Writer} `this`\n */\nWriter.prototype.ldelim = function ldelim() {\n    var head = this.head,\n        tail = this.tail,\n        len  = this.len;\n    this.reset().uint32(len);\n    if (len) {\n        this.tail.next = head.next; // skip noop\n        this.tail = tail;\n        this.len += len;\n    }\n    return this;\n};\n\n/**\n * Finishes the write operation.\n * @returns {Uint8Array} Finished buffer\n */\nWriter.prototype.finish = function finish() {\n    var head = this.head.next, // skip noop\n        buf  = this.constructor.alloc(this.len),\n        pos  = 0;\n    while (head) {\n        head.fn(head.val, buf, pos);\n        pos += head.len;\n        head = head.next;\n    }\n    // this.head = this.tail = null;\n    return buf;\n};\n\nWriter._configure = function(BufferWriter_) {\n    BufferWriter = BufferWriter_;\n    Writer.create = create();\n    BufferWriter._configure();\n};\n", "\"use strict\";\nmodule.exports = <PERSON><PERSON>erWriter;\n\n// extends Writer\nvar Writer = require(38);\n(BufferWriter.prototype = Object.create(Writer.prototype)).constructor = BufferWriter;\n\nvar util = require(35);\n\n/**\n * Constructs a new buffer writer instance.\n * @classdesc Wire format writer using node buffers.\n * @extends Writer\n * @constructor\n */\nfunction BufferWriter() {\n    Writer.call(this);\n}\n\nBufferWriter._configure = function () {\n    /**\n     * Allocates a buffer of the specified size.\n     * @function\n     * @param {number} size Buffer size\n     * @returns {Buffer} Buffer\n     */\n    BufferWriter.alloc = util._Buffer_allocUnsafe;\n\n    BufferWriter.writeBytesBuffer = util.Buffer && util.Buffer.prototype instanceof Uint8Array && util.Buffer.prototype.set.name === \"set\"\n        ? function writeBytesBuffer_set(val, buf, pos) {\n          buf.set(val, pos); // faster than copy (requires node >= 4 where Buffers extend Uint8Array and set is properly inherited)\n          // also works for plain array values\n        }\n        /* istanbul ignore next */\n        : function writeBytesBuffer_copy(val, buf, pos) {\n          if (val.copy) // Buffer values\n            val.copy(buf, pos, 0, val.length);\n          else for (var i = 0; i < val.length;) // plain array values\n            buf[pos++] = val[i++];\n        };\n};\n\n\n/**\n * @override\n */\nBufferWriter.prototype.bytes = function write_bytes_buffer(value) {\n    if (util.isString(value))\n        value = util._Buffer_from(value, \"base64\");\n    var len = value.length >>> 0;\n    this.uint32(len);\n    if (len)\n        this._push(BufferWriter.writeBytesBuffer, len, value);\n    return this;\n};\n\nfunction writeStringBuffer(val, buf, pos) {\n    if (val.length < 40) // plain js is faster for short strings (probably due to redundant assertions)\n        util.utf8.write(val, buf, pos);\n    else if (buf.utf8Write)\n        buf.utf8Write(val, pos);\n    else\n        buf.write(val, pos);\n}\n\n/**\n * @override\n */\nBufferWriter.prototype.string = function write_string_buffer(value) {\n    var len = util.Buffer.byteLength(value);\n    this.uint32(len);\n    if (len)\n        this._push(writeStringBuffer, len, value);\n    return this;\n};\n\n\n/**\n * Finishes the write operation.\n * @name BufferWriter#finish\n * @function\n * @returns {Buffer} Finished buffer\n */\n\nBufferWriter._configure();\n"], "sourceRoot": "."}