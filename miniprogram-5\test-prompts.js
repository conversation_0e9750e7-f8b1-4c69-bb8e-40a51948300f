// 提示词模板测试工具
// 测试各模块专业化提示词的生成效果

const { 
  MEIHUA_PROMPTS, 
  YIJING_PROMPTS, 
  BAZI_PROMPTS, 
  ZIWEI_PROMPTS 
} = require('./miniprogram/utils/prompt-templates.js');

const { buildSystemPrompt } = require('./miniprogram/utils/ai-service.js');

/**
 * 测试梅花易数提示词
 */
function testMeihuaPrompts() {
  console.log('\n=== 梅花易数提示词测试 ===');
  
  const questionTypes = ['财运', '事业', '婚姻', '健康', '学业'];
  
  questionTypes.forEach(type => {
    console.log(`\n--- ${type}问题提示词 ---`);
    console.log('系统提示词:');
    console.log(MEIHUA_PROMPTS.system.substring(0, 200) + '...');
    
    console.log('\n用户特定提示词:');
    console.log(MEIHUA_PROMPTS.user(type));
    
    console.log('\n完整系统提示词:');
    const fullPrompt = buildSystemPrompt(type, 'meihua');
    console.log(fullPrompt.substring(0, 300) + '...');
    console.log(`总长度: ${fullPrompt.length} 字符`);
  });
}

/**
 * 测试周易六爻提示词
 */
function testYijingPrompts() {
  console.log('\n=== 周易六爻提示词测试 ===');
  
  const questionTypes = ['财运', '事业', '婚姻'];
  
  questionTypes.forEach(type => {
    console.log(`\n--- ${type}问题提示词 ---`);
    const fullPrompt = buildSystemPrompt(type, 'yijing');
    
    // 检查关键术语
    const keyTerms = ['用神', '世爻', '应爻', '六亲', '六神', '动爻'];
    const foundTerms = keyTerms.filter(term => fullPrompt.includes(term));
    
    console.log(`包含专业术语: ${foundTerms.join(', ')}`);
    console.log(`提示词长度: ${fullPrompt.length} 字符`);
    
    // 检查古籍引用
    const ancientBooks = ['增删卜易', '卜筮正宗', '火珠林', '黄金策'];
    const foundBooks = ancientBooks.filter(book => fullPrompt.includes(book));
    console.log(`引用古籍: ${foundBooks.join(', ')}`);
  });
}

/**
 * 测试八字提示词
 */
function testBaziPrompts() {
  console.log('\n=== 子平八字提示词测试 ===');
  
  const questionTypes = ['财运', '事业', '婚姻'];
  
  questionTypes.forEach(type => {
    console.log(`\n--- ${type}问题提示词 ---`);
    const fullPrompt = buildSystemPrompt(type, 'bazi');
    
    // 检查关键术语
    const keyTerms = ['日主', '用神', '十神', '大运', '流年', '格局'];
    const foundTerms = keyTerms.filter(term => fullPrompt.includes(term));
    
    console.log(`包含专业术语: ${foundTerms.join(', ')}`);
    console.log(`提示词长度: ${fullPrompt.length} 字符`);
    
    // 检查古籍引用
    const ancientBooks = ['子平真诠', '滴天髓', '穷通宝鉴', '三命通会'];
    const foundBooks = ancientBooks.filter(book => fullPrompt.includes(book));
    console.log(`引用古籍: ${foundBooks.join(', ')}`);
  });
}

/**
 * 测试紫微斗数提示词
 */
function testZiweiPrompts() {
  console.log('\n=== 紫微斗数提示词测试 ===');
  
  const questionTypes = ['财运', '事业', '婚姻'];
  
  questionTypes.forEach(type => {
    console.log(`\n--- ${type}问题提示词 ---`);
    const fullPrompt = buildSystemPrompt(type, 'ziwei');
    
    // 检查关键术语
    const keyTerms = ['命宫', '身宫', '十四主星', '四化', '大限', '流年'];
    const foundTerms = keyTerms.filter(term => fullPrompt.includes(term));
    
    console.log(`包含专业术语: ${foundTerms.join(', ')}`);
    console.log(`提示词长度: ${fullPrompt.length} 字符`);
    
    // 检查主星名称
    const mainStars = ['紫微', '天机', '太阳', '武曲', '天同', '廉贞'];
    const foundStars = mainStars.filter(star => fullPrompt.includes(star));
    console.log(`包含主星: ${foundStars.join(', ')}`);
  });
}

/**
 * 对比不同模块的提示词特点
 */
function comparePromptFeatures() {
  console.log('\n=== 提示词特点对比 ===');
  
  const modules = ['meihua', 'yijing', 'bazi', 'ziwei'];
  const questionType = '财运';
  
  modules.forEach(module => {
    const prompt = buildSystemPrompt(questionType, module);
    
    console.log(`\n--- ${module.toUpperCase()} 模块 ---`);
    console.log(`提示词总长度: ${prompt.length} 字符`);
    
    // 统计关键词密度
    const keywords = {
      '古籍': (prompt.match(/古籍|经典|原著/g) || []).length,
      '专业术语': (prompt.match(/用神|日主|命宫|体卦/g) || []).length,
      '时间预测': (prompt.match(/时间|年月日|大运|流年/g) || []).length,
      '具体分析': (prompt.match(/具体|详细|精确|准确/g) || []).length
    };
    
    Object.entries(keywords).forEach(([key, count]) => {
      console.log(`${key}关键词出现: ${count} 次`);
    });
  });
}

/**
 * 测试提示词的完整性
 */
function testPromptCompleteness() {
  console.log('\n=== 提示词完整性测试 ===');
  
  const requiredSections = [
    '核心理论依据',
    '分析框架', 
    '专业术语',
    '分析要求'
  ];
  
  const modules = ['meihua', 'yijing', 'bazi', 'ziwei'];
  
  modules.forEach(module => {
    console.log(`\n--- ${module.toUpperCase()} 完整性检查 ---`);
    const prompt = buildSystemPrompt('财运', module);
    
    const missingSections = requiredSections.filter(section => 
      !prompt.includes(section) && !prompt.includes(section.replace(/[【】]/g, ''))
    );
    
    if (missingSections.length === 0) {
      console.log('✅ 所有必需章节都已包含');
    } else {
      console.log(`❌ 缺少章节: ${missingSections.join(', ')}`);
    }
    
    // 检查是否有具体的分析要求
    const hasSpecificRequirements = prompt.includes('必须') && prompt.includes('分析');
    console.log(`具体分析要求: ${hasSpecificRequirements ? '✅' : '❌'}`);
    
    // 检查是否有古籍引用
    const hasAncientReference = prompt.includes('著') || prompt.includes('经典');
    console.log(`古籍理论依据: ${hasAncientReference ? '✅' : '❌'}`);
  });
}

/**
 * 主测试函数
 */
function runPromptTests() {
  console.log('🚀 开始提示词模板测试...\n');
  
  try {
    // 测试各模块提示词
    testMeihuaPrompts();
    testYijingPrompts();
    testBaziPrompts();
    testZiweiPrompts();
    
    // 对比分析
    comparePromptFeatures();
    
    // 完整性测试
    testPromptCompleteness();
    
    console.log('\n✅ 提示词模板测试完成');
    console.log('\n📋 测试总结:');
    console.log('1. 梅花易数提示词 - 包含体用卦理论');
    console.log('2. 周易六爻提示词 - 包含六亲六神理论');
    console.log('3. 子平八字提示词 - 包含十神格局理论');
    console.log('4. 紫微斗数提示词 - 包含星曜宫位理论');
    console.log('5. 所有模块都包含古籍依据和专业术语');
    console.log('6. 针对不同问题类型有专门的分析要求');
    
  } catch (error) {
    console.error('测试过程中出现错误:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runPromptTests();
}

module.exports = {
  testMeihuaPrompts,
  testYijingPrompts,
  testBaziPrompts,
  testZiweiPrompts,
  comparePromptFeatures,
  testPromptCompleteness,
  runPromptTests
};
