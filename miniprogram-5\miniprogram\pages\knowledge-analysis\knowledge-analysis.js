// 知识库分析页面
Page({
  data: {
    analysisStatus: '未开始',
    currentStep: '',
    analysisResults: {},
    logs: [],
    isAnalyzing: false,
    progress: 0,
    
    // 分析结果展示
    structureData: null,
    keywordsData: null,
    contentData: null,
    terminologyData: null,
    summaryData: null
  },

  onLoad() {
    this.addLog('知识库分析页面已加载', 'info');
  },

  // 添加日志
  addLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const log = {
      time: timestamp,
      message: message,
      type: type
    };
    
    const logs = this.data.logs;
    logs.unshift(log);
    
    // 限制日志数量
    if (logs.length > 50) {
      logs.pop();
    }
    
    this.setData({ logs });
    console.log(`[${type.toUpperCase()}] ${timestamp} - ${message}`);
  },

  // 开始快速分析
  async startFullAnalysis() {
    if (this.data.isAnalyzing) {
      wx.showToast({
        title: '分析正在进行中',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isAnalyzing: true,
      progress: 0,
      analysisStatus: '快速分析中',
      analysisResults: {}
    });

    this.addLog('🚀 开始知识库快速分析（避免超时）...', 'info');

    try {
      // 使用超快速分析器
      await this.performQuickAnalysis();

      this.setData({
        analysisStatus: '分析完成',
        progress: 100,
        isAnalyzing: false
      });

      this.addLog('✅ 知识库快速分析完成', 'success');

      wx.showToast({
        title: '分析完成',
        icon: 'success'
      });

    } catch (error) {
      this.addLog(`❌ 分析过程出错: ${error.message}`, 'error');
      this.setData({
        analysisStatus: '分析失败',
        isAnalyzing: false
      });

      wx.showToast({
        title: '分析失败',
        icon: 'error'
      });
    }
  },

  // 执行快速分析
  async performQuickAnalysis() {
    this.setData({
      currentStep: '正在进行超快速分析...',
      progress: 20
    });

    this.addLog('📊 执行超快速分析...', 'info');

    try {
      const result = await wx.cloud.callFunction({
        name: 'quick-analyzer',
        data: { action: 'quick_analysis' }
      });

      if (result.result.success) {
        const analysis = result.result.analysis;

        this.setData({
          'analysisResults.structure': analysis,
          progress: 100,
          currentStep: '快速分析完成'
        });

        this.addLog(`📈 总记录数: ${analysis.overview.total_records}`, 'success');
        this.addLog(`📂 发现分类: ${analysis.sample_data.categories.join(', ')}`, 'info');
        this.addLog(`👤 发现作者: ${analysis.sample_data.authors.join(', ')}`, 'info');
        this.addLog(`🔍 发现术语: ${analysis.sample_data.found_terms.join(', ')}`, 'info');
        this.addLog(`💾 预估总大小: ${analysis.estimates.estimated_total_size_mb}MB`, 'info');

        analysis.recommendations.forEach(rec => {
          this.addLog(`💡 ${rec}`, 'info');
        });

      } else {
        throw new Error(result.result.error || '快速分析失败');
      }

    } catch (error) {
      this.addLog(`❌ 快速分析失败: ${error.message}`, 'error');
      throw error;
    }
  },

  // 开始结构化处理
  async startStructurization() {
    if (this.data.isAnalyzing) {
      wx.showToast({
        title: '处理正在进行中',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认结构化处理',
      content: '这将对您的437个古籍文件进行结构化处理，包括术语提取、索引创建、内容优化等。处理时间约5-10分钟，是否继续？',
      confirmText: '开始处理',
      cancelText: '取消',
      success: async (res) => {
        if (res.confirm) {
          await this.performStructurization();
        }
      }
    });
  },

  // 执行结构化处理
  async performStructurization() {
    this.setData({
      isAnalyzing: true,
      progress: 0,
      analysisStatus: '结构化处理中',
      currentStep: '正在进行知识库结构化处理...'
    });

    this.addLog('🔧 开始知识库结构化处理...', 'info');

    try {
      const result = await wx.cloud.callFunction({
        name: 'knowledge-structurizer',
        data: { action: 'full_structurize' }
      });

      if (result.result.success) {
        this.setData({
          analysisStatus: '结构化完成',
          progress: 100,
          isAnalyzing: false,
          currentStep: ''
        });

        this.addLog('✅ 知识库结构化处理完成！', 'success');
        this.addLog(`📊 处理统计: 共处理${result.result.results.total_processed.total}条记录`, 'info');
        this.addLog(`⏱️ 处理耗时: ${Math.round(result.result.results.processing_time / 1000)}秒`, 'info');

        wx.showModal({
          title: '结构化完成',
          content: `知识库结构化处理已完成！\n\n处理记录: ${result.result.results.total_processed.total}条\n处理时间: ${Math.round(result.result.results.processing_time / 1000)}秒\n\n现在您的知识库已经完全结构化，AI分析准确性将大幅提升！`,
          showCancel: false,
          confirmText: '太好了'
        });

      } else {
        throw new Error(result.result.error || '结构化处理失败');
      }

    } catch (error) {
      this.addLog(`❌ 结构化处理失败: ${error.message}`, 'error');
      this.setData({
        analysisStatus: '处理失败',
        isAnalyzing: false,
        currentStep: ''
      });

      wx.showToast({
        title: '处理失败',
        icon: 'error'
      });
    }
  },

  // 执行结构分析
  async performStructureAnalysis() {
    this.setData({ currentStep: '正在分析知识库结构...' });
    this.addLog('📊 开始结构分析...', 'info');
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'simple-knowledge-analyzer',
        data: { action: 'analyze_structure' }
      });
      
      if (result.result.success) {
        this.setData({ 
          structureData: result.result.analysis,
          progress: 20
        });
        this.addLog(`✅ 结构分析完成 - 共${result.result.analysis.total_records}条记录`, 'success');
      } else {
        throw new Error(result.result.error || '结构分析失败');
      }
    } catch (error) {
      this.addLog(`❌ 结构分析失败: ${error.message}`, 'error');
      throw error;
    }
  },

  // 执行关键词提取
  async performKeywordExtraction() {
    this.setData({ currentStep: '正在提取关键词...' });
    this.addLog('🔍 开始关键词提取...', 'info');
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'simple-knowledge-analyzer',
        data: { action: 'extract_keywords' }
      });
      
      if (result.result.success) {
        this.setData({ 
          keywordsData: result.result.keywords,
          progress: 40
        });
        this.addLog(`✅ 关键词提取完成 - 发现${result.result.keywords.common_terms.length}个常用术语`, 'success');
      } else {
        throw new Error(result.result.error || '关键词提取失败');
      }
    } catch (error) {
      this.addLog(`❌ 关键词提取失败: ${error.message}`, 'error');
      throw error;
    }
  },

  // 执行内容分类
  async performContentCategorization() {
    this.setData({ currentStep: '正在分析内容分类...' });
    this.addLog('📚 开始内容分类分析...', 'info');
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'simple-knowledge-analyzer',
        data: { action: 'categorize_content' }
      });
      
      if (result.result.success) {
        this.setData({ 
          contentData: result.result.content_analysis,
          progress: 60
        });
        this.addLog(`✅ 内容分类完成 - 分析了${result.result.content_analysis.sample_size}个样本`, 'success');
      } else {
        throw new Error(result.result.error || '内容分类失败');
      }
    } catch (error) {
      this.addLog(`❌ 内容分类失败: ${error.message}`, 'error');
      throw error;
    }
  },

  // 执行术语分析
  async performTerminologyAnalysis() {
    this.setData({ currentStep: '正在分析专业术语...' });
    this.addLog('📖 开始术语分析...', 'info');
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'simple-knowledge-analyzer',
        data: { action: 'analyze_terminology' }
      });
      
      if (result.result.success) {
        this.setData({ 
          terminologyData: result.result.terminology,
          progress: 80
        });
        this.addLog(`✅ 术语分析完成 - 词典包含${result.result.terminology.dictionary_size}个术语`, 'success');
      } else {
        throw new Error(result.result.error || '术语分析失败');
      }
    } catch (error) {
      this.addLog(`❌ 术语分析失败: ${error.message}`, 'error');
      throw error;
    }
  },

  // 生成总结报告
  async performSummaryGeneration() {
    this.setData({ currentStep: '正在生成总结报告...' });
    this.addLog('📋 开始生成总结报告...', 'info');
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'simple-knowledge-analyzer',
        data: { action: 'generate_summary' }
      });
      
      if (result.result.success) {
        this.setData({ 
          summaryData: result.result.summary,
          progress: 100
        });
        this.addLog(`✅ 总结报告生成完成`, 'success');
      } else {
        throw new Error(result.result.error || '总结报告生成失败');
      }
    } catch (error) {
      this.addLog(`❌ 总结报告生成失败: ${error.message}`, 'error');
      throw error;
    }
  },

  // 查看详细结果
  viewDetailedResults(e) {
    const type = e.currentTarget.dataset.type;
    const data = this.data[`${type}Data`];
    
    if (!data) {
      wx.showToast({
        title: '暂无数据',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: `/pages/analysis-detail/analysis-detail?type=${type}&data=${encodeURIComponent(JSON.stringify(data))}`
    });
  },

  // 导出分析结果
  exportResults() {
    const results = {
      structure: this.data.structureData,
      keywords: this.data.keywordsData,
      content: this.data.contentData,
      terminology: this.data.terminologyData,
      summary: this.data.summaryData,
      exported_at: new Date().toISOString()
    };
    
    // 这里可以实现导出功能，比如保存到云存储
    console.log('导出分析结果:', results);
    
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    });
  },

  // 清除日志
  clearLogs() {
    this.setData({ logs: [] });
    this.addLog('日志已清除', 'info');
  },

  // 重新开始分析
  restartAnalysis() {
    wx.showModal({
      title: '确认重新分析',
      content: '这将清除当前的分析结果，是否继续？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            analysisStatus: '未开始',
            currentStep: '',
            analysisResults: {},
            logs: [],
            isAnalyzing: false,
            progress: 0,
            structureData: null,
            keywordsData: null,
            contentData: null,
            terminologyData: null,
            summaryData: null
          });
          this.addLog('已重置分析状态', 'info');
        }
      }
    });
  }
});
