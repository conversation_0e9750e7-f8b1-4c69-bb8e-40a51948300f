// 云函数：quick-analyzer
// 超快速知识库分析器，专门解决超时问题

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { action = 'quick_analysis' } = event;
  
  console.log(`超快速分析器被调用: ${action}`);
  
  try {
    switch (action) {
      case 'quick_analysis':
        return await performQuickAnalysis();
      case 'basic_stats':
        return await getBasicStats();
      default:
        return { success: false, error: '未知的分析操作' };
    }
  } catch (error) {
    console.error('快速分析失败:', error);
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * 执行超快速分析
 */
async function performQuickAnalysis() {
  console.log('开始超快速分析...');
  
  try {
    // 1. 获取总数（最快）
    const totalCount = await db.collection('knowledge_base').count();
    console.log('总记录数:', totalCount.total);
    
    // 2. 只获取前5条记录做样本分析
    const sampleRecords = await db.collection('knowledge_base')
      .field({
        title: true,
        category: true,
        author: true,
        content: true
      })
      .limit(5)
      .get();
    
    console.log('样本记录数:', sampleRecords.data.length);
    
    // 3. 快速统计
    const stats = {
      total_records: totalCount.total,
      sample_size: sampleRecords.data.length,
      categories: new Set(),
      authors: new Set(),
      total_content_length: 0,
      avg_content_length: 0,
      sample_titles: []
    };
    
    // 4. 分析样本
    sampleRecords.data.forEach(record => {
      stats.categories.add(record.category || '未分类');
      stats.authors.add(record.author || '未知作者');
      stats.total_content_length += (record.content || '').length;
      stats.sample_titles.push(record.title);
    });
    
    stats.avg_content_length = Math.round(stats.total_content_length / sampleRecords.data.length);
    stats.categories = Array.from(stats.categories);
    stats.authors = Array.from(stats.authors);
    
    // 5. 预定义术语快速检测
    const commonTerms = ['卦', '爻', '八字', '命宫', '用神', '体卦', '世爻', '大运'];
    const foundTerms = [];
    
    const sampleText = sampleRecords.data
      .map(r => (r.title || '') + ' ' + (r.content || '').substring(0, 200))
      .join(' ');
    
    commonTerms.forEach(term => {
      if (sampleText.includes(term)) {
        foundTerms.push(term);
      }
    });
    
    console.log('超快速分析完成');
    
    return {
      success: true,
      analysis: {
        overview: {
          total_records: stats.total_records,
          sample_analyzed: stats.sample_size,
          categories_found: stats.categories.length,
          authors_found: stats.authors.length,
          avg_content_size_kb: Math.round(stats.avg_content_length / 1024)
        },
        sample_data: {
          categories: stats.categories,
          authors: stats.authors,
          sample_titles: stats.sample_titles,
          found_terms: foundTerms
        },
        estimates: {
          estimated_total_size_mb: Math.round((stats.avg_content_length * stats.total_records) / (1024 * 1024)),
          analysis_coverage: Math.round((stats.sample_size / stats.total_records) * 100)
        },
        recommendations: [
          '知识库规模较大，建议进行结构化处理',
          '发现多个专业术语，适合AI深度分析',
          '内容丰富，建议建立术语索引',
          '可以进行分类优化和搜索优化'
        ],
        analysis_timestamp: new Date().toISOString(),
        note: `基于${stats.sample_size}个样本的超快速分析，总计${stats.total_records}个文件`
      }
    };
    
  } catch (error) {
    console.error('超快速分析失败:', error);
    throw error;
  }
}

/**
 * 获取基础统计信息
 */
async function getBasicStats() {
  console.log('获取基础统计信息...');
  
  try {
    // 只获取最基本的信息
    const totalCount = await db.collection('knowledge_base').count();
    
    // 获取第一条记录作为示例
    const firstRecord = await db.collection('knowledge_base')
      .field({
        title: true,
        category: true,
        author: true,
        content: true
      })
      .limit(1)
      .get();
    
    const sample = firstRecord.data[0] || {};
    
    return {
      success: true,
      stats: {
        total_count: totalCount.total,
        sample_record: {
          title: sample.title || '无标题',
          category: sample.category || '未分类',
          author: sample.author || '未知作者',
          content_length: (sample.content || '').length,
          content_preview: (sample.content || '').substring(0, 100) + '...'
        },
        database_status: '正常',
        last_check: new Date().toISOString()
      }
    };
    
  } catch (error) {
    console.error('基础统计失败:', error);
    throw error;
  }
}
