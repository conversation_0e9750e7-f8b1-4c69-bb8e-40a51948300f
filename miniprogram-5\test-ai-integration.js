// AI深度集成测试脚本
// 测试优化后的知识库检索和AI分析功能

const { 
  getRelevantKnowledge, 
  buildEnhancedKeywords,
  performSemanticSearch,
  KNOWLEDGE_CONFIG 
} = require('./miniprogram/utils/knowledge-base.js');

const {
  callDeepSeekAPI,
  analyzeMeihuaWithAI,
  analyzeYijingWithAI,
  analyzeBaziWithAI,
  analyzeZiweiWithAI,
  detectQuestionType
} = require('./miniprogram/utils/ai-service.js');

/**
 * 测试知识库检索优化
 */
async function testKnowledgeRetrieval() {
  console.log('\n=== 测试知识库检索优化 ===');
  
  const testCases = [
    {
      questionType: '财运',
      query: '投资股票什么时候能赚钱',
      context: { hexagram: { name: '天地否', method: 'yijing' } }
    },
    {
      questionType: '婚姻',
      query: '什么时候能结婚',
      context: { bazi: { dayMaster: '甲木' } }
    },
    {
      questionType: '事业',
      query: '升职机会如何',
      context: { ziwei: { mingGong: '紫微星' } }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n--- 测试案例: ${testCase.questionType} ---`);
    console.log(`问题: ${testCase.query}`);
    
    try {
      // 测试增强关键词构建
      const enhancedKeywords = buildEnhancedKeywords(
        testCase.questionType, 
        testCase.query, 
        testCase.context
      );
      console.log(`增强关键词: ${enhancedKeywords}`);
      
      // 测试知识库检索
      const relevantKnowledge = await getRelevantKnowledge(
        testCase.questionType,
        testCase.query,
        testCase.context
      );
      
      console.log(`检索到 ${relevantKnowledge.length} 条相关知识`);
      relevantKnowledge.forEach((knowledge, index) => {
        console.log(`  ${index + 1}. 《${knowledge.title}》- 相关性: ${knowledge.semanticScore || 'N/A'}`);
      });
      
    } catch (error) {
      console.error(`测试失败: ${error.message}`);
    }
  }
}

/**
 * 测试AI分析优化
 */
async function testAIAnalysis() {
  console.log('\n=== 测试AI分析优化 ===');
  
  const testCases = [
    {
      type: 'meihua',
      question: '投资房产能否获利',
      context: {
        hexagram: {
          name: '山天大畜',
          upper: '艮',
          lower: '乾',
          changingLines: ['六二'],
          method: 'meihua',
          date: '2024年1月15日'
        }
      }
    },
    {
      type: 'bazi',
      question: '今年事业运势如何',
      context: {
        bazi: {
          year: '甲辰',
          month: '丙寅',
          day: '戊申',
          hour: '壬子',
          dayMaster: '戊土',
          pattern: '身强用官'
        }
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n--- 测试 ${testCase.type} 模块 ---`);
    console.log(`问题: ${testCase.question}`);
    
    try {
      let result;
      switch (testCase.type) {
        case 'meihua':
          result = await analyzeMeihuaWithAI(testCase.question, testCase.context.hexagram);
          break;
        case 'bazi':
          result = await analyzeBaziWithAI(testCase.question, testCase.context.bazi);
          break;
        default:
          console.log('未知测试类型');
          continue;
      }
      
      console.log('AI分析结果:');
      console.log(result.substring(0, 200) + '...');
      
      // 验证结果质量
      const hasTimePredict = /\d{4}年.*?\d{1,2}月.*?\d{1,2}日/.test(result);
      const hasAncientRef = result.includes('古籍') || result.includes('依据');
      const hasAdvice = result.includes('建议') || result.includes('化解');
      
      console.log('质量检查:');
      console.log(`  时间预测: ${hasTimePredict ? '✓' : '✗'}`);
      console.log(`  古籍依据: ${hasAncientRef ? '✓' : '✗'}`);
      console.log(`  实用建议: ${hasAdvice ? '✓' : '✗'}`);
      
    } catch (error) {
      console.error(`AI分析测试失败: ${error.message}`);
    }
  }
}

/**
 * 测试问题类型检测
 */
function testQuestionTypeDetection() {
  console.log('\n=== 测试问题类型检测 ===');
  
  const testQuestions = [
    '我什么时候能发财',
    '投资股票能赚钱吗',
    '今年升职机会如何',
    '什么时候能结婚',
    '身体健康状况怎样',
    '考试能否通过'
  ];

  testQuestions.forEach(question => {
    const questionType = detectQuestionType(question);
    console.log(`"${question}" -> ${questionType}`);
  });
}

/**
 * 测试专业术语匹配
 */
function testProfessionalTerms() {
  console.log('\n=== 测试专业术语匹配 ===');
  
  const categories = Object.keys(KNOWLEDGE_CONFIG.professionalTerms);
  categories.forEach(category => {
    const terms = KNOWLEDGE_CONFIG.professionalTerms[category];
    console.log(`${category}: ${terms.join(', ')}`);
  });
  
  const questionKeywords = Object.keys(KNOWLEDGE_CONFIG.questionKeywords);
  questionKeywords.forEach(type => {
    const keywords = KNOWLEDGE_CONFIG.questionKeywords[type];
    console.log(`${type}问题关键词: ${keywords.join(', ')}`);
  });
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始AI深度集成测试...\n');
  
  try {
    // 测试问题类型检测
    testQuestionTypeDetection();
    
    // 测试专业术语匹配
    testProfessionalTerms();
    
    // 测试知识库检索（需要云开发环境）
    console.log('\n⚠️  知识库检索测试需要在小程序环境中运行');
    
    // 测试AI分析（需要网络连接）
    console.log('\n⚠️  AI分析测试需要网络连接和API密钥');
    
    console.log('\n✅ 基础功能测试完成');
    console.log('\n📋 测试总结:');
    console.log('1. 问题类型检测 - 正常');
    console.log('2. 专业术语配置 - 正常');
    console.log('3. 知识库检索 - 需要云环境测试');
    console.log('4. AI分析功能 - 需要网络环境测试');
    
  } catch (error) {
    console.error('测试过程中出现错误:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests();
}

module.exports = {
  testKnowledgeRetrieval,
  testAIAnalysis,
  testQuestionTypeDetection,
  testProfessionalTerms,
  runTests
};
