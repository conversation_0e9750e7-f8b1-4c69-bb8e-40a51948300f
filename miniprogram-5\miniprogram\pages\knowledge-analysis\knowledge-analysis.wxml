<!--知识库分析页面-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">知识库深度分析</text>
    <text class="subtitle">分析云端知识库的结构和内容特征</text>
  </view>

  <!-- 分析状态卡片 -->
  <view class="status-card">
    <view class="status-header">
      <text class="status-title">分析状态</text>
      <text class="status-value {{analysisStatus === '分析完成' ? 'success' : analysisStatus === '分析失败' ? 'error' : 'info'}}">
        {{analysisStatus}}
      </text>
    </view>

    <view class="progress-section" wx:if="{{isAnalyzing || progress > 0}}">
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{progress}}%"></view>
      </view>
      <text class="progress-text">{{progress}}%</text>
    </view>

    <view class="current-step" wx:if="{{currentStep}}">
      <text class="step-text">{{currentStep}}</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button
      class="btn-primary"
      bindtap="startFullAnalysis"
      disabled="{{isAnalyzing}}"
    >
      {{isAnalyzing && analysisStatus === '分析中' ? '分析中...' : '开始完整分析'}}
    </button>

    <button
      class="btn-structurize"
      bindtap="startStructurization"
      disabled="{{isAnalyzing}}"
    >
      {{isAnalyzing && analysisStatus === '结构化处理中' ? '结构化中...' : '🔧 结构化处理'}}
    </button>

    <button
      class="btn-secondary"
      bindtap="restartAnalysis"
      disabled="{{isAnalyzing}}"
    >
      重新开始
    </button>
  </view>

  <!-- 分析结果概览 -->
  <view class="results-overview" wx:if="{{summaryData}}">
    <view class="section-title">分析结果概览</view>
    
    <view class="overview-grid">
      <view class="overview-item">
        <text class="overview-label">总记录数</text>
        <text class="overview-value">{{summaryData.overview.total_records}}</text>
      </view>
      <view class="overview-item">
        <text class="overview-label">分类数量</text>
        <text class="overview-value">{{summaryData.overview.categories}}</text>
      </view>
      <view class="overview-item">
        <text class="overview-label">作者数量</text>
        <text class="overview-value">{{summaryData.overview.authors}}</text>
      </view>
      <view class="overview-item">
        <text class="overview-label">总大小(MB)</text>
        <text class="overview-value">{{summaryData.overview.total_size_mb}}</text>
      </view>
    </view>
  </view>

  <!-- 详细分析结果 -->
  <view class="detailed-results" wx:if="{{structureData || keywordsData || contentData || terminologyData}}">
    <view class="section-title">详细分析结果</view>
    
    <view class="result-cards">
      <!-- 结构分析 -->
      <view class="result-card" wx:if="{{structureData}}" bindtap="viewDetailedResults" data-type="structure">
        <view class="card-header">
          <text class="card-title">📊 结构分析</text>
          <text class="card-status">完成</text>
        </view>
        <view class="card-content">
          <text class="card-desc">分析知识库的整体结构和数据分布</text>
          <view class="card-stats">
            <text class="stat-item">记录: {{structureData.total_records}}</text>
            <text class="stat-item">平均大小: {{structureData.size_statistics.avgSize}}字节</text>
          </view>
        </view>
      </view>

      <!-- 关键词分析 -->
      <view class="result-card" wx:if="{{keywordsData}}" bindtap="viewDetailedResults" data-type="keywords">
        <view class="card-header">
          <text class="card-title">🔍 关键词分析</text>
          <text class="card-status">完成</text>
        </view>
        <view class="card-content">
          <text class="card-desc">提取和分析重要术语和概念</text>
          <view class="card-stats">
            <text class="stat-item">常用术语: {{keywordsData.common_terms.length}}</text>
            <text class="stat-item">分析记录: {{keywordsData.total_analyzed}}</text>
          </view>
        </view>
      </view>

      <!-- 内容分析 -->
      <view class="result-card" wx:if="{{contentData}}" bindtap="viewDetailedResults" data-type="content">
        <view class="card-header">
          <text class="card-title">📚 内容分析</text>
          <text class="card-status">完成</text>
        </view>
        <view class="card-content">
          <text class="card-desc">分析内容特征和分类模式</text>
          <view class="card-stats">
            <text class="stat-item">样本数: {{contentData.sample_size}}</text>
            <text class="stat-item">短文档: {{contentData.length_distribution.short}}</text>
          </view>
        </view>
      </view>

      <!-- 术语分析 -->
      <view class="result-card" wx:if="{{terminologyData}}" bindtap="viewDetailedResults" data-type="terminology">
        <view class="card-header">
          <text class="card-title">📖 术语分析</text>
          <text class="card-status">完成</text>
        </view>
        <view class="card-content">
          <text class="card-desc">分析专业术语的覆盖度和使用频率</text>
          <view class="card-stats">
            <text class="stat-item">词典大小: {{terminologyData.dictionary_size}}</text>
            <text class="stat-item">样本数: {{terminologyData.sample_size}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 优化建议 -->
  <view class="recommendations" wx:if="{{summaryData && summaryData.recommendations}}">
    <view class="section-title">优化建议</view>
    <view class="recommendation-list">
      <view class="recommendation-item" wx:for="{{summaryData.recommendations}}" wx:key="index">
        <text class="recommendation-text">{{index + 1}}. {{item}}</text>
      </view>
    </view>
  </view>

  <!-- 操作区域 -->
  <view class="actions" wx:if="{{summaryData}}">
    <button class="btn-outline" bindtap="exportResults">导出结果</button>
  </view>

  <!-- 日志区域 -->
  <view class="logs-section">
    <view class="logs-header">
      <text class="logs-title">分析日志</text>
      <button class="btn-clear" bindtap="clearLogs" size="mini">清除</button>
    </view>
    
    <scroll-view class="logs-container" scroll-y="true">
      <view class="log-item {{item.type}}" wx:for="{{logs}}" wx:key="index">
        <text class="log-time">{{item.time}}</text>
        <text class="log-message">{{item.message}}</text>
      </view>
      
      <view class="empty-logs" wx:if="{{logs.length === 0}}">
        <text class="empty-text">暂无日志</text>
      </view>
    </scroll-view>
  </view>
</view>
