# 术语词典数据 - 云开发控制台导入

## 📋 操作步骤

1. **打开微信开发者工具**
2. **点击工具栏的"云开发"按钮**
3. **进入云开发控制台**
4. **选择"数据库"选项卡**
5. **点击"创建集合"**
6. **输入集合名称：`terminology_dictionary`**
7. **点击确定创建集合**
8. **进入新创建的集合**
9. **点击"添加记录"**
10. **复制下方JSON数据并粘贴**
11. **点击确定保存**

## 📊 JSON数据（直接复制使用）

```json
{
  "version": "1.0",
  "created_at": "2024-12-29T07:36:00.000Z",
  "categories": {
    "liuyao": [
      "世爻",
      "应爻",
      "用神",
      "原神",
      "忌神",
      "仇神",
      "妻财爻",
      "官鬼爻",
      "父母爻",
      "兄弟爻",
      "子孙爻",
      "动爻",
      "静爻",
      "变爻",
      "飞神",
      "伏神",
      "月建",
      "日辰",
      "旬空",
      "月破",
      "暗动",
      "进神",
      "退神",
      "反吟",
      "伏吟",
      "三合",
      "六合",
      "六冲",
      "三刑",
      "自刑",
      "墓库"
    ],
    "meihua": [
      "体卦",
      "用卦",
      "互卦",
      "变卦",
      "本卦",
      "上卦",
      "下卦",
      "动爻",
      "静爻",
      "体用",
      "生克",
      "比和",
      "内卦",
      "外卦",
      "主卦",
      "客卦",
      "时间起卦",
      "数字起卦",
      "方位起卦",
      "声音起卦",
      "颜色起卦",
      "字数起卦"
    ],
    "bazi": [
      "日主",
      "日元",
      "日干",
      "用神",
      "喜神",
      "忌神",
      "仇神",
      "闲神",
      "十神",
      "正官",
      "偏官",
      "正财",
      "偏财",
      "食神",
      "伤官",
      "比肩",
      "劫财",
      "正印",
      "偏印",
      "大运",
      "流年",
      "月令",
      "格局",
      "身强",
      "身弱",
      "从格",
      "化格",
      "专旺格",
      "十二长生"
    ],
    "ziwei": [
      "命宫",
      "身宫",
      "兄弟宫",
      "夫妻宫",
      "子女宫",
      "财帛宫",
      "疾厄宫",
      "迁移宫",
      "奴仆宫",
      "官禄宫",
      "田宅宫",
      "福德宫",
      "父母宫",
      "十四主星",
      "紫微",
      "天机",
      "太阳",
      "武曲",
      "天同",
      "廉贞",
      "天府",
      "太阴",
      "贪狼",
      "巨门",
      "天相",
      "天梁",
      "七杀",
      "破军",
      "四化",
      "化禄",
      "化权",
      "化科",
      "化忌",
      "大限",
      "小限"
    ]
  },
  "description": "传统占卜学专业术语词典，包含六爻、梅花易数、八字、紫微斗数四大体系的核心术语"
}
```

## ✅ 验证步骤

完成导入后，请验证：

1. **集合名称**：`terminology_dictionary`
2. **记录数量**：1条记录
3. **version字段**：值为 `"1.0"`
4. **categories字段**：包含4个分类（liuyao, meihua, bazi, ziwei）
5. **术语总数**：约100+个专业术语

## 🔄 完成后操作

1. **返回微信小程序**
2. **进入AI验证页面**
3. **点击"🏠 本地化修复（推荐）"**
4. **验证术语词典状态**
5. **确认显示"✅ 术语词典已存在"**

## ⚠️ 注意事项

- 确保JSON格式完全正确，不要有语法错误
- 集合名称必须完全匹配：`terminology_dictionary`
- version字段值必须是字符串：`"1.0"`
- 如果粘贴时出现格式问题，请检查引号和逗号

## 📞 技术支持

如果遇到问题：
1. 检查云开发环境ID是否正确
2. 确认有数据库操作权限
3. 重新检查JSON格式
4. 尝试重新创建集合
