{"name": "marked", "description": "A markdown parser built for speed", "author": "<PERSON>", "version": "4.0.19", "type": "module", "main": "./lib/marked.cjs", "module": "./lib/marked.esm.js", "browser": "./lib/marked.umd.js", "bin": {"marked": "bin/marked.js"}, "man": "./man/marked.1", "files": ["bin/", "lib/", "src/", "man/", "marked.min.js"], "exports": {".": {"import": "./lib/marked.esm.js", "default": "./lib/marked.cjs"}, "./package.json": "./package.json"}, "repository": "git://github.com/markedjs/marked.git", "homepage": "https://marked.js.org", "bugs": {"url": "http://github.com/markedjs/marked/issues"}, "license": "MIT", "keywords": ["markdown", "markup", "html"], "tags": ["markdown", "markup", "html"], "devDependencies": {"@babel/core": "^7.18.10", "@babel/preset-env": "^7.18.10", "@markedjs/html-differ": "^4.0.2", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^22.0.2", "@semantic-release/commit-analyzer": "^9.0.2", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^8.0.5", "@semantic-release/npm": "^9.0.1", "@semantic-release/release-notes-generator": "^10.0.3", "cheerio": "^1.0.0-rc.12", "commonmark": "0.30.0", "eslint": "^8.22.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.2.4", "eslint-plugin-promise": "^6.0.0", "front-matter": "^4.0.2", "highlight.js": "^11.6.0", "jasmine": "^4.3.0", "markdown-it": "13.0.1", "node-fetch": "^3.2.10", "rollup": "^2.78.0", "rollup-plugin-license": "^2.8.1", "semantic-release": "^19.0.3", "titleize": "^3.0.0", "uglify-js": "^3.16.3", "vuln-regex-detector": "^1.3.0"}, "scripts": {"test": "jasmine --config=jasmine.json", "test:all": "npm test && npm run test:lint", "test:unit": "npm test -- test/unit/**/*-spec.js", "test:specs": "npm test -- test/specs/**/*-spec.js", "test:lint": "eslint .", "test:redos": "node test/vuln-regex.js", "test:update": "node test/update-specs.js", "rules": "node test/rules.js", "bench": "npm run rollup && node test/bench.js", "lint": "eslint --fix .", "build:reset": "git checkout upstream/master lib/marked.cjs lib/marked.umd.js lib/marked.esm.js marked.min.js", "build": "npm run rollup && npm run minify", "build:docs": "node build-docs.js", "rollup": "npm run rollup:umd && npm run rollup:esm", "rollup:umd": "rollup -c rollup.config.js", "rollup:esm": "rollup -c rollup.config.esm.js", "minify": "uglifyjs lib/marked.umd.js -cm  --comments /Copyright/ -o marked.min.js", "preversion": "npm run build && (git diff --quiet || git commit -am build)"}, "engines": {"node": ">= 12"}}