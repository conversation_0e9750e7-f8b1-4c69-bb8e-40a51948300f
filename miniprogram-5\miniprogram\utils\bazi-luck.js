// 八字大运流年系统
// 基于传统子平八字理论

import { HEAVENLY_STEMS, EARTHLY_BRANCHES, YIN_YANG } from './bazi-calculator.js';

// 二十四节气对应的月份和大致日期（用于起运计算）
const SOLAR_TERMS = {
  1: { name: '立春', approxDay: 4 },
  2: { name: '惊蛰', approxDay: 5 },
  3: { name: '清明', approxDay: 5 },
  4: { name: '立夏', approxDay: 5 },
  5: { name: '芒种', approxDay: 6 },
  6: { name: '小暑', approxDay: 7 },
  7: { name: '立秋', approxDay: 7 },
  8: { name: '白露', approxDay: 8 },
  9: { name: '寒露', approxDay: 8 },
  10: { name: '立冬', approxDay: 7 },
  11: { name: '大雪', approxDay: 7 },
  12: { name: '小寒', approxDay: 6 }
};

/**
 * 计算起运时间
 * @param {Object} bazi - 八字信息
 * @param {boolean} isMale - 是否为男性
 * @returns {Object} 起运信息
 */
export function calculateStartLuck(bazi, isMale) {
  const birthYear = bazi.birthDate.getFullYear();
  const birthMonth = bazi.birthDate.getMonth() + 1; // JavaScript月份从0开始
  const birthDay = bazi.birthDate.getDate();
  const yearStem = bazi.year.stem;
  const yearYinYang = YIN_YANG[yearStem];

  // 阳男阴女顺行，阴男阳女逆行
  const isForward = (isMale && yearYinYang === '阳') || (!isMale && yearYinYang === '阴');

  // 计算到下一个节气的天数（精确计算）
  const nextSolarTerm = getNextSolarTerm(birthYear, birthMonth, birthDay);
  const daysDiff = calculateDaysDifference(bazi.birthDate, nextSolarTerm);

  // 按照传统算法：三天为一年，一天为四个月，一个时辰为十天
  const startAgeYears = Math.floor(daysDiff / 3);
  const remainingDays = daysDiff % 3;
  const startAgeMonths = Math.floor(remainingDays * 4);

  const startAge = startAgeYears + (startAgeMonths / 12);
  const startYear = birthYear + startAgeYears;

  return {
    startAge: Math.round(startAge * 10) / 10, // 保留一位小数
    startAgeYears: startAgeYears,
    startAgeMonths: startAgeMonths,
    startYear: startYear,
    direction: isForward ? '顺行' : '逆行',
    isForward: isForward,
    daysDiff: daysDiff,
    nextSolarTerm: nextSolarTerm
  };
}

/**
 * 获取下一个节气日期
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} day - 日期
 * @returns {Date} 下一个节气日期
 */
function getNextSolarTerm(year, month, day) {
  // 简化算法：找到当月或下月的节气
  let targetMonth = month;
  let targetDay = SOLAR_TERMS[month] ? SOLAR_TERMS[month].approxDay : 5;

  // 如果已经过了当月节气，则取下月节气
  if (day >= targetDay) {
    targetMonth = month === 12 ? 1 : month + 1;
    if (targetMonth === 1) {
      year += 1; // 跨年
    }
    targetDay = SOLAR_TERMS[targetMonth] ? SOLAR_TERMS[targetMonth].approxDay : 5;
  }

  return new Date(year, targetMonth - 1, targetDay); // JavaScript月份从0开始
}

/**
 * 计算两个日期之间的天数差
 * @param {Date} startDate - 开始日期
 * @param {Date} endDate - 结束日期
 * @returns {number} 天数差
 */
function calculateDaysDifference(startDate, endDate) {
  const timeDiff = endDate.getTime() - startDate.getTime();
  return Math.ceil(timeDiff / (1000 * 3600 * 24));
}

/**
 * 计算大运
 * @param {Object} bazi - 八字信息
 * @param {Object} startLuck - 起运信息
 * @param {number} periods - 计算几个大运周期，默认8个
 * @returns {Array} 大运数组
 */
export function calculateDayun(bazi, startLuck, periods = 8) {
  const monthStem = bazi.month.stem;
  const monthBranch = bazi.month.branch;
  const isForward = startLuck.isForward;
  
  const stemIndex = HEAVENLY_STEMS.indexOf(monthStem);
  const branchIndex = EARTHLY_BRANCHES.indexOf(monthBranch);
  
  const dayunList = [];
  
  for (let i = 0; i < periods; i++) {
    let currentStemIndex, currentBranchIndex;
    
    if (isForward) {
      // 顺行：天干地支都向后推
      currentStemIndex = (stemIndex + i + 1) % 10;
      currentBranchIndex = (branchIndex + i + 1) % 12;
    } else {
      // 逆行：天干地支都向前推
      currentStemIndex = (stemIndex - i - 1 + 10) % 10;
      currentBranchIndex = (branchIndex - i - 1 + 12) % 12;
    }
    
    // 大运从起运开始，每10年一个周期
    const startAge = startLuck.startAge + i * 10; // 从起运年龄开始计算
    const endAge = startAge + 9;
    const startYear = startLuck.startYear + i * 10;
    const endYear = startYear + 9;
    
    dayunList.push({
      period: i + 1,
      stem: HEAVENLY_STEMS[currentStemIndex],
      branch: EARTHLY_BRANCHES[currentBranchIndex],
      startAge: startAge,
      endAge: endAge,
      startYear: startYear,
      endYear: endYear,
      ganzhi: `${HEAVENLY_STEMS[currentStemIndex]}${EARTHLY_BRANCHES[currentBranchIndex]}`
    });
  }
  
  return dayunList;
}

/**
 * 计算流年
 * @param {number} startYear - 开始年份
 * @param {number} years - 计算多少年，默认10年
 * @returns {Array} 流年数组
 */
export function calculateLiunian(startYear, years = 10) {
  const liunianList = [];
  
  for (let i = 0; i < years; i++) {
    const year = startYear + i;
    
    // 计算该年的天干地支
    const stemIndex = (year - 4) % 10; // 甲子年为公元4年
    const branchIndex = (year - 4) % 12;
    
    liunianList.push({
      year: year,
      stem: HEAVENLY_STEMS[stemIndex],
      branch: EARTHLY_BRANCHES[branchIndex],
      ganzhi: `${HEAVENLY_STEMS[stemIndex]}${EARTHLY_BRANCHES[branchIndex]}`
    });
  }
  
  return liunianList;
}

/**
 * 分析大运流年与命局的关系
 * @param {Object} bazi - 八字信息
 * @param {Object} dayun - 大运信息
 * @param {Object} liunian - 流年信息
 * @param {Object} useGodAnalysis - 用神分析
 * @returns {Object} 大运流年分析
 */
export function analyzeDayunLiunian(bazi, dayun, liunian, useGodAnalysis) {
  const analysis = {
    dayun: analyzeSinglePeriod(bazi, dayun, useGodAnalysis, '大运'),
    liunian: analyzeSinglePeriod(bazi, liunian, useGodAnalysis, '流年'),
    combined: ''
  };
  
  // 大运流年组合分析
  const dayunGood = analysis.dayun.isGood;
  const liunianGood = analysis.liunian.isGood;
  
  if (dayunGood && liunianGood) {
    analysis.combined = '大运流年皆吉，运势极佳，宜积极进取';
  } else if (dayunGood && !liunianGood) {
    analysis.combined = '大运吉而流年不利，整体尚可，需要谨慎';
  } else if (!dayunGood && liunianGood) {
    analysis.combined = '大运不利但流年较好，可有小的转机';
  } else {
    analysis.combined = '大运流年皆不利，需要谨慎保守，避免冒险';
  }
  
  return analysis;
}

/**
 * 分析单个时期（大运或流年）
 * @param {Object} bazi - 八字信息
 * @param {Object} period - 时期信息（大运或流年）
 * @param {Object} useGodAnalysis - 用神分析
 * @param {string} type - 类型（'大运'或'流年'）
 * @returns {Object} 分析结果
 */
function analyzeSinglePeriod(bazi, period, useGodAnalysis, type) {
  const periodStem = period.stem;
  const periodBranch = period.branch;
  const useGod = useGodAnalysis.useGod;
  const avoidGod = useGodAnalysis.avoidGod;
  
  let score = 0;
  let analysis = [];
  
  // 分析天干
  const stemElement = getElementFromStem(periodStem);
  if (useGod.includes(stemElement)) {
    score += 2;
    analysis.push(`${type}天干${periodStem}为用神，大吉`);
  } else if (avoidGod.includes(stemElement)) {
    score -= 2;
    analysis.push(`${type}天干${periodStem}为忌神，不利`);
  }
  
  // 分析地支
  const branchElement = getElementFromBranch(periodBranch);
  if (useGod.includes(branchElement)) {
    score += 1;
    analysis.push(`${type}地支${periodBranch}为用神，有利`);
  } else if (avoidGod.includes(branchElement)) {
    score -= 1;
    analysis.push(`${type}地支${periodBranch}为忌神，不利`);
  }
  
  // 与日主的关系
  const dayMaster = bazi.dayMaster;
  const relation = analyzeRelation(dayMaster, periodStem);
  analysis.push(`与日主关系：${relation}`);
  
  return {
    score: score,
    isGood: score > 0,
    analysis: analysis,
    ganzhi: `${periodStem}${periodBranch}`
  };
}

/**
 * 分析两个天干的关系
 * @param {string} stem1 - 天干1
 * @param {string} stem2 - 天干2
 * @returns {string} 关系描述
 */
function analyzeRelation(stem1, stem2) {
  // 简化的关系分析
  if (stem1 === stem2) {
    return '比肩，同类相助';
  }
  
  // 这里应该有更详细的天干关系分析
  // 包括合化、冲克等关系
  return '需要具体分析';
}

/**
 * 获取天干对应的五行
 */
function getElementFromStem(stem) {
  const elementMap = {
    '甲': '木', '乙': '木',
    '丙': '火', '丁': '火',
    '戊': '土', '己': '土',
    '庚': '金', '辛': '金',
    '壬': '水', '癸': '水'
  };
  return elementMap[stem];
}

/**
 * 获取地支对应的五行
 */
function getElementFromBranch(branch) {
  const elementMap = {
    '子': '水', '亥': '水',
    '寅': '木', '卯': '木',
    '巳': '火', '午': '火',
    '申': '金', '酉': '金',
    '辰': '土', '戌': '土', '丑': '土', '未': '土'
  };
  return elementMap[branch];
}

/**
 * 获取当前大运
 * @param {Array} dayunList - 大运列表
 * @param {number} currentAge - 当前年龄
 * @returns {Object} 当前大运
 */
export function getCurrentDayun(dayunList, currentAge) {
  return dayunList.find(dayun => 
    currentAge >= dayun.startAge && currentAge <= dayun.endAge
  );
}

/**
 * 获取当前流年
 * @param {number} currentYear - 当前年份
 * @returns {Object} 当前流年
 */
export function getCurrentLiunian(currentYear) {
  const stemIndex = (currentYear - 4) % 10;
  const branchIndex = (currentYear - 4) % 12;
  
  return {
    year: currentYear,
    stem: HEAVENLY_STEMS[stemIndex],
    branch: EARTHLY_BRANCHES[branchIndex],
    ganzhi: `${HEAVENLY_STEMS[stemIndex]}${EARTHLY_BRANCHES[branchIndex]}`
  };
}

/**
 * 预测未来运势
 * @param {Object} bazi - 八字信息
 * @param {boolean} isMale - 是否为男性
 * @param {number} currentAge - 当前年龄
 * @param {number} years - 预测年数
 * @returns {Object} 运势预测
 */
export function predictFortune(bazi, isMale, currentAge, years = 5) {
  const startLuck = calculateStartLuck(bazi, isMale);
  const dayunList = calculateDayun(bazi, startLuck);
  const currentYear = new Date().getFullYear();
  const liunianList = calculateLiunian(currentYear, years);
  
  const predictions = [];
  
  liunianList.forEach(liunian => {
    const age = currentAge + (liunian.year - currentYear);
    const currentDayun = getCurrentDayun(dayunList, age);
    
    if (currentDayun) {
      // 这里需要用神分析，暂时简化
      const useGodAnalysis = { useGod: ['木'], avoidGod: ['金'] }; // 简化
      const analysis = analyzeDayunLiunian(bazi, currentDayun, liunian, useGodAnalysis);
      
      predictions.push({
        year: liunian.year,
        age: age,
        dayun: currentDayun.ganzhi,
        liunian: liunian.ganzhi,
        analysis: analysis.combined,
        score: analysis.dayun.score + analysis.liunian.score
      });
    }
  });
  
  return {
    predictions: predictions,
    dayunList: dayunList,
    currentDayun: getCurrentDayun(dayunList, currentAge)
  };
}
