<!--pages/ai-verification/ai-verification.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">🤖 AI分析准确性验证</text>
    <text class="subtitle">确保每个信息、建议、推断都精准无误</text>
  </view>

  <!-- 验证状态 -->
  <view class="status-section">
    <view class="status-card">
      <view class="status-header">
        <text class="status-title">验证状态</text>
        <text class="status-value {{verificationStatus === '验证完成' ? 'success' : verificationStatus === '验证失败' ? 'error' : 'processing'}}">
          {{verificationStatus}}
        </text>
      </view>
      
      <view class="progress-container" wx:if="{{isVerifying}}">
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progress}}%"></view>
        </view>
        <text class="progress-text">{{progress}}%</text>
      </view>
      
      <text class="current-step" wx:if="{{currentStep}}">{{currentStep}}</text>
    </view>
  </view>

  <!-- 测试参数 -->
  <view class="test-params">
    <view class="param-card">
      <view class="param-header">
        <text class="param-title">测试参数</text>
        <button class="change-btn" bindtap="changeTestParams" disabled="{{isVerifying}}">切换</button>
      </view>
      
      <view class="param-item">
        <text class="param-label">占卜类型：</text>
        <text class="param-value">{{testAnalysisType === 'liuyao' ? '六爻占卜' : testAnalysisType === 'meihua' ? '梅花易数' : testAnalysisType === 'bazi' ? '子平八字' : '紫微斗数'}}</text>
      </view>
      
      <view class="param-item">
        <text class="param-label">测试问题：</text>
        <text class="param-value">{{testQuestion}}</text>
      </view>
      
      <view class="param-item">
        <text class="param-label">分析内容：</text>
        <text class="param-value">{{testAnalysisContent}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button 
      class="verify-btn {{isVerifying ? 'disabled' : ''}}" 
      bindtap="startFullVerification"
      disabled="{{isVerifying}}"
    >
      {{isVerifying ? '验证中...' : '🚀 开始完整验证'}}
    </button>
    
    <button
      class="detail-btn"
      bindtap="viewDetailedResults"
      disabled="{{!verificationResults.success}}"
    >
      📊 查看详细结果
    </button>
  </view>

  <!-- 验证结果概览 -->
  <view class="results-section" wx:if="{{verificationResults.analysis_verification}}">
    <view class="results-card">
      <text class="results-title">AI分析验证结果</text>

      <view class="score-grid">
        <view class="score-item">
          <text class="score-label">准确性</text>
          <text class="score-value {{verificationResults.analysis_verification.accuracy_score >= 8 ? 'excellent' : verificationResults.analysis_verification.accuracy_score >= 6 ? 'good' : 'poor'}}">
            {{verificationResults.analysis_verification.accuracy_score}}/10
          </text>
        </view>

        <view class="score-item">
          <text class="score-label">知识库符合度</text>
          <text class="score-value {{verificationResults.analysis_verification.knowledge_compliance >= 8 ? 'excellent' : verificationResults.analysis_verification.knowledge_compliance >= 6 ? 'good' : 'poor'}}">
            {{verificationResults.analysis_verification.knowledge_compliance}}/10
          </text>
        </view>

        <view class="score-item">
          <text class="score-label">术语正确性</text>
          <text class="score-value {{verificationResults.analysis_verification.terminology_correctness >= 8 ? 'excellent' : verificationResults.analysis_verification.terminology_correctness >= 6 ? 'good' : 'poor'}}">
            {{verificationResults.analysis_verification.terminology_correctness}}/10
          </text>
        </view>

        <view class="score-item">
          <text class="score-label">预测具体性</text>
          <text class="score-value {{verificationResults.analysis_verification.prediction_specificity >= 8 ? 'excellent' : verificationResults.analysis_verification.prediction_specificity >= 6 ? 'good' : 'poor'}}">
            {{verificationResults.analysis_verification.prediction_specificity}}/10
          </text>
        </view>
      </view>
    </view>
  </view>

  <!-- 集成状态 -->
  <view class="integration-section" wx:if="{{integrationStatus.overall_readiness}}">
    <view class="integration-card">
      <text class="integration-title">系统集成状态</text>
      
      <view class="integration-item">
        <text class="integration-label">知识库：</text>
        <text class="integration-value {{integrationStatus.knowledge_base.status === 'ready' ? 'ready' : 'not-ready'}}">
          {{integrationStatus.knowledge_base.count}}条记录 ({{integrationStatus.knowledge_base.status}})
        </text>
      </view>
      
      <view class="integration-item">
        <text class="integration-label">术语词典：</text>
        <text class="integration-value {{integrationStatus.terminology_dictionary.status === 'ready' ? 'ready' : 'not-ready'}}">
          {{integrationStatus.terminology_dictionary.count}}条记录 ({{integrationStatus.terminology_dictionary.status}})
        </text>
      </view>
      
      <view class="integration-item">
        <text class="integration-label">DeepSeek API：</text>
        <text class="integration-value {{integrationStatus.deepseek_api.status === 'connected' ? 'ready' : 'not-ready'}}">
          {{integrationStatus.deepseek_api.status}}
        </text>
      </view>
      
      <view class="integration-item">
        <text class="integration-label">整体就绪：</text>
        <text class="integration-value {{integrationStatus.overall_readiness === 'ready' ? 'ready' : 'not-ready'}}">
          {{integrationStatus.overall_readiness}}
        </text>
      </view>
    </view>
  </view>

  <!-- 日志区域 -->
  <view class="logs-section">
    <view class="logs-header">
      <text class="logs-title">验证日志</text>
      <button class="clear-btn" bindtap="clearLogs">清空</button>
    </view>
    
    <scroll-view class="logs-container" scroll-y="true">
      <view class="log-item {{log.type}}" wx:for="{{logs}}" wx:key="time">
        <text class="log-time">[{{log.time}}]</text>
        <text class="log-message">{{log.message}}</text>
      </view>
      
      <view class="log-empty" wx:if="{{logs.length === 0}}">
        <text>暂无日志记录</text>
      </view>
    </scroll-view>
  </view>
</view>
