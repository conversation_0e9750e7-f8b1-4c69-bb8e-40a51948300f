{"name": "linkify-it", "version": "3.0.3", "description": "Links recognition library with FULL unicode support", "keywords": ["linkify", "linkifier", "autolink", "autolinker"], "repository": "markdown-it/linkify-it", "files": ["index.js", "lib/"], "license": "MIT", "scripts": {"lint": "eslint .", "test": "npm run lint && nyc mocha", "coverage": "npm run test && nyc report --reporter html", "report-coveralls": "nyc report --reporter=text-lcov | coveralls", "demo": "npm run lint && node support/build_demo.js", "doc": "node support/build_doc.js", "gh-pages": "npm run demo && npm run doc && shx cp -R doc/ demo/ && gh-pages -d demo -f", "prepublishOnly": "npm run gh-pages"}, "dependencies": {"uc.micro": "^1.0.1"}, "devDependencies": {"ansi": "^0.3.0", "autoprefixer-stylus": "^0.14.0", "benchmark": "^2.1.0", "browserify": "^16.2.3", "coveralls": "^3.0.2", "eslint": "^7.0.0", "gh-pages": "^2.2.0", "mdurl": "^1.0.0", "mocha": "^7.1.2", "ndoc": "^5.0.1", "nyc": "^15.0.1", "pug-cli": "^1.0.0-alpha6", "shelljs": "^0.8.4", "shx": "^0.3.2", "stylus": "~0.54.5", "tlds": "^1.166.0"}}