/* pages/ai-verification/ai-verification.wxss */

.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 验证状态卡片 */
.status-section {
  margin-bottom: 30rpx;
}

.status-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.status-value {
  font-size: 28rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.status-value.success {
  background: #e8f5e8;
  color: #52c41a;
}

.status-value.error {
  background: #fff2f0;
  color: #ff4d4f;
}

.status-value.processing {
  background: #e6f7ff;
  color: #1890ff;
}

.progress-container {
  margin-bottom: 15rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff, #52c41a);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

.current-step {
  font-size: 26rpx;
  color: #1890ff;
}

/* 测试参数 */
.test-params {
  margin-bottom: 30rpx;
}

.param-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.param-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.change-btn {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.param-item {
  margin-bottom: 15rpx;
}

.param-label {
  font-size: 26rpx;
  color: #666;
  font-weight: bold;
}

.param-value {
  font-size: 26rpx;
  color: #333;
  margin-left: 10rpx;
}

/* 操作按钮 */
.action-section {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.verify-btn {
  flex: 2;
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 25rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.verify-btn.disabled {
  background: #d9d9d9;
  color: #999;
}

.detail-btn {
  flex: 1;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 25rpx;
  font-size: 28rpx;
}

.init-btn {
  width: 100%;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 25rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}

.fix-btn {
  width: 100%;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  color: #333;
  border: none;
  border-radius: 25rpx;
  padding: 25rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
  font-weight: bold;
}

.local-fix-btn {
  width: 100%;
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
  border: none;
  border-radius: 25rpx;
  padding: 25rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 15rpx rgba(168, 237, 234, 0.4);
}

/* 验证结果 */
.results-section {
  margin-bottom: 30rpx;
}

.results-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.results-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.score-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.score-item {
  text-align: center;
  padding: 20rpx;
  border-radius: 15rpx;
  background: #f8f9fa;
}

.score-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.score-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
}

.score-value.excellent {
  color: #52c41a;
}

.score-value.good {
  color: #faad14;
}

.score-value.poor {
  color: #ff4d4f;
}

/* 集成状态 */
.integration-section {
  margin-bottom: 30rpx;
}

.integration-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.integration-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.integration-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.integration-label {
  font-size: 26rpx;
  color: #666;
  font-weight: bold;
}

.integration-value {
  font-size: 26rpx;
}

.integration-value.ready {
  color: #52c41a;
}

.integration-value.not-ready {
  color: #ff4d4f;
}

/* 日志区域 */
.logs-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.logs-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.clear-btn {
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 15rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

.logs-container {
  height: 400rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 10rpx;
  padding: 15rpx;
  background: #fafafa;
}

.log-item {
  margin-bottom: 10rpx;
  padding: 8rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.log-item.info {
  background: #e6f7ff;
  border-left: 4rpx solid #1890ff;
}

.log-item.success {
  background: #f6ffed;
  border-left: 4rpx solid #52c41a;
}

.log-item.warning {
  background: #fffbe6;
  border-left: 4rpx solid #faad14;
}

.log-item.error {
  background: #fff2f0;
  border-left: 4rpx solid #ff4d4f;
}

.log-time {
  color: #666;
  margin-right: 10rpx;
}

.log-message {
  color: #333;
}

.log-empty {
  text-align: center;
  color: #999;
  padding: 50rpx;
}
