import { TsConfigOptions } from '.';
/**
 * tsconfig schema which includes "ts-node" options.
 * @allOf [{"$ref": "https://schemastore.azurewebsites.net/schemas/json/tsconfig.json"}]
 */
export interface TsConfigSchema {
    /**
     * ts-node options.  See also: https://github.com/TypeStrong/ts-node#configuration-options
     *
     * ts-node offers TypeScript execution and REPL for node.js, with source map support.
     */
    'ts-node': TsConfigOptions;
}
