// 本地化快速修复 - 不依赖云函数
// 直接在小程序端解决所有问题

const localFix = {
  
  // 一键本地修复
  async fixAllLocal() {
    console.log('🔧 开始本地化修复...');
    
    const results = {
      terminology_fix: await this.createTerminologyLocal(),
      knowledge_test: await this.testKnowledgeLocal(),
      system_check: await this.checkSystemLocal()
    };
    
    console.log('🎯 本地修复结果:', results);
    
    const allSuccess = Object.values(results).every(result => result.success);
    
    return {
      success: allSuccess,
      results: results,
      summary: {
        terminology_status: results.terminology_fix.success ? '已创建' : '创建失败',
        knowledge_status: results.knowledge_test.success ? `${results.knowledge_test.count}条记录` : '查询失败',
        system_status: results.system_check.success ? '系统就绪' : '存在问题',
        overall_status: allSuccess ? '修复完成' : '部分问题'
      }
    };
  },

  // 本地创建术语词典
  async createTerminologyLocal() {
    console.log('📖 本地创建术语词典...');
    
    try {
      const db = wx.cloud.database();
      
      // 先尝试创建集合
      try {
        await db.createCollection('terminology_dictionary');
        console.log('✅ 术语词典集合创建成功');
      } catch (createError) {
        console.log('ℹ️ 集合可能已存在:', createError.message);
      }
      
      // 检查现有数据
      let existing;
      try {
        existing = await db.collection('terminology_dictionary')
          .where({ version: '1.0' })
          .get();
        
        if (existing.data.length > 0) {
          console.log('✅ 术语词典已存在');
          return {
            success: true,
            total_terms: Object.keys(existing.data[0].categories).reduce((sum, key) => 
              sum + existing.data[0].categories[key].length, 0),
            categories: Object.keys(existing.data[0].categories),
            status: 'already_exists'
          };
        }
      } catch (queryError) {
        console.log('查询失败，继续创建:', queryError.message);
      }
      
      // 创建术语数据
      const terminologyData = {
        version: '1.0',
        created_at: new Date().toISOString(),
        categories: {
          'liuyao': [
            '世爻', '应爻', '用神', '原神', '忌神', '仇神',
            '妻财爻', '官鬼爻', '父母爻', '兄弟爻', '子孙爻',
            '动爻', '静爻', '变爻', '飞神', '伏神',
            '月建', '日辰', '旬空', '月破', '暗动',
            '进神', '退神', '反吟', '伏吟', '三合',
            '六合', '六冲', '三刑', '自刑', '墓库'
          ],
          'meihua': [
            '体卦', '用卦', '互卦', '变卦', '本卦',
            '上卦', '下卦', '动爻', '静爻', '体用',
            '生克', '比和', '内卦', '外卦', '主卦',
            '客卦', '时间起卦', '数字起卦', '方位起卦',
            '声音起卦', '颜色起卦', '字数起卦'
          ],
          'bazi': [
            '日主', '日元', '日干', '用神', '喜神',
            '忌神', '仇神', '闲神', '十神', '正官',
            '偏官', '正财', '偏财', '食神', '伤官',
            '比肩', '劫财', '正印', '偏印', '大运',
            '流年', '月令', '格局', '身强', '身弱',
            '从格', '化格', '专旺格', '十二长生'
          ],
          'ziwei': [
            '命宫', '身宫', '兄弟宫', '夫妻宫', '子女宫',
            '财帛宫', '疾厄宫', '迁移宫', '奴仆宫', '官禄宫',
            '田宅宫', '福德宫', '父母宫', '十四主星', '紫微',
            '天机', '太阳', '武曲', '天同', '廉贞',
            '天府', '太阴', '贪狼', '巨门', '天相',
            '天梁', '七杀', '破军', '四化', '化禄',
            '化权', '化科', '化忌', '大限', '小限'
          ]
        },
        description: '传统占卜学专业术语词典，包含六爻、梅花易数、八字、紫微斗数四大体系的核心术语'
      };
      
      // 插入数据
      const addResult = await db.collection('terminology_dictionary').add({
        data: terminologyData
      });
      
      console.log('✅ 术语词典创建成功:', addResult._id);
      
      return {
        success: true,
        id: addResult._id,
        total_terms: Object.values(terminologyData.categories).reduce((sum, terms) => sum + terms.length, 0),
        categories: Object.keys(terminologyData.categories),
        status: 'newly_created'
      };
      
    } catch (error) {
      console.error('❌ 术语词典创建失败:', error);
      return { 
        success: false, 
        error: error.message,
        error_code: error.errCode || 'unknown'
      };
    }
  },

  // 本地测试知识库
  async testKnowledgeLocal() {
    console.log('📚 本地测试知识库...');
    
    try {
      const db = wx.cloud.database();
      
      const knowledgeCount = await db.collection('knowledge_base').count();
      console.log(`📊 知识库记录数: ${knowledgeCount.total}`);
      
      if (knowledgeCount.total > 0) {
        const sampleRecords = await db.collection('knowledge_base')
          .field({ title: true, content: true, category: true })
          .limit(3)
          .get();
        
        console.log('📖 样本记录:', sampleRecords.data.length, '条');
        
        return {
          success: true,
          count: knowledgeCount.total,
          samples: sampleRecords.data.length,
          status: 'available'
        };
      } else {
        return { 
          success: false, 
          error: '知识库为空',
          count: 0
        };
      }
      
    } catch (error) {
      console.error('❌ 知识库测试失败:', error);
      return { 
        success: false, 
        error: error.message,
        count: 0
      };
    }
  },

  // 本地系统检查
  async checkSystemLocal() {
    console.log('🔍 本地系统检查...');
    
    try {
      // 检查云开发环境
      const envCheck = wx.cloud.getCloudEnv ? wx.cloud.getCloudEnv() : 'unknown';
      
      // 检查数据库连接
      const db = wx.cloud.database();
      const collections = ['knowledge_base', 'terminology_dictionary'];
      
      const collectionStatus = {};
      for (const collection of collections) {
        try {
          const count = await db.collection(collection).count();
          collectionStatus[collection] = {
            exists: true,
            count: count.total
          };
        } catch (error) {
          collectionStatus[collection] = {
            exists: false,
            error: error.message
          };
        }
      }
      
      console.log('📊 集合状态:', collectionStatus);
      
      return {
        success: true,
        environment: envCheck,
        collections: collectionStatus,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('❌ 系统检查失败:', error);
      return { 
        success: false, 
        error: error.message 
      };
    }
  },

  // 显示修复状态
  showLocalFixStatus(results) {
    const status = results.summary;
    
    let message = `本地化修复状态：\n\n`;
    message += `📖 术语词典: ${status.terminology_status}\n`;
    message += `📚 知识库: ${status.knowledge_status}\n`;
    message += `🔍 系统状态: ${status.system_status}\n`;
    message += `🎯 整体状态: ${status.overall_status}\n\n`;
    
    if (results.success) {
      message += `✅ 系统修复完成，可以正常使用！`;
    } else {
      message += `⚠️ 部分功能存在问题，但基础功能可用。`;
    }
    
    return message;
  }
};

// 导出本地修复对象
module.exports = localFix;
