# path-is-absolute [![Build Status](https://travis-ci.org/sindresorhus/path-is-absolute.svg?branch=master)](https://travis-ci.org/sindresorhus/path-is-absolute)

> Node.js 0.12 [`path.isAbsolute()`](http://nodejs.org/api/path.html#path_path_isabsolute_path) [ponyfill](https://ponyfill.com)


## Install

```
$ npm install --save path-is-absolute
```


## Usage

```js
const pathIsAbsolute = require('path-is-absolute');

// Running on Linux
pathIsAbsolute('/home/<USER>');
//=> true
pathIsAbsolute('C:/Users/<USER>');
//=> false

// Running on Windows
pathIsAbsolute('C:/Users/<USER>');
//=> true
pathIsAbsolute('/home/<USER>');
//=> false

// Running on any OS
pathIsAbsolute.posix('/home/<USER>');
//=> true
pathIsAbsolute.posix('C:/Users/<USER>');
//=> false
pathIsAbsolute.win32('C:/Users/<USER>');
//=> true
pathIsAbsolute.win32('/home/<USER>');
//=> false
```


## API

See the [`path.isAbsolute()` docs](http://nodejs.org/api/path.html#path_path_isabsolute_path).

### pathIsAbsolute(path)

### pathIsAbsolute.posix(path)

POSIX specific version.

### pathIsAbsolute.win32(path)

Windows specific version.


## License

MIT © [Sindre Sorhus](https://sindresorhus.com)
