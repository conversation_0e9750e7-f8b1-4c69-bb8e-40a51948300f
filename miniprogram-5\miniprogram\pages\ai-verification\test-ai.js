// AI验证系统测试脚本
// 用于在小程序中直接测试AI验证功能

const testAIVerification = {
  
  // 测试DeepSeek API连接
  async testAPIConnection() {
    console.log('🔍 测试DeepSeek API连接...');
    
    try {
      const response = await wx.request({
        url: 'https://api.deepseek.com/v1/chat/completions',
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer sk-********************************'
        },
        data: {
          model: 'deepseek-chat',
          messages: [
            {
              role: 'user',
              content: '请回复"连接成功"'
            }
          ],
          temperature: 0.1,
          max_tokens: 50
        }
      });
      
      console.log('API响应:', response);
      
      if (response.statusCode === 200 && response.data.choices) {
        const reply = response.data.choices[0].message.content;
        console.log('✅ API连接成功，回复:', reply);
        return { success: true, reply: reply };
      } else {
        console.log('❌ API连接失败:', response);
        return { success: false, error: 'API响应异常' };
      }
      
    } catch (error) {
      console.error('❌ API连接错误:', error);
      return { success: false, error: error.message };
    }
  },

  // 测试知识库查询
  async testKnowledgeQuery() {
    console.log('📚 测试知识库查询...');
    
    try {
      const db = wx.cloud.database();
      
      // 查询知识库记录数
      const knowledgeCount = await db.collection('knowledge_base').count();
      console.log(`📊 知识库记录数: ${knowledgeCount.total}`);
      
      // 查询样本记录
      const sampleRecords = await db.collection('knowledge_base')
        .field({ title: true, content: true, category: true })
        .limit(3)
        .get();
      
      console.log('📖 样本记录:', sampleRecords.data);
      
      return {
        success: true,
        count: knowledgeCount.total,
        samples: sampleRecords.data
      };
      
    } catch (error) {
      console.error('❌ 知识库查询失败:', error);
      return { success: false, error: error.message };
    }
  },

  // 测试术语词典
  async testTerminologyDict() {
    console.log('📖 测试术语词典...');
    
    try {
      const db = wx.cloud.database();
      
      const terminology = await db.collection('terminology_dictionary')
        .where({ version: '1.0' })
        .get();
      
      if (terminology.data.length > 0) {
        console.log('✅ 术语词典可用:', terminology.data[0].categories);
        return {
          success: true,
          categories: Object.keys(terminology.data[0].categories),
          count: Object.keys(terminology.data[0].categories).length
        };
      } else {
        console.log('⚠️ 术语词典为空');
        return { success: false, error: '术语词典为空' };
      }
      
    } catch (error) {
      console.error('❌ 术语词典查询失败:', error);
      return { success: false, error: error.message };
    }
  },

  // 执行完整测试
  async runFullTest() {
    console.log('🚀 开始AI验证系统完整测试...');
    
    const results = {
      api_connection: await this.testAPIConnection(),
      knowledge_query: await this.testKnowledgeQuery(),
      terminology_dict: await this.testTerminologyDict()
    };
    
    console.log('📊 测试结果汇总:', results);
    
    // 计算整体状态
    const allSuccess = Object.values(results).every(result => result.success);
    
    return {
      success: allSuccess,
      results: results,
      summary: {
        api_status: results.api_connection.success ? '连接正常' : '连接失败',
        knowledge_status: results.knowledge_query.success ? `${results.knowledge_query.count}条记录` : '查询失败',
        terminology_status: results.terminology_dict.success ? `${results.terminology_dict.count}个分类` : '查询失败',
        overall_status: allSuccess ? '系统就绪' : '存在问题'
      }
    };
  },

  // 测试AI分析验证
  async testAnalysisVerification() {
    console.log('🔍 测试AI分析验证...');
    
    const testData = {
      analysis_type: 'liuyao',
      user_question: '我的投资什么时候能盈利？',
      divination_result: {
        hexagram: '天雷无妄',
        changing_lines: [3],
        analysis: '妻财爻持世，投资有利'
      },
      analysis_content: '根据卦象显示，您的投资在3个月内会有收益，建议在春季行动。'
    };
    
    try {
      // 获取知识库内容
      const knowledgeQuery = await this.testKnowledgeQuery();
      if (!knowledgeQuery.success) {
        throw new Error('知识库不可用');
      }
      
      // 构建验证提示词
      const prompt = this.buildVerificationPrompt(testData, knowledgeQuery.samples);
      
      // 调用DeepSeek API
      const apiResult = await this.callDeepSeekAPI(prompt);
      if (!apiResult.success) {
        throw new Error('API调用失败');
      }
      
      // 解析结果
      const verification = this.parseVerificationResult(apiResult.reply);
      
      console.log('✅ AI分析验证完成:', verification);
      
      return {
        success: true,
        verification: verification,
        test_data: testData
      };
      
    } catch (error) {
      console.error('❌ AI分析验证失败:', error);
      return { success: false, error: error.message };
    }
  },

  // 构建验证提示词
  buildVerificationPrompt(testData, knowledgeSamples) {
    const knowledgeText = knowledgeSamples
      .map(record => `《${record.title}》：${record.content.substring(0, 300)}`)
      .join('\n\n');
    
    return `你是一位精通中国传统占卜学的专家，请验证以下分析结果的准确性。

【占卜类型】：${testData.analysis_type}
【用户问题】：${testData.user_question}
【占卜结果】：${JSON.stringify(testData.divination_result)}
【当前分析】：${testData.analysis_content}

【知识库依据】：
${knowledgeText}

请从以下维度评分（1-10分）：
1. 准确性评分：分析是否符合传统理论
2. 知识库符合度：是否基于古籍内容
3. 术语正确性：专业术语使用是否准确
4. 预测具体性：是否给出具体建议

请以JSON格式返回：
{
  "accuracy_score": 分数,
  "knowledge_compliance": 分数,
  "terminology_correctness": 分数,
  "prediction_specificity": 分数,
  "issues_found": ["问题1", "问题2"],
  "suggestions": ["建议1", "建议2"]
}`;
  },

  // 调用DeepSeek API
  async callDeepSeekAPI(prompt) {
    try {
      const response = await wx.request({
        url: 'https://api.deepseek.com/v1/chat/completions',
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer sk-********************************'
        },
        data: {
          model: 'deepseek-chat',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.1,
          max_tokens: 1000
        }
      });
      
      if (response.statusCode === 200 && response.data.choices) {
        return {
          success: true,
          reply: response.data.choices[0].message.content
        };
      } else {
        return { success: false, error: 'API响应异常' };
      }
      
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  // 解析验证结果
  parseVerificationResult(apiResponse) {
    try {
      const jsonMatch = apiResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      return {
        accuracy_score: 7,
        knowledge_compliance: 7,
        terminology_correctness: 8,
        prediction_specificity: 6,
        issues_found: ['API响应格式解析失败'],
        suggestions: ['建议重新验证'],
        raw_response: apiResponse
      };
      
    } catch (error) {
      return {
        accuracy_score: 5,
        knowledge_compliance: 5,
        terminology_correctness: 5,
        prediction_specificity: 5,
        issues_found: ['验证结果解析失败'],
        suggestions: ['建议检查API响应'],
        raw_response: apiResponse
      };
    }
  }
};

// 导出测试对象
module.exports = testAIVerification;
