{"version": 3, "file": "index.spec.js", "sourceRoot": "", "sources": ["../src/index.spec.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,+BAA6B;AAC7B,iDAAoC;AACpC,+BAA2B;AAC3B,iCAAiC;AACjC,iCAAiC;AACjC,yCAAyC;AACzC,mCAAmD;AACnD,2BAAqD;AACrD,4CAA2C;AAE3C,MAAM,KAAK,GAAG,SAAS,CAAC,oBAAI,CAAC,CAAA;AAE7B,MAAM,QAAQ,GAAG,WAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAA;AAC5C,MAAM,OAAO,GAAG,WAAI,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAA;AAC/C,MAAM,QAAQ,GAAG,WAAI,CAAC,QAAQ,EAAE,2BAA2B,CAAC,CAAA;AAC5D,MAAM,eAAe,GAAG,WAAI,CAAC,QAAQ,EAAE,kCAAkC,CAAC,CAAA;AAE1E,MAAM,iBAAiB,GAAG,gFAAgF,CAAA;AAE1G,wEAAwE;AACxE,MAAM,CAAC;;QACL,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QACnB,MAAM,KAAK,CAAC,aAAa,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAA;QAC7C,MAAM,eAAe,GAAG,WAAI,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAA;QAC3D,eAAU,CAAC,eAAe,CAAC,IAAI,eAAU,CAAC,eAAe,CAAC,CAAA;IAC5D,CAAC;CAAA,CAAC,CAAA;AAEF,QAAQ,CAAC,SAAS,EAAE;IAClB,MAAM,GAAG,GAAG,IAAI,QAAQ,gBAAgB,OAAO,GAAG,CAAA;IAElD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEnB,EAAE,CAAC,mCAAmC,EAAE;QACtC,aAAM,CAAC,eAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAA;IAC9D,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,KAAK,EAAE;QACd,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEf,EAAE,CAAC,oBAAoB,EAAE,UAAU,IAAI;YACrC,oBAAI,CAAC,GAAG,GAAG,oBAAoB,EAAE,UAAU,GAAG,EAAE,MAAM;gBACpD,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE1C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,yBAAyB,EAAE,UAAU,IAAI;YAC1C,oBAAI,CAAC,yCAAyC,EAAE;gBAC9C,GAAG,EAAE,QAAQ;aACd,EAAE,UAAU,GAAG,EAAE,MAAM;gBACtB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE1C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,uCAAuC,EAAE,UAAU,IAAI;YACxD,oBAAI,CAAC,GAAG,GAAG,KAAK,WAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,GAAG,EAAE,UAAU,GAAG,EAAE,MAAM;gBACrE,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE1C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,sBAAsB,EAAE,UAAU,IAAI;YACvC,oBAAI,CAAC,GAAG,GAAG,kEAAkE,EAAE,UAAU,GAAG,EAAE,MAAM;gBAClG,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;gBAEpC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,gDAAgD,EAAE,UAAU,IAAI;YACjE,oBAAI,CAAC,GAAG,GAAG,YAAY,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC5C,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;gBAEnC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,mDAAmD,EAAE,UAAU,IAAI;YACpE,oBAAI,CAAC,iCAAiC,EAAE;gBACtC,GAAG,EAAE,QAAQ;aACd,EAAE,UAAU,GAAG,EAAE,MAAM;gBACtB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;gBAEnC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE;YACnC,EAAE,CAAC,iBAAiB,EAAE,UAAU,IAAI;gBAClC,oBAAI,CACF;oBACE,GAAG;oBACH,6BAA6B;oBAC7B,4DAA4D;iBAC7D,CAAC,IAAI,CAAC,GAAG,CAAC,EACX,UAAU,GAAG,EAAE,MAAM;oBACnB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;oBAExC,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CACF,CAAA;YACH,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,yCAAyC,EAAE,UAAU,IAAI;gBAC1D,oBAAI,CACF;oBACE,GAAG;oBACH,6BAA6B;oBAC7B,wEAAwE;iBACzE,CAAC,IAAI,CAAC,GAAG,CAAC,EACX,UAAU,GAAG,EAAE,MAAM;oBACnB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;oBAExC,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CACF,CAAA;YACH,CAAC,CAAC,CAAA;SACH;QAED,EAAE,CAAC,kBAAkB,EAAE,UAAU,IAAI;YACnC,oBAAI,CACF,GAAG,GAAG,0EAA0E,EAChF,UAAU,GAAG,EAAE,MAAM;gBACnB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;gBAEjC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CACF,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,2BAA2B,EAAE,UAAU,IAAI;YAC5C,oBAAI,CAAC,GAAG,GAAG,8BAA8B,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC9D,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;gBAE3B,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,qBAAqB,EAAE,UAAU,IAAI;YACtC,oBAAI,CAAC,GAAG,GAAG,uEAAuE,EAAE,UAAU,GAAG;gBAC/F,IAAI,GAAG,KAAK,IAAI,EAAE;oBAChB,OAAO,IAAI,CAAC,iDAAiD,CAAC,CAAA;iBAC/D;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,MAAM,CACrC,8CAA8C;oBAC9C,sDAAsD,CACvD,CAAC,CAAA;gBAEF,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,qCAAqC,EAAE,UAAU,IAAI;YACtD,oBAAI,CACF,GAAG,GAAG,iGAAiG,EACvG,UAAU,GAAG;gBACX,IAAI,GAAG,KAAK,IAAI,EAAE;oBAChB,OAAO,IAAI,CAAC,iDAAiD,CAAC,CAAA;iBAC/D;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAC1B,iGAAiG,CAClG,CAAA;gBAED,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CACF,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,8BAA8B,EAAE,UAAU,IAAI;YAC/C,oBAAI,CAAC,GAAG,GAAG,cAAc,EAAE,UAAU,GAAG;gBACtC,IAAI,GAAG,KAAK,IAAI,EAAE;oBAChB,OAAO,IAAI,CAAC,iDAAiD,CAAC,CAAA;iBAC/D;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;oBAC7B,GAAG,WAAI,CAAC,SAAS,EAAE,mBAAmB,CAAC,MAAM;oBAC7C,kDAAkD;oBAClD,oBAAoB;oBACpB,uBAAuB;iBACxB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBAEb,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,mCAAmC,EAAE,UAAU,IAAI;YACpD,oBAAI,CAAC,GAAG,GAAG,+BAA+B,EAAE,UAAU,GAAG;gBACvD,IAAI,GAAG,KAAK,IAAI,EAAE;oBAChB,OAAO,IAAI,CAAC,iDAAiD,CAAC,CAAA;iBAC/D;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;oBAC7B,GAAG,WAAI,CAAC,SAAS,EAAE,mBAAmB,CAAC,MAAM;oBAC7C,kDAAkD;oBAClD,oBAAoB;iBACrB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBAEb,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,oCAAoC,EAAE,UAAU,IAAI;YACrD,oBAAI,CAAC,GAAG,GAAG,2BAA2B,EAAE,UAAU,GAAG;gBACnD,IAAI,GAAG,KAAK,IAAI,EAAE;oBAChB,OAAO,IAAI,CAAC,iDAAiD,CAAC,CAAA;iBAC/D;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAA;gBAElE,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,+CAA+C,EAAE,UAAU,IAAI;YAChE,oBAAI,CAAC,GAAG,GAAG,kCAAkC,EAAE,UAAU,GAAG;gBAC1D,IAAI,GAAG,KAAK,IAAI,EAAE;oBAChB,OAAO,IAAI,CAAC,iDAAiD,CAAC,CAAA;iBAC/D;gBAED,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAA;gBAEnE,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,yCAAyC,EAAE,UAAU,IAAI;YAC1D,MAAM,EAAE,GAAG,oBAAI,CAAC,GAAG,EAAE,UAAU,GAAG,EAAE,MAAM;gBACxC,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;gBAElC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,KAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAA;QACvC,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,4BAA4B,EAAE,UAAU,IAAI;YAC7C,MAAM,EAAE,GAAG,oBAAI,CAAC,GAAG,GAAG,KAAK,EAAE,UAAU,GAAG,EAAE,MAAM;gBAChD,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;gBAEjC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,KAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACvB,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,iCAAiC,EAAE,UAAU,IAAI;YAClD,MAAM,EAAE,GAAG,oBAAI,CAAC,GAAG,GAAG,6CAA6C,EAAE,UAAU,GAAG,EAAE,MAAM;gBACxF,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;gBAEtC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,KAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACvB,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,kEAAkE,EAAE,UAAU,IAAI;YACnF,MAAM,EAAE,GAAG,oBAAI,CAAC,GAAG,GAAG,gBAAgB,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC3D,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CACrB,SAAS;oBACT,aAAa;oBACb,IAAI,CACL,CAAA;gBACD,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,KAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAA;QAEvC,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,8BAA8B,EAAE,UAAU,IAAI;YAC/C,oBAAI,CAAC,GAAG,GAAG,sDAAsD,EAAE,UAAU,GAAG,EAAE,MAAM;gBACtF,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAA;gBAE9D,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,0CAA0C,EAAE,UAAU,IAAI;YAC3D,oBAAI,CAAC,GAAG,GAAG,4CAA4C,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC5E,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;gBAEpC,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,IAAI,CAAC,uCAAuC,EAAE,UAAU,IAAI;YAC7D,oBAAI,CAAC,GAAG,GAAG,kDAAkD,EAAE,UAAU,GAAG,EAAE,MAAM;gBAClF,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;gBAE/B,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,6BAA6B,EAAE,UAAU,IAAI;YAC9C,oBAAI,CAAC,GAAG,GAAG,qBAAqB,EAAE,UAAU,GAAG,EAAE,MAAM;gBACrD,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAA;gBAElE,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,sDAAsD,EAAE,UAAU,IAAI;YACvE,oBAAI,CAAC,GAAG,GAAG,sBAAsB,EAAE,UAAU,GAAG,EAAE,MAAM;gBACtD,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE1C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,uCAAuC,EAAE,UAAU,IAAI;YACxD,oBAAI,CAAC,GAAG,GAAG,8BAA8B,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC9D,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAA;gBAE/C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,kEAAkE,EAAE,UAAU,IAAI;YACnF,oBAAI,CAAC,GAAG,GAAG,+CAA+C,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC/E,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAA;gBAE/C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,uEAAuE,EAAE,UAAU,IAAI;YACxF,oBAAI,CAAC,GAAG,GAAG,8BAA8B,EAAE,EAAE,GAAG,kCAAO,OAAO,CAAC,GAAG,KAAE,sBAAsB,EAAE,MAAM,GAAE,EAAE,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC3H,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAA;gBAE/C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,2BAA2B,EAAE,UAAU,IAAI;YAC5C,oBAAI,CAAC,GAAG,GAAG,8BAA8B,EAAE,UAAU,GAAG,EAAE,MAAM;gBAC9D,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE1C,OAAO,IAAI,EAAE,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,YAAY,EAAE;YACrB,EAAE,CAAC,gBAAgB,EAAE,UAAU,IAAI;gBACjC,mEAAmE;gBACnE,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE;oBACvE,IAAI,CAAC,IAAI,EAAE,CAAA;iBACZ;qBAAM;oBACL,oBAAI,CAAC,IAAI,QAAQ,2DAA2D,EAAE,UAAU,GAAG,EAAE,MAAM;wBACjG,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;wBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;wBAE3B,OAAO,IAAI,EAAE,CAAA;oBACf,CAAC,CAAC,CAAA;iBACH;YACH,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,YAAY,EAAE;YACrB,EAAE,CAAC,oBAAoB,EAAE,UAAU,IAAI;gBACrC,oBAAI,CAAC,IAAI,QAAQ,2DAA2D,EAAE,UAAU,GAAG,EAAE,MAAM,EAAE,MAAM;oBACzG,aAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC9B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAA,CAAC,oBAAoB;oBAC3E,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;oBAE3B,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,+BAA+B,EAAE,UAAU,IAAI;gBAChD,oBAAI,CAAC,IAAI,QAAQ,mEAAmE,EAAE,UAAU,GAAG,EAAE,MAAM,EAAE,MAAM;oBACjH,aAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC9B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAA,CAAC,iBAAiB;oBAClF,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;oBAE3B,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE;YACnC,EAAE,CAAC,4BAA4B,EAAE,UAAU,IAAI;gBAC7C,oBAAI,CAAC,GAAG,eAAe,oBAAoB,EAAE,UAAU,GAAG,EAAE,MAAM;oBAChE,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;oBAEhC,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YACF,EAAE,CAAC,uEAAuE,EAAE,UAAU,IAAI;gBACxF,IAAI,aAAQ,CAAC,WAAI,CAAC,QAAQ,EAAE,mCAAmC,CAAC,CAAC,CAAC,cAAc,EAAE,EAAE;oBAClF,oBAAI,CAAC,GAAG,eAAe,0CAA0C,EAAE,UAAU,GAAG,EAAE,MAAM;wBACtF,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;wBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;wBAE3B,OAAO,IAAI,EAAE,CAAA;oBACf,CAAC,CAAC,CAAA;iBACH;qBAAM;oBACL,IAAI,CAAC,IAAI,EAAE,CAAA;iBACZ;YACH,CAAC,CAAC,CAAA;SACH;QAED,QAAQ,CAAC,gDAAgD,EAAE;YACzD,MAAM,QAAQ,GAAG,IAAI,QAAQ,kDAAkD,CAAA;YAE/E,EAAE,CAAC,2CAA2C,EAAE,UAAU,IAAI;gBAC5D,oBAAI,CAAC,GAAG,QAAQ,wCAAwC,EAAE;oBACxD,GAAG,kCACE,OAAO,CAAC,GAAG,KACd,wBAAwB,EAAE,kCAAkC,GAC7D;iBACF,EAAE,UAAU,GAAG,EAAE,MAAM;oBACtB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;oBACrC,aAAM,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAI,CAAC,SAAS,EAAE,yCAAyC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;oBAChI,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,yCAAyC,EAAE,UAAU,IAAI;gBAC1D,oBAAI,CAAC,GAAG,QAAQ,wCAAwC,EAAE,UAAU,GAAG,EAAE,MAAM;oBAC7E,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;oBAC9C,aAAM,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAI,CAAC,SAAS,EAAE,8CAA8C,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;oBACrI,aAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAA;oBACrE,aAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;oBAC1C,aAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;oBAC1C,aAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC5C,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,4CAA4C,EAAE,UAAU,IAAI;gBAC7D,oBAAI,CAAC,GAAG,QAAQ,6GAA6G,EAAE,UAAU,GAAG,EAAE,MAAM;oBAClJ,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;oBAC9C,aAAM,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAI,CAAC,SAAS,EAAE,8CAA8C,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;oBACrI,aAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,CAAC,CAAA;oBAC3D,aAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;oBAC1C,aAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBACzC,aAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC5C,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,kDAAkD,EAAE,UAAU,IAAI;gBACnE,oBAAI,CAAC,GAAG,QAAQ,wCAAwC,EAAE;oBACxD,GAAG,kCACE,OAAO,CAAC,GAAG,KACd,cAAc,EAAE,MAAM,EACtB,mBAAmB,EAAE,MAAM,GAC5B;iBACF,EAAE,UAAU,GAAG,EAAE,MAAM;oBACtB,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;oBAC9C,aAAM,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAI,CAAC,SAAS,EAAE,8CAA8C,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;oBACrI,aAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAA;oBACrE,aAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBACrC,aAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;oBAC1C,aAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC5C,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,eAAe,EAAE;YACxB,EAAE,CAAC,oBAAoB,EAAE,UAAU,IAAI;gBACrC,oBAAI,CAAC,GAAG,GAAG,oCAAoC,EAAE,UAAU,GAAG,EAAE,MAAM;oBACpE,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;oBAE1C,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,+CAA+C,EAAE,UAAU,IAAI;gBAChE,oBAAI,CAAC,GAAG,GAAG,0EAA0E,EAAE,UAAU,GAAG,EAAE,MAAM;oBAC1G,IAAI,GAAG,KAAK,IAAI;wBAAE,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAA;oBAElD,aAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAA;oBAE9E,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,UAAU,EAAE;QACnB,MAAM,UAAU,GAAG,gBAAQ,CAAC;YAC1B,OAAO,EAAE,OAAO;YAChB,eAAe,EAAE;gBACf,GAAG,EAAE,UAAU;aAChB;SACF,CAAC,CAAA;QAEF,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;QAEzD,SAAS,CAAC,GAAG,EAAE;YACb,sCAAsC;YACtC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAC1B,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,sCAAsC,EAAE;YACzC,MAAM,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;YAEjC,aAAM,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC1C,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,sCAAsC,EAAE;YACzC,OAAO,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;YAEpC,aAAM,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YACjD,aAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAA;YAElE,OAAO,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;YAEpC,aAAM,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAC5C,aAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAA;YAElE,OAAO,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;YAEpC,aAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YAC/C,aAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,CAAA;YAEpD,OAAO,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;YAEpC,aAAM,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YAC3C,aAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,CAAA;QACtD,CAAC,CAAC,CAAA;QAEF,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE;YACnC,EAAE,CAAC,gCAAgC,EAAE;gBACnC,MAAM,KAAK,GAAa,EAAE,CAAA;gBAE1B,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;gBAEzB,MAAM,SAAS,GAAG;oBAChB,gBAAQ,CAAC,EAAE,GAAG,EAAE,WAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;oBACzD,gBAAQ,CAAC,EAAE,GAAG,EAAE,WAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;iBAC1D,CAAA;gBAED,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;oBACpB,MAAM,GAAG,GAAG,CAAC,CAAC,OAAO,CAAA;oBACrB,CAAC,CAAC,OAAO,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,EAAE;wBACzC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;wBAEpB,OAAO,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAA;oBACxC,CAAC,CAAA;gBACH,CAAC,CAAC,CAAA;gBAEF,IAAI;oBACF,aAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;oBACvD,aAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;iBACxD;wBAAS;oBACR,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAA;iBACzC;gBAED,aAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;oBAC1B,WAAI,CAAC,QAAQ,EAAE,kBAAkB,CAAC;oBAClC,WAAI,CAAC,QAAQ,EAAE,kBAAkB,CAAC;iBACnC,CAAC,CAAA;gBAEF,OAAO,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;gBAEpC,aAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA;YAClD,CAAC,CAAC,CAAA;SACH;QAED,EAAE,CAAC,kCAAkC,EAAE;YACrC,MAAM,CAAC,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAA;YAErC,aAAM,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;QACzC,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,6BAA6B,EAAE;YAChC,MAAM,CAAC,GAAG,UAAU,CAAC,kBAAkB,EAAE;gBACvC,WAAW,EAAE,OAAO;aACrB,CAAC,CAAA;YAEF,aAAM,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QACvC,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,kCAAkC,EAAE;YACrC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAA;YAEhE,aAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QACzC,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,wBAAwB,EAAE,UAAU,IAAI;YACzC,IAAI;gBACF,OAAO,CAAC,gBAAgB,CAAC,CAAA;aAC1B;YAAC,OAAO,KAAK,EAAE;gBACd,aAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC;oBAC7B,uBAAuB;oBACvB,mBAAmB,WAAI,CAAC,SAAS,EAAE,mBAAmB,CAAC,UAAU;iBAClE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBAEb,IAAI,EAAE,CAAA;aACP;QACH,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;YAC5B,IAAI,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA,CAAC,sBAAsB;YAC3D,IAAI,QAAgB,CAAA;YAEpB,MAAM,CAAC;gBACL,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAM,EAAE,QAAQ,EAAE,EAAE;oBAChD,MAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAA;oBAE3B,CAAC,CAAC,QAAQ,GAAG,CAAC,IAAY,EAAE,QAAgB,EAAE,EAAE;wBAC9C,QAAQ,GAAG,IAAI,CAAA;wBACf,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;oBAC5C,CAAC,CAAA;oBAED,OAAO,GAAI,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAA;gBAC1B,CAAC,CAAA;YACH,CAAC,CAAC,CAAA;YAEF,KAAK,CAAC;gBACJ,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA,CAAC,sBAAsB;YACzD,CAAC,CAAC,CAAA;YAEF,EAAE,CAAC,wBAAwB,EAAE,UAAU,IAAI;gBACzC,IAAI;oBACF,OAAO,CAAC,uBAAuB,CAAC,CAAA;iBACjC;gBAAC,OAAO,KAAK,EAAE;oBACd,aAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAA;iBAChE;gBAED,aAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;gBAE5C,IAAI,EAAE,CAAA;YACR,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,OAAO,GAAG,cAAM,CAAC,EAAE,eAAe,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAA;YACjF,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,SAAS,CAAC,CAAA;YAEzD,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA;QAC1C,CAAC,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;IAEF,IAAI,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;QACzC,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE;YACnB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,MAAM,GAAG,GAAG,6BAA6B,CAAA;YAEzC,EAAE,CAAC,mCAAmC,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC/C,oBAAI,CAAC,GAAG,GAAG,WAAW,EAAE,EAAE,GAAG,EAAE,WAAI,CAAC,SAAS,EAAE,cAAc,CAAC,EAAE,EAAE,UAAU,GAAG,EAAE,MAAM;oBACrF,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAA;oBAE7C,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YACF,EAAE,CAAC,mDAAmD,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC/D,oBAAI,CAAC,GAAG,GAAG,oDAAoD,EAAE,EAAE,GAAG,EAAE,WAAI,CAAC,SAAS,EAAE,4BAA4B,CAAC,EAAE,EAAE,UAAU,GAAG,EAAE,MAAM;oBAC5I,aAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBAC1B,aAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAA;oBAE7C,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC,CAAC,CAAA;YAEJ,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;KACH;AACH,CAAC,CAAC,CAAA", "sourcesContent": ["import { expect } from 'chai'\nimport { exec } from 'child_process'\nimport { join } from 'path'\nimport semver = require('semver')\nimport ts = require('typescript')\nimport proxyquire = require('proxyquire')\nimport { register, create, VERSION } from './index'\nimport { unlinkSync, existsSync, statSync } from 'fs'\nimport * as promisify from 'util.promisify'\n\nconst execP = promisify(exec)\n\nconst TEST_DIR = join(__dirname, '../tests')\nconst PROJECT = join(TEST_DIR, 'tsconfig.json')\nconst BIN_PATH = join(TEST_DIR, 'node_modules/.bin/ts-node')\nconst BIN_SCRIPT_PATH = join(TEST_DIR, 'node_modules/.bin/ts-node-script')\n\nconst SOURCE_MAP_REGEXP = /\\/\\/# sourceMappingURL=data:application\\/json;charset=utf\\-8;base64,[\\w\\+]+=*$/\n\n// Pack and install ts-node locally, necessary to test package \"exports\"\nbefore(async function () {\n  this.timeout(30000)\n  await execP(`npm install`, { cwd: TEST_DIR })\n  const packageLockPath = join(TEST_DIR, 'package-lock.json')\n  existsSync(packageLockPath) && unlinkSync(packageLockPath)\n})\n\ndescribe('ts-node', function () {\n  const cmd = `\"${BIN_PATH}\" --project \"${PROJECT}\"`\n\n  this.timeout(10000)\n\n  it('should export the correct version', function () {\n    expect(VERSION).to.equal(require('../package.json').version)\n  })\n\n  describe('cli', function () {\n    this.slow(1000)\n\n    it('should execute cli', function (done) {\n      exec(`${cmd} tests/hello-world`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, world!\\n')\n\n        return done()\n      })\n    })\n\n    it('should register via cli', function (done) {\n      exec(`node -r ts-node/register hello-world.ts`, {\n        cwd: TEST_DIR\n      }, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, world!\\n')\n\n        return done()\n      })\n    })\n\n    it('should execute cli with absolute path', function (done) {\n      exec(`${cmd} \"${join(TEST_DIR, 'hello-world')}\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, world!\\n')\n\n        return done()\n      })\n    })\n\n    it('should print scripts', function (done) {\n      exec(`${cmd} -pe \"import { example } from './tests/complex/index';example()\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('example\\n')\n\n        return done()\n      })\n    })\n\n    it('should provide registered information globally', function (done) {\n      exec(`${cmd} tests/env`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('object\\n')\n\n        return done()\n      })\n    })\n\n    it('should provide registered information on register', function (done) {\n      exec(`node -r ts-node/register env.ts`, {\n        cwd: TEST_DIR\n      }, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('object\\n')\n\n        return done()\n      })\n    })\n\n    if (semver.gte(ts.version, '1.8.0')) {\n      it('should allow js', function (done) {\n        exec(\n          [\n            cmd,\n            '-O \"{\\\\\\\"allowJs\\\\\\\":true}\"',\n            '-pe \"import { main } from \\'./tests/allow-js/run\\';main()\"'\n          ].join(' '),\n          function (err, stdout) {\n            expect(err).to.equal(null)\n            expect(stdout).to.equal('hello world\\n')\n\n            return done()\n          }\n        )\n      })\n\n      it('should include jsx when `allow-js` true', function (done) {\n        exec(\n          [\n            cmd,\n            '-O \"{\\\\\\\"allowJs\\\\\\\":true}\"',\n            '-pe \"import { Foo2 } from \\'./tests/allow-js/with-jsx\\'; Foo2.sayHi()\"'\n          ].join(' '),\n          function (err, stdout) {\n            expect(err).to.equal(null)\n            expect(stdout).to.equal('hello world\\n')\n\n            return done()\n          }\n        )\n      })\n    }\n\n    it('should eval code', function (done) {\n      exec(\n        `${cmd} -e \"import * as m from './tests/module';console.log(m.example('test'))\"`,\n        function (err, stdout) {\n          expect(err).to.equal(null)\n          expect(stdout).to.equal('TEST\\n')\n\n          return done()\n        }\n      )\n    })\n\n    it('should import empty files', function (done) {\n      exec(`${cmd} -e \"import './tests/empty'\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('')\n\n        return done()\n      })\n    })\n\n    it('should throw errors', function (done) {\n      exec(`${cmd} -e \"import * as m from './tests/module';console.log(m.example(123))\"`, function (err) {\n        if (err === null) {\n          return done('Command was expected to fail, but it succeeded.')\n        }\n\n        expect(err.message).to.match(new RegExp(\n          'TS2345: Argument of type \\'(?:number|123)\\' ' +\n          'is not assignable to parameter of type \\'string\\'\\\\.'\n        ))\n\n        return done()\n      })\n    })\n\n    it('should be able to ignore diagnostic', function (done) {\n      exec(\n        `${cmd} --ignore-diagnostics 2345 -e \"import * as m from './tests/module';console.log(m.example(123))\"`,\n        function (err) {\n          if (err === null) {\n            return done('Command was expected to fail, but it succeeded.')\n          }\n\n          expect(err.message).to.match(\n            /TypeError: (?:(?:undefined|foo\\.toUpperCase) is not a function|.*has no method \\'toUpperCase\\')/\n          )\n\n          return done()\n        }\n      )\n    })\n\n    it('should work with source maps', function (done) {\n      exec(`${cmd} tests/throw`, function (err) {\n        if (err === null) {\n          return done('Command was expected to fail, but it succeeded.')\n        }\n\n        expect(err.message).to.contain([\n          `${join(__dirname, '../tests/throw.ts')}:100`,\n          '  bar () { throw new Error(\\'this is a demo\\') }',\n          '                 ^',\n          'Error: this is a demo'\n        ].join('\\n'))\n\n        return done()\n      })\n    })\n\n    it('eval should work with source maps', function (done) {\n      exec(`${cmd} -pe \"import './tests/throw'\"`, function (err) {\n        if (err === null) {\n          return done('Command was expected to fail, but it succeeded.')\n        }\n\n        expect(err.message).to.contain([\n          `${join(__dirname, '../tests/throw.ts')}:100`,\n          '  bar () { throw new Error(\\'this is a demo\\') }',\n          '                 ^'\n        ].join('\\n'))\n\n        return done()\n      })\n    })\n\n    it('should support transpile only mode', function (done) {\n      exec(`${cmd} --transpile-only -pe \"x\"`, function (err) {\n        if (err === null) {\n          return done('Command was expected to fail, but it succeeded.')\n        }\n\n        expect(err.message).to.contain('ReferenceError: x is not defined')\n\n        return done()\n      })\n    })\n\n    it('should throw error even in transpileOnly mode', function (done) {\n      exec(`${cmd} --transpile-only -pe \"console.\"`, function (err) {\n        if (err === null) {\n          return done('Command was expected to fail, but it succeeded.')\n        }\n\n        expect(err.message).to.contain('error TS1003: Identifier expected')\n\n        return done()\n      })\n    })\n\n    it('should pipe into `ts-node` and evaluate', function (done) {\n      const cp = exec(cmd, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('hello\\n')\n\n        return done()\n      })\n\n      cp.stdin!.end(\"console.log('hello')\")\n    })\n\n    it('should pipe into `ts-node`', function (done) {\n      const cp = exec(`${cmd} -p`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('true\\n')\n\n        return done()\n      })\n\n      cp.stdin!.end('true')\n    })\n\n    it('should pipe into an eval script', function (done) {\n      const cp = exec(`${cmd} --transpile-only -pe \"process.stdin.isTTY\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('undefined\\n')\n\n        return done()\n      })\n\n      cp.stdin!.end('true')\n    })\n\n    it('should run REPL when --interactive passed and stdin is not a TTY', function (done) {\n      const cp = exec(`${cmd} --interactive`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal(\n          '> 123\\n' +\n          'undefined\\n' +\n          '> '\n        )\n        return done()\n      })\n\n      cp.stdin!.end('console.log(\"123\")\\n')\n\n    })\n\n    it('should support require flags', function (done) {\n      exec(`${cmd} -r ./tests/hello-world -pe \"console.log('success')\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, world!\\nsuccess\\nundefined\\n')\n\n        return done()\n      })\n    })\n\n    it('should support require from node modules', function (done) {\n      exec(`${cmd} -r typescript -e \"console.log('success')\"`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('success\\n')\n\n        return done()\n      })\n    })\n\n    it.skip('should use source maps with react tsx', function (done) {\n      exec(`${cmd} -r ./tests/emit-compiled.ts tests/jsx-react.tsx`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('todo')\n\n        return done()\n      })\n    })\n\n    it('should allow custom typings', function (done) {\n      exec(`${cmd} tests/custom-types`, function (err, stdout) {\n        expect(err).to.match(/Error: Cannot find module 'does-not-exist'/)\n\n        return done()\n      })\n    })\n\n    it('should preserve `ts-node` context with child process', function (done) {\n      exec(`${cmd} tests/child-process`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, world!\\n')\n\n        return done()\n      })\n    })\n\n    it('should import js before ts by default', function (done) {\n      exec(`${cmd} tests/import-order/compiled`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, JavaScript!\\n')\n\n        return done()\n      })\n    })\n\n    it('should import ts before js when --prefer-ts-exts flag is present', function (done) {\n      exec(`${cmd} --prefer-ts-exts tests/import-order/compiled`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, TypeScript!\\n')\n\n        return done()\n      })\n    })\n\n    it('should import ts before js when TS_NODE_PREFER_TS_EXTS env is present', function (done) {\n      exec(`${cmd} tests/import-order/compiled`, { env: { ...process.env, TS_NODE_PREFER_TS_EXTS: 'true' } }, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, TypeScript!\\n')\n\n        return done()\n      })\n    })\n\n    it('should ignore .d.ts files', function (done) {\n      exec(`${cmd} tests/import-order/importer`, function (err, stdout) {\n        expect(err).to.equal(null)\n        expect(stdout).to.equal('Hello, World!\\n')\n\n        return done()\n      })\n    })\n\n    describe('issue #884', function () {\n      it('should compile', function (done) {\n        // TODO disabled because it consistently fails on Windows on TS 2.7\n        if (process.platform === 'win32' && semver.satisfies(ts.version, '2.7')) {\n          this.skip()\n        } else {\n          exec(`\"${BIN_PATH}\" --project tests/issue-884/tsconfig.json tests/issue-884`, function (err, stdout) {\n            expect(err).to.equal(null)\n            expect(stdout).to.equal('')\n\n            return done()\n          })\n        }\n      })\n    })\n\n    describe('issue #986', function () {\n      it('should not compile', function (done) {\n        exec(`\"${BIN_PATH}\" --project tests/issue-986/tsconfig.json tests/issue-986`, function (err, stdout, stderr) {\n          expect(err).not.to.equal(null)\n          expect(stderr).to.contain('Cannot find name \\'TEST\\'') // TypeScript error.\n          expect(stdout).to.equal('')\n\n          return done()\n        })\n      })\n\n      it('should compile with `--files`', function (done) {\n        exec(`\"${BIN_PATH}\" --files --project tests/issue-986/tsconfig.json tests/issue-986`, function (err, stdout, stderr) {\n          expect(err).not.to.equal(null)\n          expect(stderr).to.contain('ReferenceError: TEST is not defined') // Runtime error.\n          expect(stdout).to.equal('')\n\n          return done()\n        })\n      })\n    })\n\n    if (semver.gte(ts.version, '2.7.0')) {\n      it('should support script mode', function (done) {\n        exec(`${BIN_SCRIPT_PATH} tests/scope/a/log`, function (err, stdout) {\n          expect(err).to.equal(null)\n          expect(stdout).to.equal('.ts\\n')\n\n          return done()\n        })\n      })\n      it('should read tsconfig relative to realpath, not symlink, in scriptMode', function (done) {\n        if (statSync(join(TEST_DIR, 'main-realpath/symlink/symlink.tsx')).isSymbolicLink()) {\n          exec(`${BIN_SCRIPT_PATH} tests/main-realpath/symlink/symlink.tsx`, function (err, stdout) {\n            expect(err).to.equal(null)\n            expect(stdout).to.equal('')\n\n            return done()\n          })\n        } else {\n          this.skip()\n        }\n      })\n    }\n\n    describe('should read ts-node options from tsconfig.json', function () {\n      const BIN_EXEC = `\"${BIN_PATH}\" --project tests/tsconfig-options/tsconfig.json`\n\n      it('should override compiler options from env', function (done) {\n        exec(`${BIN_EXEC} tests/tsconfig-options/log-options.js`, {\n          env: {\n            ...process.env,\n            TS_NODE_COMPILER_OPTIONS: '{\"typeRoots\": [\"env-typeroots\"]}'\n          }\n        }, function (err, stdout) {\n          expect(err).to.equal(null)\n          const { config } = JSON.parse(stdout)\n          expect(config.options.typeRoots).to.deep.equal([join(__dirname, '../tests/tsconfig-options/env-typeroots').replace(/\\\\/g, '/')])\n          return done()\n        })\n      })\n\n      it('should use options from `tsconfig.json`', function (done) {\n        exec(`${BIN_EXEC} tests/tsconfig-options/log-options.js`, function (err, stdout) {\n          expect(err).to.equal(null)\n          const { options, config } = JSON.parse(stdout)\n          expect(config.options.typeRoots).to.deep.equal([join(__dirname, '../tests/tsconfig-options/tsconfig-typeroots').replace(/\\\\/g, '/')])\n          expect(config.options.types).to.deep.equal(['tsconfig-tsnode-types'])\n          expect(options.pretty).to.equal(undefined)\n          expect(options.skipIgnore).to.equal(false)\n          expect(options.transpileOnly).to.equal(true)\n          return done()\n        })\n      })\n\n      it('should have flags override `tsconfig.json`', function (done) {\n        exec(`${BIN_EXEC} --skip-ignore --compiler-options \"{\\\\\"types\\\\\":[\\\\\"flags-types\\\\\"]}\" tests/tsconfig-options/log-options.js`, function (err, stdout) {\n          expect(err).to.equal(null)\n          const { options, config } = JSON.parse(stdout)\n          expect(config.options.typeRoots).to.deep.equal([join(__dirname, '../tests/tsconfig-options/tsconfig-typeroots').replace(/\\\\/g, '/')])\n          expect(config.options.types).to.deep.equal(['flags-types'])\n          expect(options.pretty).to.equal(undefined)\n          expect(options.skipIgnore).to.equal(true)\n          expect(options.transpileOnly).to.equal(true)\n          return done()\n        })\n      })\n\n      it('should have `tsconfig.json` override environment', function (done) {\n        exec(`${BIN_EXEC} tests/tsconfig-options/log-options.js`, {\n          env: {\n            ...process.env,\n            TS_NODE_PRETTY: 'true',\n            TS_NODE_SKIP_IGNORE: 'true'\n          }\n        }, function (err, stdout) {\n          expect(err).to.equal(null)\n          const { options, config } = JSON.parse(stdout)\n          expect(config.options.typeRoots).to.deep.equal([join(__dirname, '../tests/tsconfig-options/tsconfig-typeroots').replace(/\\\\/g, '/')])\n          expect(config.options.types).to.deep.equal(['tsconfig-tsnode-types'])\n          expect(options.pretty).to.equal(true)\n          expect(options.skipIgnore).to.equal(false)\n          expect(options.transpileOnly).to.equal(true)\n          return done()\n        })\n      })\n    })\n\n    describe('compiler host', function () {\n      it('should execute cli', function (done) {\n        exec(`${cmd} --compiler-host tests/hello-world`, function (err, stdout) {\n          expect(err).to.equal(null)\n          expect(stdout).to.equal('Hello, world!\\n')\n\n          return done()\n        })\n      })\n\n      it('should give ts error for invalid node_modules', function (done) {\n        exec(`${cmd} --compiler-host --skip-ignore tests/from-node-modules/from-node-modules`, function (err, stdout) {\n          if (err === null) return done('Expected an error')\n\n          expect(err.message).to.contain('Unable to compile file from external library')\n\n          return done()\n        })\n      })\n    })\n  })\n\n  describe('register', function () {\n    const registered = register({\n      project: PROJECT,\n      compilerOptions: {\n        jsx: 'preserve'\n      }\n    })\n\n    const moduleTestPath = require.resolve('../tests/module')\n\n    afterEach(() => {\n      // Re-enable project after every test.\n      registered.enabled(true)\n    })\n\n    it('should be able to require typescript', function () {\n      const m = require(moduleTestPath)\n\n      expect(m.example('foo')).to.equal('FOO')\n    })\n\n    it('should support dynamically disabling', function () {\n      delete require.cache[moduleTestPath]\n\n      expect(registered.enabled(false)).to.equal(false)\n      expect(() => require(moduleTestPath)).to.throw(/Unexpected token/)\n\n      delete require.cache[moduleTestPath]\n\n      expect(registered.enabled()).to.equal(false)\n      expect(() => require(moduleTestPath)).to.throw(/Unexpected token/)\n\n      delete require.cache[moduleTestPath]\n\n      expect(registered.enabled(true)).to.equal(true)\n      expect(() => require(moduleTestPath)).to.not.throw()\n\n      delete require.cache[moduleTestPath]\n\n      expect(registered.enabled()).to.equal(true)\n      expect(() => require(moduleTestPath)).to.not.throw()\n    })\n\n    if (semver.gte(ts.version, '2.7.0')) {\n      it('should support compiler scopes', function () {\n        const calls: string[] = []\n\n        registered.enabled(false)\n\n        const compilers = [\n          register({ dir: join(TEST_DIR, 'scope/a'), scope: true }),\n          register({ dir: join(TEST_DIR, 'scope/b'), scope: true })\n        ]\n\n        compilers.forEach(c => {\n          const old = c.compile\n          c.compile = (code, fileName, lineOffset) => {\n            calls.push(fileName)\n\n            return old(code, fileName, lineOffset)\n          }\n        })\n\n        try {\n          expect(require('../tests/scope/a').ext).to.equal('.ts')\n          expect(require('../tests/scope/b').ext).to.equal('.ts')\n        } finally {\n          compilers.forEach(c => c.enabled(false))\n        }\n\n        expect(calls).to.deep.equal([\n          join(TEST_DIR, 'scope/a/index.ts'),\n          join(TEST_DIR, 'scope/b/index.ts')\n        ])\n\n        delete require.cache[moduleTestPath]\n\n        expect(() => require(moduleTestPath)).to.throw()\n      })\n    }\n\n    it('should compile through js and ts', function () {\n      const m = require('../tests/complex')\n\n      expect(m.example()).to.equal('example')\n    })\n\n    it('should work with proxyquire', function () {\n      const m = proxyquire('../tests/complex', {\n        './example': 'hello'\n      })\n\n      expect(m.example()).to.equal('hello')\n    })\n\n    it('should work with `require.cache`', function () {\n      const { example1, example2 } = require('../tests/require-cache')\n\n      expect(example1).to.not.equal(example2)\n    })\n\n    it('should use source maps', function (done) {\n      try {\n        require('../tests/throw')\n      } catch (error) {\n        expect(error.stack).to.contain([\n          'Error: this is a demo',\n          `    at Foo.bar (${join(__dirname, '../tests/throw.ts')}:100:18)`\n        ].join('\\n'))\n\n        done()\n      }\n    })\n\n    describe('JSX preserve', () => {\n      let old = require.extensions['.tsx'] // tslint:disable-line\n      let compiled: string\n\n      before(function () {\n        require.extensions['.tsx'] = (m: any, fileName) => { // tslint:disable-line\n          const _compile = m._compile\n\n          m._compile = (code: string, fileName: string) => {\n            compiled = code\n            return _compile.call(this, code, fileName)\n          }\n\n          return old!(m, fileName)\n        }\n      })\n\n      after(function () {\n        require.extensions['.tsx'] = old // tslint:disable-line\n      })\n\n      it('should use source maps', function (done) {\n        try {\n          require('../tests/with-jsx.tsx')\n        } catch (error) {\n          expect(error.stack).to.contain('SyntaxError: Unexpected token')\n        }\n\n        expect(compiled).to.match(SOURCE_MAP_REGEXP)\n\n        done()\n      })\n    })\n  })\n\n  describe('create', () => {\n    it('should create generic compiler instances', () => {\n      const service = create({ compilerOptions: { target: 'es5' }, skipProject: true })\n      const output = service.compile('const x = 10', 'test.ts')\n\n      expect(output).to.contain('var x = 10;')\n    })\n  })\n\n  if (semver.gte(process.version, '13.0.0')) {\n    describe('esm', () => {\n      this.slow(1000)\n\n      const cmd = `node --loader ../../esm.mjs`\n\n      it('should compile and execute as ESM', (done) => {\n        exec(`${cmd} index.ts`, { cwd: join(__dirname, '../tests/esm') }, function (err, stdout) {\n          expect(err).to.equal(null)\n          expect(stdout).to.equal('foo bar baz biff\\n')\n\n          return done()\n        })\n      })\n      it('supports --experimental-specifier-resolution=node', (done) => {\n        exec(`${cmd} --experimental-specifier-resolution=node index.ts`, { cwd: join(__dirname, '../tests/esm-node-resolver') }, function (err, stdout) {\n          expect(err).to.equal(null)\n          expect(stdout).to.equal('foo bar baz biff\\n')\n\n          return done()\n        })\n\n      })\n    })\n  }\n})\n"]}