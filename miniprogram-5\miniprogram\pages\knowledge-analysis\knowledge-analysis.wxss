/* 知识库分析页面样式 */
.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx 0;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #7f8c8d;
}

/* 状态卡片 */
.status-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.status-value {
  font-size: 28rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.status-value.info {
  background: #e3f2fd;
  color: #1976d2;
}

.status-value.success {
  background: #e8f5e8;
  color: #4caf50;
}

.status-value.error {
  background: #ffebee;
  color: #f44336;
}

/* 进度条 */
.progress-section {
  margin-top: 20rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #ecf0f1;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #7f8c8d;
  float: right;
}

.current-step {
  margin-top: 15rpx;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  border-left: 4rpx solid #3498db;
}

.step-text {
  font-size: 26rpx;
  color: #2c3e50;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 25rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.btn-primary[disabled] {
  background: #bdc3c7;
  color: #7f8c8d;
}

.btn-structurize {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 25rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.btn-structurize[disabled] {
  background: #bdc3c7;
  color: #7f8c8d;
}

.btn-secondary {
  background: white;
  color: #667eea;
  border: 2rpx solid #667eea;
  border-radius: 25rpx;
  padding: 25rpx;
  font-size: 30rpx;
}

.btn-secondary[disabled] {
  border-color: #bdc3c7;
  color: #bdc3c7;
}

/* 结果概览 */
.results-overview {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 25rpx;
  text-align: center;
}

.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.overview-item {
  text-align: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
}

.overview-label {
  display: block;
  font-size: 24rpx;
  color: #7f8c8d;
  margin-bottom: 8rpx;
}

.overview-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
}

/* 详细结果 */
.detailed-results {
  margin-bottom: 30rpx;
}

.result-cards {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.result-card {
  background: white;
  border-radius: 20rpx;
  padding: 25rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  transition: transform 0.2s ease;
}

.result-card:active {
  transform: scale(0.98);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #2c3e50;
}

.card-status {
  font-size: 24rpx;
  color: #27ae60;
  background: #e8f5e8;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.card-content {
  color: #7f8c8d;
}

.card-desc {
  display: block;
  font-size: 26rpx;
  margin-bottom: 15rpx;
}

.card-stats {
  display: flex;
  gap: 20rpx;
}

.stat-item {
  font-size: 24rpx;
  background: #f8f9fa;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
}

/* 优化建议 */
.recommendations {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.recommendation-list {
  margin-top: 20rpx;
}

.recommendation-item {
  padding: 15rpx 0;
  border-bottom: 1rpx solid #ecf0f1;
}

.recommendation-item:last-child {
  border-bottom: none;
}

.recommendation-text {
  font-size: 28rpx;
  color: #2c3e50;
  line-height: 1.6;
}

/* 操作区域 */
.actions {
  text-align: center;
  margin-bottom: 30rpx;
}

.btn-outline {
  background: white;
  color: #3498db;
  border: 2rpx solid #3498db;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 日志区域 */
.logs-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.logs-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.btn-clear {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 15rpx;
  font-size: 24rpx;
}

.logs-container {
  height: 400rpx;
  border: 1rpx solid #ecf0f1;
  border-radius: 10rpx;
  padding: 15rpx;
}

.log-item {
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f8f9fa;
  display: flex;
  align-items: flex-start;
  gap: 15rpx;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  font-size: 22rpx;
  color: #95a5a6;
  min-width: 120rpx;
}

.log-message {
  font-size: 26rpx;
  flex: 1;
  line-height: 1.4;
}

.log-item.info .log-message {
  color: #2c3e50;
}

.log-item.success .log-message {
  color: #27ae60;
}

.log-item.error .log-message {
  color: #e74c3c;
}

.empty-logs {
  text-align: center;
  padding: 60rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #bdc3c7;
}
