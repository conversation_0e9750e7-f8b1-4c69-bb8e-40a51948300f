{"name": "@types/linkify-it", "version": "3.0.2", "description": "TypeScript definitions for linkify-it", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/linkify-it", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/praxxis", "githubUsername": "p<PERSON><PERSON>s"}, {"name": "<PERSON>", "url": "https://github.com/rapropos/typed-linkify-it"}, {"name": "<PERSON>", "url": "https://github.com/alexplumb", "githubUsername": "alexplumb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ragafus", "githubUsername": "<PERSON><PERSON>us"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/linkify-it"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "001e13e25578e89bdd72aba9710cc97e62dacb4982344e91c4d666aa7cfd642d", "typeScriptVersion": "3.6"}