{"version": 3, "file": "bin-script-deprecated.js", "sourceRoot": "", "sources": ["../src/bin-script-deprecated.ts"], "names": [], "mappings": ";;;AAEA,+BAA4B;AAE5B,OAAO,CAAC,IAAI,CACV,8EAA8E,EAC9E,mCAAmC,CACpC,CAAA;AAED,UAAI,CAAC,CAAC,eAAe,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA", "sourcesContent": ["#!/usr/bin/env node\n\nimport { main } from './bin'\n\nconsole.warn(\n  'ts-script has been deprecated and will be removed in the next major release.',\n  'Please use ts-node-script instead'\n)\n\nmain(['--script-mode', ...process.argv.slice(2)])\n"]}