// pages/profile/profile.js - 个人中心页面
const app = getApp();

Page({
  data: {
    userInfo: null,
    isLoggedIn: false,
    menuItems: [
      {
        id: 'knowledge-analysis',
        title: '知识库分析',
        icon: '📊',
        route: '/pages/knowledge-analysis/knowledge-analysis'
      },
      {
        id: 'shopping',
        title: '购物车',
        icon: '🛒',
        route: '/pages/shopping/shopping'
      },
      {
        id: 'history',
        title: '使用记录',
        icon: '📋',
        route: '/pages/history/history'
      },
      {
        id: 'about',
        title: '关于我们',
        icon: 'ℹ️',
        action: 'about'
      }
    ]
  },

  onLoad() {
    console.log('个人中心页面加载');
  },

  onShow() {
    this.refreshUserInfo();
  },

  // 刷新用户信息
  refreshUserInfo() {
    const userInfo = app.globalData.userInfo;

    this.setData({
      userInfo: userInfo,
      isLoggedIn: !!userInfo
    });
  },

  // 点击菜单项
  onClickMenuItem(e) {
    const { item } = e.currentTarget.dataset;

    if (item.route) {
      wx.navigateTo({
        url: item.route
      });
    } else if (item.action) {
      this.handleAction(item.action);
    }
  },

  // 处理特殊操作
  handleAction(action) {
    switch (action) {
      case 'about':
        this.showAbout();
        break;
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
    }
  },

  // 显示关于我们
  showAbout() {
    wx.showModal({
      title: '关于元亨利贞',
      content: '元亨利贞是基于100GB古籍文献的AI智能命理分析平台，传承千年古籍智慧，为您提供专业的命理咨询服务。',
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 显示登录提示
  showLoginTip() {
    wx.showModal({
      title: '需要登录',
      content: '请先登录后使用此功能',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/splash/splash'
          });
        }
      }
    });
  },

  // 登录/注销
  onToggleLogin() {
    if (this.data.isLoggedIn) {
      // 注销
      wx.showModal({
        title: '确认注销',
        content: '确定要注销当前账号吗？',
        confirmText: '确认',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            app.globalData.userInfo = null;
            this.refreshUserInfo();
            wx.showToast({
              title: '已注销',
              icon: 'success'
            });
          }
        }
      });
    } else {
      // 登录
      wx.navigateTo({
        url: '/pages/splash/splash'
      });
    }
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '元亨利贞 - 千年古籍AI智慧',
      path: '/pages/splash/splash',
      imageUrl: '/images/share-cover.jpg'
    };
  }
});