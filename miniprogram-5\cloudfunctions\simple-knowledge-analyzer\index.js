// 云函数：simple-knowledge-analyzer
// 简化版知识库分析器，避免复杂聚合查询

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { action = 'analyze_structure' } = event;
  
  console.log(`简化版知识库分析器被调用: ${action}`);
  
  try {
    switch (action) {
      case 'analyze_structure':
        return await analyzeKnowledgeStructure();
      case 'extract_keywords':
        return await extractKeywords();
      case 'categorize_content':
        return await categorizeContent();
      case 'analyze_terminology':
        return await analyzeTerminology();
      case 'generate_summary':
        return await generateSummary();
      default:
        return { success: false, error: '未知的分析操作' };
    }
  } catch (error) {
    console.error('知识库分析失败:', error);
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * 分析知识库整体结构
 */
async function analyzeKnowledgeStructure() {
  console.log('开始分析知识库结构...');
  
  try {
    // 获取总数
    const totalCount = await db.collection('knowledge_base').count();
    console.log('总记录数:', totalCount.total);
    
    // 分批获取所有记录进行分析
    const batchSize = 20;
    const totalRecords = totalCount.total;
    const batches = Math.ceil(totalRecords / batchSize);
    
    const categoryStats = {};
    const authorStats = {};
    let totalSize = 0;
    let processedCount = 0;
    
    for (let i = 0; i < batches; i++) {
      const skip = i * batchSize;
      console.log(`处理批次 ${i + 1}/${batches}, skip: ${skip}`);
      
      const batch = await db.collection('knowledge_base')
        .field({
          title: true,
          category: true,
          author: true,
          content: true
        })
        .skip(skip)
        .limit(batchSize)
        .get();
      
      batch.data.forEach(record => {
        const category = record.category || '未分类';
        const author = record.author || '未知作者';
        const contentSize = (record.content || '').length;
        
        // 分类统计
        if (!categoryStats[category]) {
          categoryStats[category] = { count: 0, totalSize: 0, titles: [] };
        }
        categoryStats[category].count++;
        categoryStats[category].totalSize += contentSize;
        if (categoryStats[category].titles.length < 3) {
          categoryStats[category].titles.push(record.title);
        }
        
        // 作者统计
        if (!authorStats[author]) {
          authorStats[author] = { count: 0, works: [] };
        }
        authorStats[author].count++;
        if (authorStats[author].works.length < 3) {
          authorStats[author].works.push(record.title);
        }
        
        totalSize += contentSize;
        processedCount++;
      });
    }
    
    // 格式化统计结果
    const categoryDistribution = Object.keys(categoryStats).map(key => ({
      _id: key,
      count: categoryStats[key].count,
      avgSize: Math.round(categoryStats[key].totalSize / categoryStats[key].count),
      titles: categoryStats[key].titles
    }));
    
    const authorDistribution = Object.keys(authorStats).map(key => ({
      _id: key,
      count: authorStats[key].count,
      works: authorStats[key].works
    }));
    
    const sizeStatistics = {
      totalSize: totalSize,
      avgSize: Math.round(totalSize / processedCount),
      processedCount: processedCount
    };
    
    console.log('结构分析完成');
    
    return {
      success: true,
      analysis: {
        total_records: totalCount.total,
        category_distribution: categoryDistribution,
        author_distribution: authorDistribution,
        size_statistics: sizeStatistics,
        analysis_timestamp: new Date().toISOString()
      }
    };
    
  } catch (error) {
    console.error('结构分析失败:', error);
    throw error;
  }
}

/**
 * 提取关键词
 */
async function extractKeywords() {
  console.log('开始提取关键词...');
  
  try {
    // 获取前50条记录进行关键词分析
    const records = await db.collection('knowledge_base')
      .field({
        title: true,
        category: true,
        content: true
      })
      .limit(50)
      .get();
    
    const keywordFreq = {};
    const categoryKeywords = {};
    
    records.data.forEach(record => {
      const category = record.category || '未分类';
      const fullText = (record.title || '') + ' ' + (record.content || '').substring(0, 1000);
      
      if (!categoryKeywords[category]) {
        categoryKeywords[category] = { titles: [], keywords: [] };
      }
      categoryKeywords[category].titles.push(record.title);
      
      // 提取中文词汇
      const words = fullText.match(/[\u4e00-\u9fa5]{2,6}/g) || [];
      words.forEach(word => {
        if (word.length >= 2 && word.length <= 6) {
          keywordFreq[word] = (keywordFreq[word] || 0) + 1;
          if (keywordFreq[word] >= 2) {
            if (!categoryKeywords[category].keywords.includes(word)) {
              categoryKeywords[category].keywords.push(word);
            }
          }
        }
      });
    });
    
    // 排序常见术语
    const commonTerms = Object.entries(keywordFreq)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 30)
      .map(([term, count]) => ({ term, count }));
    
    return {
      success: true,
      keywords: {
        by_category: categoryKeywords,
        common_terms: commonTerms,
        total_analyzed: records.data.length
      }
    };
    
  } catch (error) {
    console.error('关键词提取失败:', error);
    throw error;
  }
}

/**
 * 内容分类分析
 */
async function categorizeContent() {
  console.log('开始内容分类分析...');
  
  try {
    const records = await db.collection('knowledge_base')
      .field({
        title: true,
        category: true,
        content: true
      })
      .limit(30)
      .get();
    
    const contentAnalysis = {
      categories: {},
      length_distribution: { short: 0, medium: 0, long: 0 },
      content_patterns: {}
    };
    
    records.data.forEach(record => {
      const category = record.category || '未分类';
      const content = record.content || '';
      const length = content.length;
      
      if (!contentAnalysis.categories[category]) {
        contentAnalysis.categories[category] = {
          count: 0,
          total_length: 0,
          sample_titles: []
        };
      }
      
      contentAnalysis.categories[category].count++;
      contentAnalysis.categories[category].total_length += length;
      if (contentAnalysis.categories[category].sample_titles.length < 3) {
        contentAnalysis.categories[category].sample_titles.push(record.title);
      }
      
      // 长度分布
      if (length < 10000) {
        contentAnalysis.length_distribution.short++;
      } else if (length < 100000) {
        contentAnalysis.length_distribution.medium++;
      } else {
        contentAnalysis.length_distribution.long++;
      }
      
      // 内容模式
      const patterns = ['卷', '章', '篇', '节', '法', '诀'];
      patterns.forEach(pattern => {
        if (content.includes(pattern)) {
          contentAnalysis.content_patterns[pattern] = (contentAnalysis.content_patterns[pattern] || 0) + 1;
        }
      });
    });
    
    // 计算平均长度
    Object.keys(contentAnalysis.categories).forEach(category => {
      const cat = contentAnalysis.categories[category];
      cat.avg_length = Math.round(cat.total_length / cat.count);
    });
    
    return {
      success: true,
      content_analysis: {
        ...contentAnalysis,
        sample_size: records.data.length
      }
    };
    
  } catch (error) {
    console.error('内容分类失败:', error);
    throw error;
  }
}

/**
 * 术语分析
 */
async function analyzeTerminology() {
  console.log('开始术语分析...');
  
  const terminologyDict = {
    '梅花易数': ['体卦', '用卦', '互卦', '变卦', '先天数', '后天数'],
    '六爻': ['世爻', '应爻', '用神', '原神', '忌神', '六亲'],
    '八字': ['日主', '用神', '十神', '格局', '大运', '流年'],
    '紫微': ['命宫', '身宫', '主星', '四化', '三方四正', '大限']
  };
  
  try {
    const records = await db.collection('knowledge_base')
      .field({
        title: true,
        category: true,
        content: true
      })
      .limit(50)
      .get();
    
    const terminologyAnalysis = {
      by_category: {},
      term_frequency: {},
      coverage_analysis: {}
    };
    
    // 初始化分析结构
    Object.keys(terminologyDict).forEach(category => {
      terminologyAnalysis.by_category[category] = {};
      terminologyAnalysis.coverage_analysis[category] = {
        total_terms: terminologyDict[category].length,
        found_terms: 0,
        coverage_rate: 0
      };
      
      terminologyDict[category].forEach(term => {
        terminologyAnalysis.by_category[category][term] = 0;
        terminologyAnalysis.term_frequency[term] = 0;
      });
    });
    
    // 统计术语频率
    records.data.forEach(record => {
      const fullText = (record.title || '') + ' ' + (record.content || '');
      
      Object.keys(terminologyDict).forEach(category => {
        terminologyDict[category].forEach(term => {
          const regex = new RegExp(term, 'g');
          const matches = (fullText.match(regex) || []).length;
          if (matches > 0) {
            terminologyAnalysis.by_category[category][term] += matches;
            terminologyAnalysis.term_frequency[term] += matches;
            if (terminologyAnalysis.coverage_analysis[category].found_terms === 0) {
              terminologyAnalysis.coverage_analysis[category].found_terms++;
            }
          }
        });
      });
    });
    
    // 计算覆盖率
    Object.keys(terminologyAnalysis.coverage_analysis).forEach(category => {
      const analysis = terminologyAnalysis.coverage_analysis[category];
      analysis.coverage_rate = Math.round((analysis.found_terms / analysis.total_terms) * 100);
    });
    
    return {
      success: true,
      terminology: {
        ...terminologyAnalysis,
        sample_size: records.data.length,
        dictionary_size: Object.values(terminologyDict).flat().length
      }
    };
    
  } catch (error) {
    console.error('术语分析失败:', error);
    throw error;
  }
}

/**
 * 生成总结报告
 */
async function generateSummary() {
  console.log('生成总结报告...');
  
  try {
    const [structure, keywords, content, terminology] = await Promise.all([
      analyzeKnowledgeStructure(),
      extractKeywords(),
      categorizeContent(),
      analyzeTerminology()
    ]);
    
    const summary = {
      overview: {
        total_records: structure.analysis.total_records,
        categories: structure.analysis.category_distribution.length,
        authors: structure.analysis.author_distribution.length,
        total_size_mb: Math.round(structure.analysis.size_statistics.totalSize / (1024 * 1024))
      },
      content_quality: {
        avg_file_size_kb: Math.round(structure.analysis.size_statistics.avgSize / 1024),
        terminology_coverage: terminology.terminology.coverage_analysis,
        common_terms_count: keywords.keywords.common_terms.length
      },
      recommendations: [
        '建议对内容进行进一步的结构化处理',
        '可以建立更完善的术语索引系统',
        '需要统一不同分类的内容格式',
        '建议增加内容的语义标注'
      ],
      generated_at: new Date().toISOString()
    };
    
    return {
      success: true,
      summary,
      detailed_analysis: {
        structure: structure.analysis,
        keywords: keywords.keywords,
        content: content.content_analysis,
        terminology: terminology.terminology
      }
    };
    
  } catch (error) {
    console.error('总结报告生成失败:', error);
    throw error;
  }
}
